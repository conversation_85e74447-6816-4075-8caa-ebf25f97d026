{"event": "session_start", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "timestamp": "2025-06-18T16:08:39.898646", "message": "New API session started"}
{"event": "request_start", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "05ea4fe3-9d31-4e4a-b364-f755997b209b", "endpoint": "/", "timestamp": "2025-06-18T16:09:07.763262", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "05ea4fe3-9d31-4e4a-b364-f755997b209b", "endpoint": "/", "timestamp": "2025-06-18T16:09:07.764259", "total_time_seconds": 0.0009970664978027344, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "45bab7f5-12c2-481d-8a8e-271e4b966896", "endpoint": "/", "timestamp": "2025-06-18T16:09:18.532064", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "45bab7f5-12c2-481d-8a8e-271e4b966896", "endpoint": "/", "timestamp": "2025-06-18T16:09:18.533063", "total_time_seconds": 0.0009984970092773438, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.598320", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.630972", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.630972", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.630972", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.631972", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:09:20.631972", "file_processing_time": 0.027124404907226562, "message": "Custom metric: file_processing_time=0.027124404907226562"}
{"event": "request_complete", "session_id": "62cec178-d3cd-4d8d-989f-3cba3540d4fd", "request_id": "1d17af08-3806-4422-bcb0-6800dcc40be4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:10:06.499988", "total_time_seconds": 45.90166783332825, "status_code": 200, "message": "Request completed in 45.9017s with status 200"}
