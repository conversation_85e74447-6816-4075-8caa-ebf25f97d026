# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON><PERSON><PERSON><PERSON>.pdf
# Context: resume
# Extraction Method: pdf_text
# Timestamp: 2025-06-09 14:40:09
# Text Length: 3176 characters
# ================================================

<PERSON><PERSON><PERSON><PERSON><PERSON>
LinkedIn Github Leetcode
PROFESSIONAL SUMMARY
Web developer with a strong foundation in full-stack technologies (HTML, CSS, JavaScript, React, Node.js, Express, MySQL).
Skilled in building dynamic, responsive, user-focused interfaces and creating backend APIs, with a passion for solving
problems. Focused on improving technical skills, particularly in Data Structures and Algorithms (DSA), and eager to expand
full-stack development capabilities.
EDUCATION
Bachelor of Technology (AI ML) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 7.7/10.0
Intermediate (Class XII) 2020 - 2022
St Xavier's High School Grade: 70.0%
Matriculation (Class X) 2017 - 2020
St Xavier's High School Grade: 91.0%
PROJECTS
Voting App , ( Github ) ( Demo ) October 2024
Tech Stack: Node.js ,Express.js ,MongoDB ,JSON Web Tokens (JWT) .
Description: Developed a backend voting system with RESTful APIs for candidate management, user authentication and
voting, integrating JWT for secure access and ensuring ecient database interactions.
Features:
{Implemented secure user authentication with login and registration functionalities.
{Developed CRUD operations for managing candidates, ensuring ecient data handling.
{Built robust API endpoints for secure vote submission and retrieval, ensuring seamless functionality with data
validation.
Algo Visualiser , ( Github ) ( Demo ) September 2024
Tech Stack : React.js andCSS.
Description :Built an interactive platform to visualize sorting algorithms like bubble sort, selection sort, and insertion
sort, allowing real-time user interaction and understanding of algorithm behavior.
Features :
{Enabled custom input array creation to test various data sets, enhancing algorithm testing 
exibility.
{Implemented pause and resume controls for user-driven visualization, improving interaction and learning.
{Incorporated dynamic color changes and animation to clearly indicate the stages of the sorting process.
{Developed adjustable algorithm speed to provide a customized learning experience based on user preference.
Soundly , ( Github ) ( Demo ) July 2024
Tech Stack : React.js andTailwind CSS .
Description :Developed a dynamic web-based music streaming application, enabling users to access a vast library of songs
and albums, and providing an intuitive and seamless experience for discovering, searching, and listening to music.
Features :
{Integrated external APIs to fetch and display music content, providing seamless browsing, searching, and streaming
experiences, while enhancing content discovery through a diverse catalog of songs and albums.
{Implemented a secure user authentication system to streamline registration and login processes, enhancing data
protection and user access control.
SKILLS
Computer Languages: Java, Python, JavaScript, CSS, HTML
Software Packages: React, Express JS, Prisma ORM, MySQL, Tailwind, NodeJS
Additional Courses: Data Structure
EXTRA-CURRICULAR ACTIVITIES
Achieved Top 5 Rank in the District-Level Inter-School Story Writing Competition.
Currently acting as Captain of the College Tennis Team.
Active Member of the Web Development Club and Debate Club.
