# Extracted Text Debug File
# Source File: Resume-AKASH G.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 14:21:09
# Text Length: 2507 characters
# ================================================

Akash G
LinkedIn Github Leetcode
PROFESSIONAL SUMMARY
Aspiring software developer with a strong foundation in full-stack development and a passion for solving real-world problems.
Procient in HTML, CSS, JavaScript, React.js, Node.js, Express.js, MySQL, and SQL, with a solid background in Java, OOP
principles, and DSA. Accomplished problem-solver with over 300 challenges completed on LeetCode.
EDUCATION
Bachelor of Technology (Articial intelligence) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 8.3/10.0
Intermediate (Class XII) 2022 - 2023
Kendriya Vidyalaya Grade: 68.0%
Matriculation (Class X) 2020 - 2021
Kendriya Vidyalaya Grade: 67.0%
PROJECTS
Fashion Cart E-Commerce , ( Github ) ( Demo ) September 2024
Tech Stack: React.js, React.js, Node.js, Express.js, MySQL, Prisma ORM, Tailwind CSS, Stripe, Vercel
Description: Built a full-stack e-commerce application with React for frontend, Node.js and Express for backend, Prisma
and MySQL for database management, and Stripe for secure payment processing.
Key Features: User-friendly product search, category-based browsing, secure Sign In/Sign Out ,Add-to-cart and checkout
functionality with integrated Stripe payment processing, Responsive design with Tailwind CSS
Chat Application - Talk Stream , ( Github ) ( Demo ) September 2024
Tech Stack : React, Zustand, Vite, Firebase Authentication, Firestore Database, HTML, CSS, Vercel
Description :Developed a dynamic real-time chat application with Firebase for backend integration, providing smooth
communication, user authentication, and robust state management.
Key Features: Real-time messaging with image sharing, user search, and login/logout functionality.
User blocking capabilities and chat list management for personalized interactions.
Apple Music Clone , ( Github ) ( Demo ) April 2024
Tech Stack: HTML, Tailwind CSS, Reat.js, Netlify
Description: Developed a responsive Apple website clone, replicating design aesthetics, interactive elements, and layout to
provide an authentic user experience.
Key Features: Homepage, product pages, and navigation bar designed to emulate Apple's clean and minimalistic interface.
SKILLS
Computer Languages: SQL, Java, Python, JavaScript, CSS, HTML
Software Packages: React, Express JS, Prisma ORM, MySQL, Tailwind, NodeJS
Additional Courses: Data Structure
Soft Skills: Communication Skills, Teamwork
EXTRA-CURRICULAR ACTIVITIES
Solved over 300 problems on Leetcode.
Secured 3rd position in the college marathon.
