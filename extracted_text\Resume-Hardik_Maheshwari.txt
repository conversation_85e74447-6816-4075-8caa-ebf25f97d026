# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 14:24:37
# Text Length: 2452 characters
# ================================================

<PERSON><PERSON>
LinkedIn Github Leetcode
PROFESSIONAL SUMMARY
Aspiring Algo Trader & Frontend Developer with real startup internships under my belt. Built production-level tools, live
trading apps, and crushed 75+ LeetCode problems.
EDUCATION
Bachelor of Technology (Articial intelligence) 2024 - 2028
Newton School Of Technology , Rishihood University Grade: 7.0/10.0
Matriculation (Class X) 2024 - 2024
Global Public School Grade: 88.0%
Intermediate (Class XII) 2021 - 2022
Global Public School Grade: 80.0%
INTERNSHIPS
Mobile Application Development June 2022 - August 2022
11Mantras Remote
Completed 2-month internship at11mantras , an astrologer-based startup.
Developed iOS
Android apps , gaining hands-on mobile development experience.
Contributed to the success of the project through key development eorts.
PROJECTS
Live Trading , ( Github ) ( Demo ) April 2025
Tech stack: React, FastAPI, yFinance, Plotly.js, Styled-components
description:
{Market movers section with live indices, top gainers/losers
{Interactive candlestick charts
{Backend caching for fast performance
Key Features:
Market Movers Section: Displays live updates of Nifty, Sensex, and other indices, along with top gainers and losers.
Interactive Candlestick Charts: Custom-built charting using Plotly.js with support for indicators, zoom, pan, and
crosshair tracking.
Backend Caching: Implemented smart caching in FastAPI for high-speed data retrieval and reduced API calls.
Responsive UI: Built with styled-components for seamless experience across devices.
.
Best SMA combo , ( Github ) February 2025
 Tech Stack :- Python (AngleOne API, pandas,numpy, webSockets, Plotly) , Jupiter Notebook
Description :- SMA Combo is a stock analysis tool for testing short
long SMA combinations across stocks and timeframes. It oers backtesting, performance metrics, and
visualization , helping traders optimize strategies eciently
Features :- Custom SMA selection, multi-timeframe backtesting, performance metrics, chart visualization, and fast data
caching.
SKILLS
Computer Languages: Python, JavaScript
Software Packages: Chart.js, React
Soft Skills: Leadership
Others: Analytics
EXTRA-CURRICULAR ACTIVITIES
Awarded `Best Boy of the Year' for leadership
achievements.
Achieved 1200+ rating on Chess.com (Blitz
Rapid formats).
Built Rs.6.2L+ stock portfolio using self-developed analysis tools.
Interned at 11mantras; developed production-level Android
iOS apps.
