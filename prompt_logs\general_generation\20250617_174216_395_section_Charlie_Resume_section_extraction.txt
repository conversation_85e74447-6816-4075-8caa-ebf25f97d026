================================================================================
LLM CALL LOG - 2025-06-17 17:42:16
================================================================================

[CALL INFORMATION]
Endpoint: /section
Context: Charlie Resume.pdf_languages
Call Type: section_extraction
Model: gemma3:4b
Timestamp: 2025-06-17T17:42:16.395361
Metadata: {
  "timeout_seconds": 45,
  "max_tokens": 800,
  "processing_time": 1.9380555152893066,
  "has_image": false,
  "prompt_length": 17964,
  "response_length": 27,
  "eval_count": 9,
  "prompt_eval_count": 3725,
  "model_total_duration": 1911200200
}

[PROMPT]
Length: 17964 characters
----------------------------------------
Look for a section in the resume with headings like "LANGUAGES", "LANGUAGE SKILLS", "LINGUISTIC ABILITIES", or "SPOKEN LANGUAGES".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
Charlie Wang  
������ 551-280-6504  ����� <EMAIL>  
 
 
PROFESSIONAL SUMMARY                                                 
● 7 years of experience in developing and maintaining strong backend applications using 
Java and Spring Boot , delivering scalable, high- performance applications that handle a 
large quantity of daily transactions, focusing on optimizing performance, reliability, and production- quality software to meet enterprise -level demands.  
● Proficient in leveraging the full suite of Spring Framework components, including Spring MVC , Spring Data JPA , and Spring Security , to build secure, maintainable, 
and highly modular backend services, ensuring seamless integration with other systems and applications while adhering to modern software development best practices. 
● Skilled in designing, Object -Oriented Programming (OOP)  and implementing secure 
RESTful APIs using Spring Boot , with a strong focus on authentication and 
authorization mechanisms using OAuth2 and JWT , ensuring that sensitive data and 
operations are protected across distributed systems. 
● Experienced in managing data persistence with Hibernate and JPA, working extensively 
with relational databases such as Oracle, MySQL , and PostgreSQL , as well as NoSQL 
databases like MongoDB and Cassandra DB , to ensure data integrity, high availability, 
and optimized query performance in large -scale applications.  
● Proficient in deploying and managing scalable backend services on cloud platforms such 
as AWS , utilizing Docker for containerization and Kubernetes for orchestration, 
ensuring seamless scalability, fault tolerance, and high availability across cloud environments. 
● Skilled in implementing real- time data processing and event -driven architectures using 
Apache Kafka and RabbitMQ , enabling high- throughput, reliable messaging systems 
that handle massive data streams and support distributed microservices architectures.  
● Experienced in working with Big Data technologies such as Apache Spark and 
PySpark for processing and analyzing large datasets, allowing for the efficient handling 
of complex data workflows and enabling real -time data analytics in resource- intensive 
environments. 
● Adept at designing and implementing microservices architectures , leveraging best 
practices and tools like Spring Boot , Docker, and Kubernetes , supported by CI/CD 
pipelines using Jenkins , to build modular, independently deployable services that 
streamline development and reduce time -to-market for new features.  
● Proficient in building automation and continuous integration using tools like Maven , 
ensuring efficient and reliable build processes, automated testing, and continuous 
delivery pipelines that accelerate deployment cycles and reduce manual intervention.  
● Skilled in comprehensive testing frameworks like JUnit and Mockito , developing 
extensive unit tests  and integration tests to ensure backend services maintain high levels 
of reliability, stability, and performance, while achieving high code coverage and ensuring early detection of potential issues. 
● Experienced with creational design patterns such as Builder Patterns  and Factory 
Patterns , and also Singleton , which help to create objects efficiently.  
● Demonstrated expertise in architecting and optimizing backend infrastructure using Java and Spring Boot , with a focus on optimizing resource utilization, reducing system 
bottlenecks, and ensuring that the platform can scale to meet growing demand while maintaining high availability and fault tolerance.  
● Skilled in implementing robust security features, including encryption protocols, secure API gateways, and role -based access control (RBAC), using Spring Security , JWT , and 
OAuth2 , ensuring that sensitive data and critical system operations are secure and 
comply with industry standards for data protection and privacy.  
● Proficient in creating automated deployment pipelines and streamlining DevOps processes using Jenkins , Docker, and Kubernetes , enabling faster feature rollouts, 
reducing downtime, and improving system reliability through continuous integration, continuous delivery ( CI/CD), and automated testing. 
● Used Github to conduct modern distributed source code management.  
● Experience with project management tools by using Agile, Scrum, and being able to 
troubleshoot problems on various projects.  
● Strong problem -solving, organizing, communication and planning skills, with ability to 
work in a team environment. 
TECHNICAL SKILLS                                                                       
● Programming Languages : Java, JavaScript, TypeScript, Python, C++, SQL  
● Technologies: GraphQL, Microservices, Kafka, Unit Testing, CI/CD, HTML, CSS,  React.js, 
Redux, Express.js, Node.js, Restful APIs, RDBMS (Oracle, MYSQL, PostgreSQL), NoSQL 
(MongoDB, Cassandra DB), Spark  
● Developer Tools: Maven,  Git, Postman, Webpack, Babel, NPM, Docker, Kubernetes  
● Frameworks: Spring Boot, Spring MVC, Spring Data JPA, Hibernate, React, Mockito 
● OS: Windows, MacOS, Linux  
● Cloud Platforms: AWS, Heroku  
● Database: MySQL, MongoDB, PostgreSQL, Redis  
● Project Management: Scrum, Jira, Agile  
● Platforms and IDE: Visual Studio Code, IntelliJ IDEA, PyCharm, Eclipse  
     
PROJECT EXPERIENCE      
 
Wells Fargo        
Java Developer                    Aug 2023 -  Present  
Project description :  
Wells Fargo is a leading financial institution providing a wide array of banking and 
financial services. As part of the Credit Monitoring project at Wells Fargo, I am responsible 
for the development of the backend of the Monitoring system. My primary focus  is on 
monitoring the transactions used by Wells Fargo credit cards and migrating the real -time 
transaction status details from backend to frontend, ensuring seamless integration and 
enhanced user experience. This project plays a significant role in optimi zing risk mitigation 
strategies and improving the stability of the bank’s risk management processes. Environment : 
 
Java, Spring Boot, Spring Security, JPA, Hibernate, Apache Kafka, Maven, Jenkins, 
MYSQL, RESTFUL API, Unit Test, JUnit, Mockito, React, Material UI, AWS, Agile, 
Scrum, Git.  
  Responsibilities : 
● Utilized Java within the Spring Boot ecosystem in IntelliJ IDEA  to develop and 
enhance the backend services. 
● Utilized JavaScript within the React library to build interactive and dynamic user 
interface and  using Material UI  for rendering beautiful components.  
● Design and develop complex RESTful APIs  using Spring Boot , enabling 
lightweight and efficient communication between frontend and backend.  
● Integrated authentication and authorization mechanisms such as OAuth2 and JWT 
to ensure that only users with permission can access sensitive risk monitoring 
processes.  
● Architected highly scalable microservices utilizing Spring Boot  and Apache 
Kafka , managing millions of transactions daily with minimal latency.  
● Developed a robust event -driven architecture using Kafka , enabling stable and 
scalable message processing across the system, ensuring that critical financial events such as trade transactions and risk alerts were processed in time.  
● Optimized  complex SQL queries in MySQL database, storing and managing user 
information and transaction details through operations ( CRUD).  
● Used JavaScript as main programming language in frontend design, and used 
React Library to build modularized and reusable components for the frontend to 
display dynamic information such as transaction activities, risk assessment 
variations. 
● Utilized Hibernate in Spring JPA  to connect with MySQL database to enhance 
the data query efficiency and convenience.  
● Integrated real -time WebSocket  communication channels within the platform 
provide  instant updates to risk assessment data, which largely improved the 
system’s responsiveness.  
● Established a comprehensive CI/CD pipeline using Jenkins , automating the build, 
test, and deployment process, which reduced deployment times and minimized 
downtime during updates. 
● Followed Agile (Scrum) methodology for project management, which effectively 
improves the problem -solving speed and efficiency in each sprint.  
● Employed Unit Testing tools such as JUnit and Mockitos  for bug tracking and 
progress monitoring in Spring Boot applications. 
● Worked extensively with AWS  Cloud services, including EC2 , S3, and IAM  for 
deployment and cloud storage.  
● Utilized Git for version control and Maven for building automation  and 
dependencies management .     
                                     
Target               
Java Developer                                                                              Dec 2021 -  Feb 2023 
Project description :  
Target is a well -known e -commerce and retail platform providing a diverse range of 
products. The project aimed at designing a mainstream shopping app which enables 
customers to know the latest products and information about our products clearly and 
thoroughly. I'm responsible for building a web page which displays the articles, news and 
videos of our products. And each article  or news contains a YouTube  video or video link 
to introduce the details of the product to customers, which empowers users and customers to quickly and comprehensively understand the ingredients and benefits of our current 
products or upcoming products.  
Environment :  
Java, Spring Boot, Spring Security, Spring JPA, WebSocket , Maven, RabbitMQ, JWT, 
Hibernate, Cassandra DB, RESTFUL API, JUnit, React, Mockito, AWS, Git. 
   Responsibilities : 
● Utilize Java in Spring Boot  platform to build robust backend servers providing real time 
access to products, users information and Sales status. 
● Kept up- to-date on the best  practices for Spring Boot frameworks and techniques such as 
Spring Security , Spring Data JPA . 
● Integrated WebSocket  communication channels to provide instant updates on online 
order’s delivery statuses.  
● Used Maven for build automation, standardizing the project build process, managing 
dependencies, and ensuring consistent and reliable deployment of backend services across 
multiple environments.  
● Utilized RabbitMQ  to manage large- scale message processing in an event -driven 
architecture, allowing the platform to handle thousands of online shopping orders and 
delivery events per second. 
● Implemented secure authentication and authorization using Spring Security  and JWT , 
ensuring all access to sensitive data including logistics data and user information were protected from unauthorized access, while managing user roles and permissions, securing API endpoints, and maintaining the integrity and confidentiality of all platf orm transactions.  
● Used Spring Data JPA (Hibernate)  in application to retrieve and store data (products, 
cart information, user information, delivery statuses, etc) from Cassandra Database  and 
to perform RESTFUL  services with high availability, scalability and fault tolerance.  
● Collaborated with the DevOps  team to streamline CI/CD pipelines using Jenkins, 
automating the build, test, and deployment processes to ensure rapid and reliable feature releases.  
● Worked closely with the frontend team to integrate backend services with a React -based  
frontend, ensuring smooth real -time logistics updates, reducing latency in data delivery and 
displaying a comfortable user interface.  
● Implemented application testability and diagnostics and fixing bugs with the help of  JUnit 
and Mockito . 
● Designed the backend server with RESTful API  for frontend, implemented the requests to 
fetch or manipulate data and user Authentication for login function.  
● Repeatedly worked on AWS  cloud platform and its features which include  EC2 , S3 and 
IAM . 
● Used Git  for code version control. 
 
L'Oréal              
Role:  Java Developer                                                                 Feb 2020 -  Aug 2021 
Project description :  
As a member of the development team at Loreal, I contributed to the development of an 
online shopping website designed to simplify buyers’ shopping process and decrease the difficulty for sellers to sell their goods. This website allows sellers to create o r update their 
products in a simple form and allows customers to pay in multiple ways. I am responsible for designing the product attributes, which ensures the product's information is well stored 
in the database and easy to retrieve and mapping. And it al lows the frontend team to easily 
get the data and display the product on the webpage. 
Environment :  
Java, Spring Boot, Spring Security, Hibernate, JPA, Maven, Apache Kafka, REST APIs, 
JUnit, Mockito, Agile, JWT, MySQL, Jenkins, AWS, Git.  
Responsibilities : 
● Implement the backend components for this shopping platform, utilizing Spring Boot  
and Apache Kafka  to build a scalable, fault -tolerant application.   
• Used Java as the main programming language to design the user information and product 
data models.  
• Leveraged the power of Spring Boot to introduce new features and create reusable 
components, improving code maintainability. 
• Developed the backend RESTful APIs  via Spring Boot  and Spring JPA , resulting in a 
more organized and efficient data flow.  
• Used Apache Kafka  for asynchronous operations for data retrieving and monitoring. 
• Implemented Functional Testing and Unit Testing using JUnit and Mockito to maintain code quality and reliability. 
• Collaborated within an Agile  development environment, promoting efficient 
development practices.  
• Conducted code reviews  and actively participated in discussions with peers to refine 
development solutions. 
• Implemented robust security measures using Spring Security  and JWT , ensuring that all 
API endpoints were protected, safeguarding the application from potential cyber -attacks . 
• Developed the backend server using Spring Boot , creating REST APIs  to access data 
from the database.  
• Managed data in MySQL  database through Hibernate , enabling data operations such as 
create, read, update, and delete.  
• Established and maintained automated CI/CD pipelines using Jenkins , automating the 
build, test, and deployment processes, reducing manual intervention.  
• Successfully deployed the content cloud platform on Amazon Web Services (AWS)  
using EC2 instances.  
• Used Git for source control. 
 
IGN Entertainment                 
Role:  Full stack Developer                                               Sep 2018 -  Feb 2020 
Project description :  
The project aimed to build an online evaluation system related to films, TV series, books, 
and animations, etc. The website serves as a film and television works search engine and 
opinion exchange area. I contributed to the development of the  backend. The app allows 
users to click an item such as a film, a TV series or a book, and then lead them to the details 
page and present the picture, cast list, comments about the item. The rate, review and 
comments about the item are listed below the cas t list and it allows users to share and 
exchange their opinions about the item here, which assists users to get an intuitive overview of the item.  
Environment :  
Java, JavaScript, Spring MVC, Spring Boot, Hibernate, React, HTML5, CSS3, Bootstrap, 
MySQL, JSON, TCS, SCSS, VSCode, Agile, Scrum. 
    Responsibilities : 
● Designed the backend using Java8 to build user information and film models. 
● Designed, developed the web page using HTML5 , CSS3 , Bootstrap , JavaScript, and 
React  that meet accessibility and web browser standards for websites.  
● Setting up React  framework for UI development. Developed HTML  views with HTML5 , 
CSS3 , JSON  and JavaScript . 
● Setting up Spring Boot  framework for backend development including the routes, APIs 
and data models.  
● Effectively involved in analysis, design, and Development of the application using Visual 
Studio Code.  
● Used Java for page functionality, pop up screens and drop- down menus on the webpage.  
● Used MySQL to store the data information into the database, and deployed Hibernate in 
Spring JPA  for data management.  
● Worked on an Agile (Scrum) Development Team  to deliver regular updates to the 
business team and project managers.  
● Worked on web and client -side server application development and design using Object 
Oriented Programming (OOP) technologies.  
● Worked on cloud platforms like Tencent Cloud Service.  
● Participated in client interactions and business partner discussions, worked within established procedures to develop, test, implement, and develop implementation plans, and assisted in deployment  
● Used JSON for storing and exchanging information about films, books, TV series and 
animations between browsers and servers.  
  
EDUCATION   
 
Stevens Institute of technology                                              Hoboken, New  Jersey, USA  
Master of Science in  Computer Science.                                                09, 2021 -  05, 2023  
  


CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content
----------------------------------------

[RESPONSE]
Length: 27 characters
----------------------------------------
LANGUAGES
English
NOT_FOUND
----------------------------------------

================================================================================