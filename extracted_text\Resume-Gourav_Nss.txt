# Extracted Text Debug File
# Source File: Resume-Gourav Nss.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:48:55
# Text Length: 3345 characters
# ================================================

Gourav Nss
LinkedIn Github CodeChef Codeforces Leetcode Personal Portfolio
PROFESSIONAL SUMMARY
I am a B.Tech CSE (AI & ML) student at ADYPU, passionate about coding, problem solving, and tech events.
I actively participate in Codeforces contests, hackathons, and web development projects and open-source contributions.I have
also engaged in ICPC's Go For Gold camp and tech fests.
My goal is to excel in software development, leveraging AI and web technologies to build impactful solutions
EDUCATION
Bachelor of Technology (Computer Science) 2024 - 2028
Newton School of Technology Grade: 7.27/10.0
Intermediate (Class XII) 2022 - 2024
Sri Chaitanya Grade: 80.0%
Matriculation (Class X) 2021 - 2022
FIITJEE International School Grade: 76.0%
PROJECTS
Balloon Adventure , ( Github ) ( Demo ) March 2025
This is a simple game built using HTML, CSS, and JavaScript . The project serves as a foundation for learning game
mechanics event handling and rendering graphics in a web environment.
Features
Interactive gameplay
Player controls
Collision detection
Score tracking
Portfolio , ( Github ) ( Demo ) March 2025
Developed a personal website to showcase my projects, coding skills, and achievements. Built using HTML, CSS, and
JavaScript.
Sem1 Project , ( Github ) ( Demo ) December 2024
This repository contains the Semester 1 Project for the System and Web Essentials course, focusing on:
Core Skills: Showcasing web development fundamentals using HTML and CSS.
Outcome: A structured and styled web project demonstrating prociency in front-end development.
Impact: Strengthened problem-solving abilities and hands-on experience in building responsive web pages.
CERTIFICATIONS
Generative AI for Everyone , DeepLearning.AI ( Link ) March 2025
Explored deep learning techniques for generative AI including neural networks and model training. Learned to create
AI-driven content and applications.
AI For Everyone , DeepLearning.AI ( Link ) February 2025
Gained a foundational understanding of AI its applications, and ethical considerations. Learned key concepts like machine
learning and data-driven decision-making
SKILLS
Computer Languages: SQL, JavaScript, CSS, HTML, Python
Software Packages: React
Soft Skills: Communication Skills, Presentation Skills, Openness, Responsibility, Critical Thinking, Teamwork, Creativity,
Decision-making, Time management, Team Building, Leadership, Verbal/nonverbal communication
Others: Microsoft Oce
EXTRA-CURRICULAR ACTIVITIES
Competitive programming: Active participation in CodeForces contests (Max Rating 997),CodeChef Contest(Max Rating
1363), as well as other programming challenges.
Secured 1st Place at 100 Days of NST ADYPU(Coding Relay).
Startup Pitch: 2nd place winner at Sharksphere (E-CELL Club Event).
Public Speaking: Took part in extempore competition of Orators Club.
I play Football, Cricket, Badminton, and learned to Swim.
Position of Responsibility:
Developer Member {NST Dev Club Newton School of Technology
Hackathon Organizer: Hosted a hackathon as a part of tekron fest 2025.
Go For Gold Volunteer: Assisted in organizing the ICPC Go For Gold camp for Newton School.
Orators Club Membership: Participated actively in public speaking events and discussions by being a member of the
Orators Club
Badminton Coordinator: Coordinated, organized badminton tournaments.
