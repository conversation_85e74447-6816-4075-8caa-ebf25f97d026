{"event": "session_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "timestamp": "2025-06-18T16:11:25.470466", "message": "New API session started"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "2e609879-7719-41a7-a3d7-41a94951650a", "endpoint": "/", "timestamp": "2025-06-18T16:11:54.884744", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "2e609879-7719-41a7-a3d7-41a94951650a", "endpoint": "/", "timestamp": "2025-06-18T16:11:54.885743", "total_time_seconds": 0.00099945068359375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "7621c703-c421-4de5-8d60-b9bfd792ef98", "endpoint": "/", "timestamp": "2025-06-18T16:12:06.815617", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "7621c703-c421-4de5-8d60-b9bfd792ef98", "endpoint": "/", "timestamp": "2025-06-18T16:12:06.815617", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.858628", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.876943", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.876943", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.876943", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.876943", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:08.876943", "file_processing_time": 0.015661001205444336, "message": "Custom metric: file_processing_time=0.015661001205444336"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "5bd3bb3f-6ae4-4a63-be77-4423cab3efaa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:12:53.752113", "total_time_seconds": 44.89348554611206, "status_code": 200, "message": "Request completed in 44.8935s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "ebbfe5fe-9b87-4a1f-8924-bf5518e392ca", "endpoint": "/", "timestamp": "2025-06-18T16:13:45.990273", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "ebbfe5fe-9b87-4a1f-8924-bf5518e392ca", "endpoint": "/", "timestamp": "2025-06-18T16:13:45.991272", "total_time_seconds": 0.0009996891021728516, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.048944", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.069208", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.069208", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.069208", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.070209", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:13:48.070209", "file_processing_time": 0.018262386322021484, "message": "Custom metric: file_processing_time=0.018262386322021484"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "77c6c392-cebb-4a9b-ad6f-0330f478a79f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:29.385241", "total_time_seconds": 41.336296796798706, "status_code": 200, "message": "Request completed in 41.3363s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.439676", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.458708", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.458708", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.458708", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.458708", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:14:33.459710", "file_processing_time": 0.01652836799621582, "message": "Custom metric: file_processing_time=0.01652836799621582"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "10deba57-aa25-44ee-a47e-75c547ebd6a8", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:01.398362", "total_time_seconds": 27.958686351776123, "status_code": 200, "message": "Request completed in 27.9587s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.467817", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.482836", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.482836", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.482836", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.482836", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:05.482836", "file_processing_time": 0.011508703231811523, "message": "Custom metric: file_processing_time=0.011508703231811523"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "0b668b2e-312b-48ce-8a81-27b6c8036a2d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:47.613789", "total_time_seconds": 42.14597177505493, "status_code": 200, "message": "Request completed in 42.1460s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.682529", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.697901", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.697901", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.697901", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.697901", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:15:51.697901", "file_processing_time": 0.013362646102905273, "message": "Custom metric: file_processing_time=0.013362646102905273"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "d96de151-c393-4358-af50-6586e7993baa", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:39.886752", "total_time_seconds": 48.20422387123108, "status_code": 200, "message": "Request completed in 48.2042s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.962184", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.981494", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.981494", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.981494", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.982503", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:16:43.982503", "file_processing_time": 0.015310049057006836, "message": "Custom metric: file_processing_time=0.015310049057006836"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "8fdd292b-d8c5-49af-9cd6-c9bf7ae798eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:26.264909", "total_time_seconds": 42.302724838256836, "status_code": 200, "message": "Request completed in 42.3027s with status 200"}
{"event": "request_start", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.330572", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.351920", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.352925", "file_size_bytes": 70825, "message": "Custom metric: file_size_bytes=70825"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.352925", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.352925", "extracted_text_length": 2308, "message": "Custom metric: extracted_text_length=2308"}
{"event": "custom_metric", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:17:30.352925", "file_processing_time": 0.01782679557800293, "message": "Custom metric: file_processing_time=0.01782679557800293"}
{"event": "request_complete", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "request_id": "71910dde-0cf8-4455-bfad-0b142c34f6eb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:18:08.350913", "total_time_seconds": 38.02034115791321, "status_code": 200, "message": "Request completed in 38.0203s with status 200"}
{"event": "session_end", "session_id": "e4df2011-d60c-4416-b043-cf91efa728df", "timestamp": "2025-06-18T16:24:09.568062", "message": "API session ended"}
