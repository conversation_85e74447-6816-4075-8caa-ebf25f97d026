================================================================================
LLM CALL LOG - 2025-06-18 15:20:42
================================================================================

[CALL INFORMATION]
Endpoint: /hybrid_resume
Context: Resume-Ya<PERSON>.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2025-06-18T15:20:42.048002
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 14.329737663269043,
  "has_image": false,
  "prompt_length": 6522,
  "response_length": 3041,
  "eval_count": 845,
  "prompt_eval_count": 1565,
  "model_total_duration": 14320450600
}

[PROMPT]
Length: 6522 characters
----------------------------------------

    CRITICAL OUTPUT FORMAT REQUIREMENTS - READ THIS FIRST:
    - Return ONLY the JSON object, no additional text or formatting
    - Do NOT use markdown code blocks (no ```json or ```)
    - Do NOT add any explanations or comments
    - Start directly with { and end directly with }
    - Ensure the JSON is valid and follows the exact schema below

    You are an expert resume parser. Your task is to extract ALL structured information from the resume sections below and format it as JSON.

    The resume content has already been pre-processed and organized into sections. Your job is to parse this structured content into the exact JSON format specified.

    CRITICAL SCHEMA REQUIREMENTS:
    1. Extract ALL information that is explicitly mentioned in the resume sections.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range"
            }
        ],
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }
        ],
        "projects": [
            {
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }
        ],
        "certifications": ["Certification Name 1", "Certification Name 2", ...],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": ["Achievement 1", "Achievement 2", ...],
        "publications": ["Publication 1", "Publication 2", ...],
        "volunteer_experience": ["Volunteer Experience 1", "Volunteer Experience 2", ...],
        "references": [],
        "summary": "Summary text or null",
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }

    STRICT FORMATTING RULES:
    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. CRITICAL: Keep skills as a simple array of strings, not as objects or dictionaries
    8. CRITICAL: Keep certifications as a simple array of strings, not as objects
    9. CRITICAL: Keep achievements as a simple array of strings, not as objects
    10. CRITICAL: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\n)
    11. CRITICAL: For projects, include all details in the description field as a single string with line breaks (\n)
    12. CRITICAL: Extract the name, email, and phone from the CONTACT INFORMATION section if available

    CONTENT CLASSIFICATION RULES:
    13. EXPERIENCE section should contain ONLY professional work experience with companies/organizations
    14. EXTRA-CURRICULAR ACTIVITIES, sports, competitions, awards should go in ACHIEVEMENTS array
    15. CERTIFICATIONS should be simple strings like "Python for Beginners - Newton School (2024)"
    16. Do NOT create experience entries for activities that are not professional work
    17. Personal activities, sports, competitions, olympiads should be in achievements, not experience

    Resume Sections:
    CONTACT INFORMATION:
Yashi Gupta
LinkedIn Github HackerRank Leetcode

SUMMARY:
Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ready to enhance user
experiences in a team environment.

EDUCATION:
Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 8.57/10.0
Intermediate (Class XII) 2021 - 2022
Huddard High School Grade: 94.26%
Matriculation (Class X) 2019 - 2020
Huddard High School Grade: 93.4%
INTERNSHIPS
Frontend Intern June 2024 - August 2024
IIT Roorkee with Social Studies Foundation Remote
Tech Stack: NextJS, Tailwind CSS
Description: Developed in collaboration between IIT Roorkee and the Social Studies Foundation, this project curates and
displays government schemes, job openings, and scholarships for the SC/ST community.
Contributions: Designed lter tabs with state-wise and department-wise options, integrated APIs for categorized schemes
(scholarships, job openings, general schemes), and built responsive components like detailed scheme cards and homepage
elements for improved user experience.

SKILLS:
Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python
Software Packages: React, MySQL, Express JS, NodeJS, Prisma ORM, Tailwind, Hadoop HDFS
Soft Skills: Presentation Skills, Teamwork, Time management
EXTRA-CURRICULAR ACTIVITIES
Participated in a hackathon organized by Google Cloud to develop an AI-powered chatbot website.
Participated in HackCBS, the biggest student-led hackathon.
Moderator at Coding Club, Newton School of Technology.

PROJECTS:
Expedition- Backend Python , ( Github ) ( Demo ) December 2024
Tech Stack: Python
Description: Backend in Python for a ticket booking system, Expedition.
Features: Comprehensive CRUD APIs for managing data, secure user authentication, and ecient data handling for
smooth ticket booking and selling system.
iPhone 15 Pro Website , ( Github ) ( Demo ) December 2024
Tech Stack: ReactJS, ThreeJS, GSAP
Description: iPhone 15 Pro website replica with exceptional design and functionality.
Features: Developed a precise iPhone 15 Pro website replica featuring an advanced video carousel and enhanced user
experience.
FashMore-ECommerce-Project , ( Github ) ( Demo ) September 2024
Tech Stack: React, Firebase
Description: Developed a modern e-commerce platform delivering the latest in fashion trends.
Features: Seamless browsing with organized categories, secure authentication, advanced search, ltering, and ecient cart
management.



    IMPORTANT: Return the JSON object now (no markdown, no code blocks, just the JSON):
    
----------------------------------------

[RESPONSE]
Length: 3041 characters
----------------------------------------
```json
{
  "name": "Yashi Gupta",
  "email": null,
  "phone": null,
  "education": [
    {
      "degree": "Bachelor of Technology (Computer Science)",
      "institution": "Newton School of Technology, Rishihood University",
      "year": "2023 - 2027"
    },
    {
      "degree": "Intermediate",
      "institution": "Huddard High School",
      "year": "2021 - 2022"
    },
    {
      "degree": "Matriculation",
      "institution": "Huddard High School",
      "year": "2019 - 2020"
    }
  ],
  "skills": [
    "Java",
    "JavaScript",
    "CSS",
    "HTML",
    "TypeScript",
    "Python",
    "React",
    "MySQL",
    "Express JS",
    "NodeJS",
    "Prisma ORM",
    "Tailwind",
    "Hadoop HDFS"
  ],
  "experience": [
    {
      "company_name": "IIT Roorkee with Social Studies Foundation",
      "role": "Frontend Intern",
      "duration": "June 2024 - August 2024",
      "key_responsibilities": "Developed a project curating and displaying government schemes, job openings, and scholarships for the SC/ST community. Designed lter tabs with state-wise and department-wise options, integrated APIs for categorized schemes (scholarships, job openings, general schemes), and built responsive components like detailed scheme cards and homepage elements for improved user experience."
    }
  ],
  "projects": [
    {
      "name": "Expedition - Backend Python",
      "description": "Backend in Python for a ticket booking system, Expedition. Comprehensive CRUD APIs for managing data, secure user authentication, and ecient data handling for smooth ticket booking and selling system."
    },
    {
      "name": "iPhone 15 Pro Website",
      "description": "iPhone 15 Pro website replica with exceptional design and functionality. Developed a precise iPhone 15 Pro website replica featuring an advanced video carousel and enhanced user experience. Developed in ReactJS, ThreeJS, GSAP"
    },
    {
      "name": "FashMore-ECommerce-Project",
      "description": "Developed a modern e-commerce platform delivering the latest in fashion trends. Developed in React, Firebase. Seamless browsing with organized categories, secure authentication, advanced search, ltering, and ecient cart management."
    }
  ],
  "certifications": [
    "Python for Beginners - Newton School (2024)"
  ],
  "domain_of_interest": [],
  "languages_known": [],
  "achievements": [
    "Participated in a hackathon organized by Google Cloud to develop an AI-powered chatbot website.",
    "Participated in HackCBS, the biggest student-led hackathon.",
    "Moderator at Coding Club, Newton School of Technology."
  ],
  "publications": [],
  "volunteer_experience": [],
  "references": [],
  "summary": "Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ready to enhance user experiences in a team environment.",
  "personal_projects": [],
  "social_media": [
    "linkedin.com/yashi-gupta",
    "github.com/yashi-gupta",
    "hackerank.com/yashi-gupta",
    "leetcode.com/yashi-gupta"
  ]
}
```
----------------------------------------

================================================================================