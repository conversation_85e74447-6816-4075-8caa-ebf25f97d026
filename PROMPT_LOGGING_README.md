# Prompt Logging System

This document describes the comprehensive prompt logging system implemented for debugging LLM calls in the Gemma API.

## Overview

The prompt logging system automatically captures all LLM interactions including:
- Prompts sent to the model
- Responses received from the model
- Model metadata (name, processing time, token usage)
- Context information (endpoint, file names, operation types)
- Error information when calls fail

## Directory Structure

```
prompt_logs/
├── resume_parsing/          # Resume parsing operations
├── job_description_parsing/ # Job description parsing operations
├── general_generation/      # General text generation
├── image_processing/        # Image-based text extraction
├── json_repair/            # JSON self-healing operations
├── question_generation/    # Interview question generation
├── interview_analysis/     # Resume-JD matching analysis
└── call_summary_processing/ # VAPI call summary processing
```

## File Naming Convention

Log files follow this naming pattern:
```
{timestamp}_{endpoint}_{context}_{call_type}.txt
```

Where:
- **timestamp**: `YYYYMMDD_HHMMSS_mmm` format (includes milliseconds)
- **endpoint**: API endpoint name (resume, jd_parser, generate, etc.)
- **context**: Additional context (filename, operation type, etc.)
- **call_type**: Type of LLM call (main, repair, image_extract, etc.)

### Examples:
- `20241217_143052_123_resume_john_doe_cv_main.txt`
- `20241217_143055_456_jd_parser_software_engineer_jd_main.txt`
- `20241217_143058_789_json_repair_malformed_json_repair.txt`

## Log File Format

Each log file contains:

```
================================================================================
LLM CALL LOG - 2024-12-17 14:30:52
================================================================================

[CALL INFORMATION]
Endpoint: /resume
Context: john_doe_cv.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2024-12-17T14:30:52.123456
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 15.67,
  "has_image": false,
  "prompt_length": 2345,
  "response_length": 1234,
  "eval_count": 456,
  "prompt_eval_count": 123
}

[PROMPT]
Length: 2345 characters
----------------------------------------
You are an expert resume parser...
(full prompt content)
----------------------------------------

[RESPONSE]
Length: 1234 characters
----------------------------------------
{
  "name": "John Doe",
  "email": "<EMAIL>",
  ...
}
----------------------------------------

================================================================================
```

## API Endpoints for Log Management

### Get Log Statistics
```http
GET /prompt_logs/stats
```

Returns statistics about prompt logs:
```json
{
  "status": "success",
  "stats": {
    "total_files": 150,
    "total_size": 5242880,
    "subdirs": {
      "resume_parsing": {
        "files": 45,
        "size": 1048576
      },
      "job_description_parsing": {
        "files": 32,
        "size": 786432
      }
    }
  }
}
```

### Cleanup Old Logs
```http
POST /prompt_logs/cleanup?days_to_keep=7
```

Removes log files older than specified days:
```json
{
  "status": "success",
  "message": "Cleaned up 25 old prompt log files",
  "deleted_count": 25,
  "days_kept": 7
}
```

## Call Types

The system tracks different types of LLM calls:

- **main**: Primary operation calls (resume parsing, JD parsing, etc.)
- **repair**: JSON self-healing operations
- **image_extract**: Image-based text extraction
- **image_chat**: Image conversation processing
- **question_generation**: Interview question generation

## Context Information

Context provides additional information about the operation:

- **File-based operations**: Original filename (sanitized)
- **Question generation**: Question category (technical, experience, etc.)
- **Image processing**: Image filename or operation type
- **General chat**: "chat_conversation"
- **VAPI processing**: "vapi_summary"

## Usage Examples

### Testing the System

Run the test script to verify logging functionality:
```bash
cd testing
python test_prompt_logging.py
```

### Viewing Recent Logs

Check the most recent logs in each category:
```bash
# Resume parsing logs
ls -la prompt_logs/resume_parsing/ | tail -5

# Job description parsing logs
ls -la prompt_logs/job_description_parsing/ | tail -5

# General generation logs
ls -la prompt_logs/general_generation/ | tail -5
```

### Finding Specific Logs

Search for logs by filename:
```bash
find prompt_logs -name "*john_doe*" -type f
```

Search for logs by endpoint:
```bash
find prompt_logs -name "*resume*" -type f
```

Search for error logs:
```bash
grep -r "ERROR" prompt_logs/
```

## Debugging Workflow

1. **Identify the Issue**: Note the endpoint and approximate time of the problem
2. **Locate the Log**: Use the timestamp and context to find the relevant log file
3. **Analyze the Prompt**: Check if the prompt is correctly formatted
4. **Check the Response**: Verify the model's response and any errors
5. **Review Metadata**: Check processing time, token usage, and other metrics

## Best Practices

1. **Regular Cleanup**: Use the cleanup endpoint to manage disk space
2. **Monitor Log Size**: Check log statistics regularly
3. **Meaningful Context**: Provide descriptive context when making LLM calls
4. **Error Analysis**: Review error logs to identify patterns
5. **Performance Monitoring**: Use metadata to track processing times

## Configuration

Key configuration options in `prompt_logger.py`:

- `PROMPT_LOGS_DIR`: Base directory for logs (default: "prompt_logs")
- `SUBDIRS`: Mapping of operation types to subdirectories
- Filename sanitization rules
- Log retention policies

## Troubleshooting

### Common Issues

1. **No logs created**: Check file permissions and disk space
2. **Import errors**: Ensure `prompt_logger.py` is in the correct location
3. **Large log files**: Implement log rotation or increase cleanup frequency
4. **Missing context**: Update LLM calls to include proper context information

### Error Messages

- `"Failed to log LLM call"`: Check file system permissions
- `"Failed to create prompt log directories"`: Check disk space and permissions
- `"Import prompt_logger could not be resolved"`: Check file location and imports

## Security Considerations

- Log files may contain sensitive information from resumes and job descriptions
- Implement appropriate access controls for the `prompt_logs` directory
- Consider encryption for sensitive log data
- Regular cleanup helps minimize data exposure
- Monitor log access and implement audit trails if needed
