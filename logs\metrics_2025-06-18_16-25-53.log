{"event": "session_start", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "timestamp": "2025-06-18T16:25:53.486249", "message": "New API session started"}
{"event": "request_start", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "3c014cab-787b-4fb7-922f-c429cbb12c59", "endpoint": "/", "timestamp": "2025-06-18T16:26:23.909011", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "3c014cab-787b-4fb7-922f-c429cbb12c59", "endpoint": "/", "timestamp": "2025-06-18T16:26:23.910280", "total_time_seconds": 0.0012691020965576172, "status_code": 200, "message": "Request completed in 0.0013s with status 200"}
{"event": "request_start", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "fc644ce0-69ef-4c84-bcac-be5fb09a6348", "endpoint": "/", "timestamp": "2025-06-18T16:26:34.095909", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "fc644ce0-69ef-4c84-bcac-be5fb09a6348", "endpoint": "/", "timestamp": "2025-06-18T16:26:34.097415", "total_time_seconds": 0.0015056133270263672, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.154555", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.172067", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.173325", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.173325", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.173325", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:26:36.173325", "file_processing_time": 0.014000892639160156, "message": "Custom metric: file_processing_time=0.014000892639160156"}
{"event": "request_complete", "session_id": "41b6ca43-2451-48e8-841c-961135e786df", "request_id": "e9e1e8f6-3299-43af-8ee0-456fd2ede913", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:27:26.133845", "total_time_seconds": 49.9792902469635, "status_code": 200, "message": "Request completed in 49.9793s with status 200"}
