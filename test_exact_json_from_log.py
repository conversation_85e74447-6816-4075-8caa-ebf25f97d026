#!/usr/bin/env python3
"""
Test the exact JSON from the log to see why it's failing.
"""

import json

def test_exact_json():
    """Test the exact JSON from <PERSON><PERSON><PERSON>'s log."""
    
    # Exact JSON from the log (with markdown)
    exact_json_from_log = '''```json
{
  "name": "<PERSON>urag Pandey",
  "email": null,
  "phone": null,
  "education": [
    {
      "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
      "institution": "Newton School of Technology, Rishihood University",
      "year": "2024 - 2028"
    },
    {
      "degree": "Intermediate (Class XII)",
      "institution": "FIITJEE WORLD SCHOOL NARAYANGUDA",
      "year": "2020 - 2022"
    },
    {
      "degree": "Matriculation (Class X)",
      "institution": "Army Public School Bolarum",
      "year": "2019 - 2020"
    }
  ],
  "skills": [
    "Python",
    "JavaScript",
    "CSS",
    "HTML",
    "Excel",
    "React",
    "Team Building",
    "Leadership",
    "Photoshop",
    "Social Media",
    "Graphic Design"
  ],
  "experience": [],
  "projects": [
    {
      "name": "Calculator Project",
      "description": "A web-based calculator with a user-friendly interface that supports basic arithmetic operations, updates the display dynamically, and adapts to dierent screen sizes. Features: Basic arithmetic (+, , Ö, ö), real-time display updates, responsive design, keyboard support, and error handling."
    },
    {
      "name": "Weather Website",
      "description": "A weather website that fetches real-time weather data for dierent locations, displaying temperature, conditions, and forecasts in a clean and responsive UI. Features: Search for locations, real-time weather updates, responsive design, dynamic UI, and weather condition icons."
    }
  ],
  "certifications": [
    "Python for Beginners, Newton School Of Technology ( Link ) August 2024",
    "Completed Python For Beginners Course, and it was a very good experience doing it.",
    "International Mathematics Olympiad(IMO), Science Olympiad Foundation(SOF) ( Link ) December 2017",
    "Grand Master Level in Mental Arithmetic , ALAMA International ( Link ) January 2017"
  ],
  "domain_of_interest": [],
  "languages_known": [],
  "achievements": [
    "I am a basketball player and have played in inter school and cluster fstate levelgmatches.",
    "I love mathematics from my childhood so thats why , I Have Completed Grand master level in mental arithmetics fAbacusgand represented in national competition two times .",
    "I have secured a silver medal in SOF IMO fScience Olympiad Foundation , International Mathematics Olympiad g"
  ],
  "publications": [],
  "volunteer_experience": [
    "Member of PR and Marketing team in synergy club."
  ],
  "references": [],
  "summary": "Aspiring developer and tech enthusiast with experience in Python, HTML, CSS, JavaScript, and React. Passionate about creating dynamic and responsive web applications while continuously learning new technologies.",
  "personal_projects": [],
  "social_media": [
    "LinkedIn",
    "Github",
    "CodeChef",
    "Codeforces",
    "Leetcode"
  ]
}
```'''

    print("🧪 Testing Exact JSON from Anurag's Log")
    print("=" * 60)
    
    print(f"📄 Original JSON length: {len(exact_json_from_log)} characters")
    
    # Apply our cleanup logic (same as in main.py)
    json_str = exact_json_from_log.strip()
    
    print(f"\n🔧 Step 1: Markdown cleanup")
    if "```json" in json_str:
        start_marker = "```json"
        end_marker = "```"
        start_idx = json_str.find(start_marker)
        if start_idx != -1:
            start_idx += len(start_marker)
            end_idx = json_str.find(end_marker, start_idx)
            if end_idx != -1:
                json_str = json_str[start_idx:end_idx].strip()
                print(f"✅ Extracted content between markers")
    
    # Additional cleanup
    if json_str.startswith('```json'):
        json_str = json_str[7:].strip()
    if json_str.startswith('```'):
        json_str = json_str[3:].strip()
    if json_str.endswith('```'):
        json_str = json_str[:-3].strip()
    
    json_str = json_str.replace('```json', '').replace('```', '').strip()
    
    # Fallback method
    if not json_str.startswith('{'):
        start_brace = json_str.find('{')
        end_brace = json_str.rfind('}')
        if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
            json_str = json_str[start_brace:end_brace+1]
            print(f"✅ Used brace extraction fallback")
    
    print(f"\n📊 Cleaned JSON:")
    print(f"Length: {len(json_str)} characters")
    print(f"Starts with: {repr(json_str[:50])}")
    print(f"Ends with: {repr(json_str[-20:])}")
    
    # Test JSON parsing
    print(f"\n🧪 Testing JSON parsing:")
    try:
        parsed_data = json.loads(json_str)
        print("✅ JSON parsing successful!")
        
        print(f"\n📊 Parsed data:")
        print(f"   Name: {parsed_data.get('name')}")
        print(f"   Education: {len(parsed_data.get('education', []))} entries")
        print(f"   Skills: {len(parsed_data.get('skills', []))} items")
        print(f"   Projects: {len(parsed_data.get('projects', []))} items")
        print(f"   Certifications: {len(parsed_data.get('certifications', []))} items")
        print(f"   Achievements: {len(parsed_data.get('achievements', []))} items")
        print(f"   Summary: {'Present' if parsed_data.get('summary') else 'Missing'}")
        
        return True, parsed_data
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
        print(f"Error at position: {e.pos}")
        if e.pos < len(json_str):
            print(f"Character at error: {repr(json_str[e.pos])}")
            print(f"Context: {repr(json_str[max(0, e.pos-20):e.pos+20])}")
        
        # Try to identify the issue
        print(f"\n🔍 Debugging the issue:")
        
        # Check for special characters
        special_chars = []
        for i, char in enumerate(json_str):
            if ord(char) > 127:  # Non-ASCII characters
                special_chars.append((i, char, ord(char)))
        
        if special_chars:
            print(f"⚠️ Found {len(special_chars)} non-ASCII characters:")
            for pos, char, code in special_chars[:5]:  # Show first 5
                print(f"   Position {pos}: '{char}' (code {code})")
        
        return False, None

if __name__ == "__main__":
    success, data = test_exact_json()
    
    print(f"\n{'='*60}")
    print("📊 SUMMARY")
    print(f"{'='*60}")
    
    if success:
        print("🎉 SUCCESS: The JSON from the log parses correctly!")
        print("✅ Our cleanup logic is working")
        print("✅ The issue must be elsewhere in the code")
        print("\n💡 This means the problem is likely in:")
        print("- How the response is being read from the LLM")
        print("- How the cleanup is being applied in the actual code")
        print("- Some other part of the processing pipeline")
    else:
        print("❌ FAILED: The JSON from the log has issues")
        print("🔧 The cleanup logic needs improvement")
        print("📝 Check the special characters or syntax errors above")
