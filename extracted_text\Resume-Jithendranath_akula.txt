# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON><PERSON><PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 16:33:15
# Text Length: 2738 characters
# ================================================

<PERSON><PERSON><PERSON>
LinkedIn Github Codeforces
PROFESSIONAL SUMMARY
Enthusiastic developer skilled in front-end and back-end technologies, including HTML, CSS, JavaScript, React, Node.js, and
databases. Experienced in creating responsive, user-focused applications with a commitment to quality and innovation.
Thrives in collaborative teams to deliver impactful web solutions.
EDUCATION
Bachelor of Technology (Articial intelligence) 2023 - 2027
Newton School of Technology , Rishihood University Grade: 7.86/10.0
Intermediate (Class XII) 2021 - 2023
Sri Chaitanya Junior College Grade: 96.3%
Matriculation (Class X) 2020 - 2021
Sri Chaitanya International Olympiad School Grade: 10.0/10.0
PROJECTS
Peer Chat , ( Github ) ( Demo ) December 2024
Tech Stack : JavaScript , React , Tailwind CSS, NodeJS , ExpressJS , MongoDB , WebSockets, Zustand.
Description : A Full Stack Chat Application which allows user to connect and chat in real-time and with secure
authentication. Features:
Real-time communicating : Implemented with Socket.io.
Global State management : Used Zustand for managing State.
Robust error handling on both server and client sides.
Fully Responsive and clean UI with 32 dierent themes and Online user status visibility
QR Management , ( Github ) ( Demo ) December 2024
Tech Stack: Node.js, Express.js, Prisma ORM, MySQL
Description: Backend for a QR Code Management Platform to generate, manage, and track static and dynamic QR
codes with analytics.
Features:
{ Secure Authentication : JWT-based user authentication.
{ Dynamic QR Code Management : Create and update dynamic QR codes.
{ Tracking and Analytics : Scan tracking with detailed insights and trends.
{ Scalable Database : Prisma ORM with MySQL for ecient data handling.
Notesmaker-server , ( Github ) ( Demo ) October 2024
Tech Stack: Node.js, Express.js, Prisma ORM, MySQL
Description: Backend for a note management application supporting CRUD operations on notes, workshops, and folders.
Features:
{ MVC Architecture : Clean and modular design for maintainability.
{ Secure Authentication : JWT-based registration and login.
{ Scalable Database : Prisma ORM with MySQL for ecient data handling.
SKILLS
Computer Languages: SQL, Java, Python, JavaScript, CSS, HTML
Software Packages: React, Mongo DB, Express JS, Prisma ORM, MySQL, Tailwind, NodeJS
Additional Courses: Data Structure
EXTRA-CURRICULAR ACTIVITIES
Ranked in top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology.
Qualied for the Semi-Finals in Badminton and Football and got Silver medal at the University Sports Fest.
Competitive-Coding - Member of NST-CC Competitive Coding Club
Participated in over 10 badminton tournament in my district (khammam).
