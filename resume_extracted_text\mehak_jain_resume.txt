# Extracted Text Debug File
# Source File: mehak_jain_resume.pdf
# Context: resume
# Extraction Method: pdf_text
# Timestamp: 2025-06-09 14:03:32
# Text Length: 2569 characters
# ================================================

MEHAK JAIN
Software Engineer
Email: <EMAIL>
Phone: +91-9876543210
Location: Bangalore, India
LinkedIn: linkedin.com/in/mehakjain

PROFESSIONAL SUMMARY
Experienced Software Engineer with 4+ years of expertise in full-stack development, 
machine learning, and cloud technologies. Proven track record of delivering scalable 
web applications and implementing ML solutions.

TECHNICAL SKILLS
Programming Languages: Python, JavaScript, Java, C++
Web Technologies: React, Node.js, Express.js, HTML5, CSS3
Databases: MySQL, PostgreSQL, MongoDB, Redis
Cloud Platforms: AWS, Azure, Google Cloud Platform
Machine Learning: TensorFlow, PyTorch, scikit-learn, Pan<PERSON>, NumPy
DevOps: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, CI/CD

PROFESSIONAL EXPERIENCE

Software Engineer | TechCorp Solutions | 2020 - Present
• Developed and maintained 5+ web applications using React and Node.js
• Implemented REST APIs serving 10,000+ daily active users
• Optimized database queries resulting in 40% performance improvement
• Led a team of 3 junior developers on critical project deliveries
• Integrated machine learning models for recommendation systems

Junior Software Developer | StartupInc | 2018 - 2020
• Built responsive web interfaces using React and modern JavaScript
• Collaborated with cross-functional teams in Agile environment
• Implemented automated testing reducing bug reports by 30%
• Participated in code reviews and mentored new team members

EDUCATION
Bachelor of Technology in Computer Science
Indian Institute of Technology, Delhi | 2014 - 2018
CGPA: 8.5/10.0

PROJECTS
1. E-commerce Recommendation System
   • Built ML-powered product recommendation engine using collaborative filtering
   • Technologies: Python, TensorFlow, Flask, PostgreSQL
   • Achieved 25% increase in user engagement

2. Real-time Chat Application
   • Developed scalable chat app with WebSocket support
   • Technologies: Node.js, Socket.io, React, MongoDB
   • Supports 1000+ concurrent users

3. Cloud-based Task Management System
   • Created full-stack task management platform
   • Technologies: React, Express.js, AWS Lambda, DynamoDB
   • Implemented real-time collaboration features

CERTIFICATIONS
• AWS Certified Solutions Architect - Associate (2022)
• Google Cloud Professional Data Engineer (2021)
• Microsoft Azure Fundamentals (2020)

ACHIEVEMENTS
• Winner of TechCorp Innovation Challenge 2022
• Published research paper on "Scalable ML Systems" in IEEE Conference
• Contributed to 3 open-source projects with 500+ GitHub stars
• Mentored 10+ junior developers and interns