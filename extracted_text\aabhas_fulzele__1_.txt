# Extracted Text Debug File
# Source File: a<PERSON><PERSON> (1).pdf
# Context: section_extraction
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 13:23:49
# Text Length: 3123 characters
# ================================================

 Aab<PERSON>  
 +91 8381010425  
Email:  <EMAIL>  
Sex: Male  Date  of birth  02/06/1998  | Nationality  Indian  
 
 
Seeking a suitable position in the field of Dot Net, SQL , DevOps ,AWS field where  
excellent analytical problem -solving skill, great interest in the concerned field, relevant  
knowledge and a strong work ethic can advance Personal and Organization ’s growth and  
gives  me a better  stability  with work -life balance  
 EDUCATION:    
M. Tech,  Software  Engineering  Feb 2021 - Oct2022  
Veermata  Jijabai  Technological  Institute,  Mumbai  CGPA  – 7.55  
B.E, Computer  Engineering  2016  – 2020  
Maharashtra  Institute  of Technology,  Pune  CGPA–6.71  
 IT SKILLS:    
 
Public  Cloud  AWS  
DevOps  Tools  Docker  , Jenkins,  GIT,  Ansible  , Maven,  
Operating  Systems  Windows  10 
Skills  Vb.net,  C#, Microsoft  SQL  Server  2018,visual  studio  2012 ,ASP.NET  
 
  WORK  EXPERIENCE:   
 
Client company= National  Securities  Depository  Limited   
Parent company=PC Center  
JAN 2023 - present  
 
Junior  Software  Engineer  
1. Used  vb.net  for coding.  
2. Used  Itextsharp  to generate  reports.  
3. Involved  in documentation  of the components  and reporting  
 
Project  Name : Depository  Participant  Module  DPM+  
Tools : ASP.NET,  C#, VB .NET,  MYSQL,HTML,  MVC  
Project  Details : 
DPM+  is a web  application  which  is used  to store  the details of  Depository  participants  (DPs)  and their  account  
details  in various  modules  of DPM+.  It also  consists  of the PAN details  of DPs.  

 CERTIFICATIONS  and  TRANINGS:  
• AWS Certified Cloud Practitioner.  
Aws  re/start  graduate  
 
PROJECTS:  
CI/CD  pipeline  implementation  using  Jenkins  
• Implemented  CI/CD  pipeline  using  AWS  
• Responsible for Docker container creation and management, Docker file  
management, deployment of  micro services  to container.  
• Cloud infrastructure provision and management, securing cloud environment by  
implementing  best  practices  in security  and network  domain.  
• Responsible for server configuration management via Ansible and environments  
management.  
• Have  conducted  internal  trainings  on DevOps  tools  like Docker  and AWS.  
• Troubleshooting in build environment specially fixing development issues, setup  
git branch strategy, merge feature branches with master and push changes to  
Github  based  source  code repo sitory.  
• Managing  build  and release  definition  as per project  requirement,  trigger  build  
job. 
• Created  Jenkins  file for automated  continuous  integration  and deployment.  
• Responsible  for Production  deployment  pipeline  by defining  zero  down  Devops  
strategy  
• Responsible for managing AWS cloud -based resources such as EC2, VPC, S3,  
Cloud  formation etc.  
• Responsible  for high  availability  of running  applications  on cloud  instances  and 
containers.  
• Working  with  Developers,  Release  management  and tool  decision  team  to 
enhance  CI/CD pipeline.  
• Implemented  best  practices  for Docker,  Ansible,  Gitlab,  Jenkins,  AWS,pipelineetc.  
 
