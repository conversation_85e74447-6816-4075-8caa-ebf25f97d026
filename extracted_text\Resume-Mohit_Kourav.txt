# Extracted Text Debug File
# Source File: Resume-Mohit <PERSON>v.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:50:37
# Text Length: 3421 characters
# ================================================

Mohit <PERSON>v
LinkedIn Github HackerEarth CodeChef Codeforces Leetcode
PROFESSIONAL SUMMARY
Aspiring Computer Science Engineer specializing in AI & ML, pursuing B.Tech from Newton School of Technology (ADYPU).
Passionate about building intelligent solutions and solving real-world problems with programming, data analysis, and machine
learning.
EDUCATION
Bachelor of Technology (Computer Science) 2024 - 2028
Newton School Of Technology, ADYPU University Grade: 7.18/10.0
Intermediate (Class XII) 2021 - 2022
Maharastra High School Grade: 78.4%
Matriculation (Class X) 2019 - 2020
Dream India School Grade: 89.0%
PROJECTS
Statistics Calculator , ( Github ) ( Demo ) March 2025
Purpose : A tool for calculating basic statistical measures (Mean, Median, Mode) based on user-input numbers.
User Interface : Simple and interactive design for entering numbers and viewing results.
Features :
{ Mean Calculation : Computes the average of entered numbers.
{ Median Calculation : Finds the middle value of sorted numbers.
{ Mode Calculation : Identies the most frequent number(s).
{ Responsive Design : Optimized for both desktop and mobile devices.
Technology Stack : HTML, CSS, JavaScript.
Natural Remedies for a Healthy Life , ( Github ) ( Demo ) November 2024
Objective: Developed a static web application oering natural remedies and wellness tips with a user-friendly and intuitive
interface.
Findings:
Users preferred a clean, minimalistic design for quick access to information.
Responsive design was essential for a seamless experience across devices.
Skills Applied:
HTML: Structured web pages.
CSS: Styled and designed a responsive layout.
Responsive Web Design: Ensured compatibility across desktops, tablets, and mobile devices.
Tools Used:
Visual Studio Code: For development.
Netlify: For deployment and hosting.
Chrome DevTools: For debugging and responsiveness testing.
Impact: Delivered an accessible platform promoting health awareness while enhancing web development and UI/UX design
skills.
CERTIFICATIONS
ChatGPT Prompt Engineering for Developers , DeepLearning.Ai ( Link ) February 2025
Successfully completed the "ChatGPT Prompt Engineering for Developers" course, gaining expertise in designing eective
prompts to optimize AI interactions.
Generative AI for Everyone , DeepLearning.Ai ( Link ) February 2025
Gained a comprehensive understanding of generative AI, its applications, ethical implications, and practical use cases.
AI For Everyone , DeepLearning.ai ( Link ) January 2025
Successfully completed "AI For Everyone" course, gaining a strong understanding of articial intelligence concepts, including
its societal impact, business applications, and fundamental machine learning techniques.
SKILLS
Computer Languages: JavaScript, CSS, HTML, Python
Software Packages: Excel, React
Soft Skills: Presentation Skills, Responsibility, Critical Thinking, Teamwork, Creativity, Research, Decision-making, Time
management, Team Building, Leadership
Others: Graphic Design
EXTRA-CURRICULAR ACTIVITIES
Led student councils, managed events, and represented students as Head Boy.
Hosted multiple school functions, showcasing coordination skills.
Contributed to community service initiatives as a social work volunteer.
Participated in inter-school competitions, enhancing teamwork and interpersonal skills.
Engaged in coding challenges and AI/ML workshops to enhance technical skills.
