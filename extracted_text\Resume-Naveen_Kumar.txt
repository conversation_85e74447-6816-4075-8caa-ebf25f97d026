# Extracted Text Debug File
# Source File: Resume-Naveen <PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:50:54
# Text Length: 2462 characters
# ================================================

Naveen <PERSON>
LinkedIn Github Codeforces Leetcode Personal Portfolio
PROFESSIONAL SUMMARY
Aspiring B.Tech CS/DS student and Front-end Developer with expertise in JavaScript, HTML/CSS, Python. Passionate about
solving real-world problems through technology. Leetcode Rating:- 1400+, Codeforces Rating:- 900+.
EDUCATION
Bachelor of Technology (Data Science) 2024 - 2028
Newton School of Technology, Rishihood University Grade: 7.696/10.0
Intermediate (Class XII) 2022 - 2023
Noble Kingdom Senior Secondary School Grade: 90.2%
Matriculation (Class X) 2020 - 2021
Noble Kingdom Senior Secondary School Grade: 94.0%
PROJECTS
Weather App , ( Github ) ( Demo ) February 2025
Tech Stack : HTML, CSS, JavaScript
Description :Built a weather application that fetches and displays real-time weather data. Features include a user-friendly
location-based search, detailed weather information such as humidity and wind speed, and error handling for invalid
locations.
Features: Real-Time Weather Data, Location-Based Search, Detailed Weather Information, Error Handling, User-Friendly
Interface, Responsive Design, Hover Eects.
Portfolio , ( Github ) ( Demo ) January 2025
Tech Stack: HTML, CSS, JavaScript
Description: Built a personal portfolio website to showcase projects, skills, and contact information. The website features
a clean and modern UI with smooth scrolling, section-based navigation, and interactive elements to enhance user
experience. It is fully responsive and optimized for dierent devices and screen sizes.
Features: Responsive Design, Smooth Scrolling Navigation, About Section, Projects Showcase, Contact Form, Interactive
UI Elements, Hover Eects, Clean
Modern Layout
Capstone Project , ( Github ) ( Demo ) November 2024
Tech Stack: HTML, CSS
Description: Developed a capstone project using HTML and CSS only. The project features a responsive design that
adapts to dierent screen sizes using media queries. It includes hover eects on images and other interactive elements to
enhance user experience. The application provides a clean and user-friendly interface, ensuring smooth navigation and
accessibility across devices.
Features: Responsive Design, Hover Eects, User-Friendly Interface, Smooth Navigation, Cross-Device Accessibility.
SKILLS
Computer Languages: CSS, HTML, Python, JavaScript
EXTRA-CURRICULAR ACTIVITIES
Exploring and learning new technologies in the crypto industry, including Metaverse, DeFi, NFTs, and DePIN.
