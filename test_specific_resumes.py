#!/usr/bin/env python3
"""
Test the specific resumes that were having ```json issues.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_specific_resume(resume_name):
    """Test a specific resume and check for ```json issues."""
    
    print(f"\n📄 Testing: {resume_name}")
    print("-" * 60)
    
    # Find the resume file
    resume_dir = Path("resumes for testing")
    possible_names = [
        f"Resume-{resume_name}.pdf",
        f"{resume_name}.pdf",
        f"Resume-{resume_name.replace(' ', '_')}.pdf",
        f"Resume-{resume_name.replace(' ', ' ')}.pdf"
    ]
    
    resume_path = None
    for name in possible_names:
        path = resume_dir / name
        if path.exists():
            resume_path = path
            break
    
    if not resume_path:
        print(f"❌ Resume not found. Tried: {possible_names}")
        return False, f"File not found"
    
    print(f"📊 File: {resume_path.name}")
    print(f"📊 Size: {resume_path.stat().st_size:,} bytes")
    
    try:
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=120)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                return False, f"Error: {result['error']}"
            
            # Check the key data
            name = result.get('name', 'Unknown')
            email = result.get('email')
            phone = result.get('phone')
            education_count = len(result.get('education', []))
            experience_count = len(result.get('experience', []))
            skills_count = len(result.get('skills', []))
            projects_count = len(result.get('projects', []))
            certifications_count = len(result.get('certifications', []))
            achievements_count = len(result.get('achievements', []))
            
            print(f"📊 Results:")
            print(f"   👤 Name: '{name}'")
            print(f"   📧 Email: {email or 'None'}")
            print(f"   📞 Phone: {phone or 'None'}")
            print(f"   🎓 Education: {education_count} entries")
            print(f"   💼 Experience: {experience_count} entries")
            print(f"   🛠️ Skills: {skills_count} items")
            print(f"   🚀 Projects: {projects_count} items")
            print(f"   🏆 Certifications: {certifications_count} items")
            print(f"   🏅 Achievements: {achievements_count} items")
            
            # Check if we got meaningful data (not just "Unknown")
            if name == 'Unknown' and education_count == 0 and skills_count == 0:
                print(f"❌ FAILED: Got fallback response (no meaningful data extracted)")
                return False, "Fallback response"
            else:
                print(f"✅ SUCCESS: Meaningful data extracted!")
                
                # Check for schema compliance
                issues = []
                
                # Check certifications format
                certifications = result.get('certifications', [])
                if certifications and not all(isinstance(cert, str) for cert in certifications):
                    issues.append("Certifications should be strings")
                
                # Check achievements format
                achievements = result.get('achievements', [])
                if achievements and not all(isinstance(ach, str) for ach in achievements):
                    issues.append("Achievements should be strings")
                
                # Check skills format
                skills = result.get('skills', [])
                if skills and not all(isinstance(skill, str) for skill in skills):
                    issues.append("Skills should be strings")
                
                if issues:
                    print(f"   ⚠️ Schema Issues: {', '.join(issues)}")
                else:
                    print(f"   ✅ Schema compliance: Perfect")
                
                return True, {
                    'name': name,
                    'data_counts': {
                        'education': education_count,
                        'experience': experience_count,
                        'skills': skills_count,
                        'projects': projects_count,
                        'certifications': certifications_count,
                        'achievements': achievements_count
                    },
                    'processing_time': processing_time,
                    'schema_issues': issues
                }
            
        else:
            error_msg = f"HTTP {response.status_code}: {response.text}"
            print(f"❌ FAILED: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Exception: {str(e)}"
        print(f"❌ EXCEPTION: {error_msg}")
        return False, error_msg

def main():
    """Test the specific problematic resumes."""
    
    print("🧪 Testing Specific Resumes with ```json Issues")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test the specific resumes mentioned
    test_resumes = [
        "Agrapujya Lashkari",
        "Jithendranath akula", 
        "MEESALA SREE SAI NATH",
        "Yashi Gupta"
    ]
    
    results = {}
    
    for i, resume_name in enumerate(test_resumes, 1):
        print(f"\n{'='*70}")
        print(f"📄 TESTING {i}/{len(test_resumes)}: {resume_name}")
        print(f"{'='*70}")
        
        success, result = test_specific_resume(resume_name)
        results[resume_name] = {'success': success, 'result': result}
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 TESTING SUMMARY")
    print(f"{'='*70}")
    
    successful = [name for name, data in results.items() if data['success']]
    failed = [name for name, data in results.items() if not data['success']]
    
    print(f"✅ Successful: {len(successful)}/{len(test_resumes)}")
    print(f"❌ Failed: {len(failed)}/{len(test_resumes)}")
    
    if successful:
        print(f"\n✅ Successful Resumes:")
        for name in successful:
            result_data = results[name]['result']
            if isinstance(result_data, dict):
                name_extracted = result_data.get('name', 'Unknown')
                processing_time = result_data.get('processing_time', 0)
                schema_issues = result_data.get('schema_issues', [])
                issue_indicator = " ⚠️" if schema_issues else ""
                print(f"   ✅ {name}: '{name_extracted}' ({processing_time:.1f}s){issue_indicator}")
            else:
                print(f"   ✅ {name}")
    
    if failed:
        print(f"\n❌ Failed Resumes:")
        for name in failed:
            error = results[name]['result']
            print(f"   ❌ {name}: {error}")
    
    # Check if the ```json issue is fixed
    print(f"\n🎯 ```json Issue Assessment:")
    if len(successful) == len(test_resumes):
        print("🎉 SUCCESS: All resumes processed without ```json issues!")
        print("✅ Prompt improvements and cleanup logic working")
    elif len(successful) > 0:
        print(f"⚠️ PARTIAL SUCCESS: {len(successful)}/{len(test_resumes)} resumes working")
        print("Some improvements working, but more fixes needed")
    else:
        print("❌ FAILED: ```json issues persist")
        print("Need to investigate further or try alternative approaches")
    
    print(f"\n📝 Next Steps:")
    if failed:
        print("1. Check the latest prompt logs for failed resumes")
        print("2. Look for patterns in the failures")
        print("3. Consider additional prompt modifications")
    else:
        print("✅ All resumes working - ready for broader testing!")

if __name__ == "__main__":
    main()
