================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: Charlie Resume.pdf
Extraction Method: single_call
Timestamp: 2025-06-17T17:45:42.677488
Total Sections Extracted: 8
Processing Time: 35.381412744522095 seconds
Total LLM Calls: 1
Overall Confidence: 0.9249999999999999
================================================================================

[SUMMARY]
----------------------------------------
● 7 years of experience in developing and maintaining strong backend applications using Java and Spring Boot , delivering scalable, high- performance applications that handle a large quantity of daily transactions, focusing on optimizing performance, reliability, and production- quality software to meet enterprise -level demands.
● Proficient in leveraging the full suite of Spring Framework components, including Spring MVC , Spring Data JPA , and Spring Security , to build secure, maintainable,
and highly modular backend services, ensuring seamless integration with other systems and applications while adhering to modern software development best practices.
● Skilled in designing, Object -Oriented Programming (OOP)  and implementing secure
RESTful APIs using Spring Boot , with a strong focus on authentication and
authorization mechanisms using OAuth2 and JWT, ensuring secure access control and data protection.
● Experienced in building and deploying microservices architectures, utilizing technologies such as Docker and Kubernetes for containerization and orchestration, enabling scalable and resilient applications.
● Proficient in Agile methodologies, including Scrum and Kanban, fostering collaborative development and iterative delivery of software solutions.

========================================

[PROJECTS]
----------------------------------------
IGN Entertainment
Java Developer                                                                 Sep 2018 -  Feb 2020
Project description :
The project aimed to build an online evaluation system related to films, TV series, books, and animations, etc. The website serves as a film and television works search engine and opinion exchange area. I contributed to the development of the  backend. The app allows users to click an item such as a film, a TV series or a book, and then lead them to the details page and present the picture, cast list, comments about the item. The rate, review and comments about the item are listed below the cast list and it allows users to share and exchange their opinions about the item here, which assists users to get an intuitive overview of the item.
Environment :
Java, Spring MVC, Spring Boot, Hibernate, React, HTML5, CSS3, Bootstrap, MySQL, JSON, VSCode, Agile, Scrum.
Responsibilities :
● Designed the backend using Java8 to build user information and film models.
● Developed the web page using HTML5 , CSS3 , JSON  and JavaScript .
● Effectively involved in analysis, design, and Development of the application using Object
Oriented Programming (OOP) technologies.
Target
Java Developer                                                                              Dec 2021 -  Feb 2023
Project description :
Target is a well -known e -commerce and retail platform providing a diverse range of products. The project aimed at designing a mainstream shopping app which enables customers to know the latest products and information about our products clearly and thoroughly. I'm responsible for building a web page which displays the articles, news and videos of our products. And each article  or news contains a YouTube  video or video link to introduce the details of the product to customers, which empowers users and customers to quickly and comprehensively understand the ingredients and benefits of our current
products or upcoming products.
Environment :
Java, Spring Boot, Spring Security, Spring JPA, WebSocket , Maven, RabbitMQ, JWT, Hibernate, Cassandra DB, RESTful API, JUnit, React, Mockito, AWS, Git.
Responsibilities :
● Implement the backend components for this shopping platform, utilizing Spring Boot
and Apache Kafka  to build a scalable, fault -tolerant application.
• Used Java as the main programming language to design the user information and film models.
• Leveraged the power of Spring Boot to introduce new features and create reusable
components, improving code maintainability.
• Developed the backend RESTful APIs  via Spring Boot  and Spring JPA , resulting in a
more organized and efficient data flow.
• Used Apache Kafka  for asynchronous operations for data retrieving and monitoring.
• Implemented Functional Testing and Unit Testing using JUnit and Mockito to maintain code quality and reliability.
L'Oréal
Role:  Java Developer                                                                 Feb 2020 -  Aug 2021
Project description :
As a member of the development team at Loreal, I contributed to the development of an online shopping website designed to simplify buyers’ shopping process and decrease the difficulty for sellers to sell their goods. This website allows sellers to create o r update their
products in a simple form and allows customers to pay in multiple ways. I am responsible for designing the product attributes, which ensures the product's information is well stored in the database and easy to retrieve and mapping. And it al lows the frontend team to easily
get the data and display the product on the webpage.
Environment :
Java, Spring MVC, Spring Boot, Hibernate, JPA, Maven, Apache Kafka, REST APIs, JUnit, Mockito, Agile, JWT, MySQL, Jenkins, AWS, Git.
Responsibilities :
● Implement the backend components for this shopping platform, utilizing Spring Boot
and Apache Kafka  to build a scalable, fault -tolerant application.
• Used Java as the main programming language to design the user information and film models.
• Leveraged the power of Spring Boot to introduce new features and create reusable
components, improving code maintainability.
• Developed the backend RESTful APIs  via Spring Boot  and Spring JPA , resulting in a
more organized and efficient data flow.
• Used Apache Kafka  for asynchronous operations for data retrieving and monitoring.
• Implemented Functional Testing and Unit Testing using JUnit and Mockito to maintain code quality and reliability.
IGN Entertainment
Java Developer                                                                 Sep 2018 -  Feb 2020
Project description :
The project aimed to build an online evaluation system related to films, TV series, books, and animations, etc. The website serves as a film and television works search engine and opinion exchange area. I contributed to the development of the  backend. The app allows users to click an item such as a film, a TV series or a book, and then lead them to the details page and present the picture, cast list, comments about the item. The rate, review and comments about the item are listed below the cast list and it allows users to share and exchange their opinions about the item here, which assists users to get an intuitive overview of the item.
Environment :
Java, Spring MVC, Spring Boot, Hibernate, React, HTML5, CSS3, Bootstrap, MySQL, JSON, VSCode, Agile, Scrum.
Responsibilities :
● Designed the backend using Java8 to build user information and film models.
● Developed the web page using HTML5 , CSS3 , JSON  and JavaScript .
● Effectively involved in analysis, design, and Development of the application using Object
Oriented Programming (OOP) technologies.

========================================

[ACHIEVEMENTS]
----------------------------------------
None.

========================================

[EDUCATION]
----------------------------------------
Stevens Institute of technology                                              Hoboken, New  Jersey, USA
Master of Science in  Computer Science.                                                09, 2021 -  05, 2023

========================================

[EXPERIENCE]
----------------------------------------
No content extracted for this section.

========================================

[SKILLS]
----------------------------------------
No content extracted for this section.

========================================

[CERTIFICATIONS]
----------------------------------------
No content extracted for this section.

========================================

[LANGUAGES]
----------------------------------------
No content extracted for this section.

========================================

