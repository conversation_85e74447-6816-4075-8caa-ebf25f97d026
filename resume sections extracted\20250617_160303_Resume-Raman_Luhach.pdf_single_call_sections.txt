================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: Resume-Raman Luhach.pdf
Extraction Method: single_call
Timestamp: 2025-06-17T16:03:03.357509
Total Sections Extracted: 8
Processing Time: 15.046854496002197 seconds
Total LLM Calls: 1
Overall Confidence: 0.9999999999999998
================================================================================

[SUMMARY]
----------------------------------------
Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user experiences.

========================================

[EDUCATION]
----------------------------------------
Bachelor of Technology (AI ML) 2023 - 2027
Newton School of Technology , Rishihood University Grade: 9.18/10.0
Intermediate (Class XII) 2021 - 2022
Mother India Sr Sec School Marot, Jhajjar , Haryana Grade: 90.0%
Matriculation (Class X) 2019 - 2020
R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana Grade: 95.8%

========================================

[EXPERIENCE]
----------------------------------------
No content extracted for this section.

========================================

[SKILLS]
----------------------------------------
Computer Languages: SQL, Java, JavaScript, CSS, HTML, Python
Software Packages: React, MySQL, NodeJS, Prisma ORM, Tailwind
Additional Courses: Data Structure
Soft Skills: Communication Skills, Research, Decision-making, Team Building, Leadership

========================================

[PROJECTS]
----------------------------------------
Tech Talks , ( Github ) September 2024
Tech Stack :React ,Node .js,HTML /CSS
Feature :Authentication , blog management, categorized content ,CRUD operations on Blogs
Description : Developed "Tech Talks", a blogging site for tech content. Users can register, log in, create , access
and comment on tech blogs .
Zee5 Clone , ( Github ) ( Demo ) April 2024
Tech Stack :React ,HTML /CSS andJavaScript
Description : Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access
a library of movies with intuitive UI .
Feature : Movie rendering , Authentication .

========================================

[CERTIFICATIONS]
----------------------------------------
Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024
Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations.
Developed a functional gesture-controlled wheelchair prototype.

========================================

[ACHIEVEMENTS]
----------------------------------------
No content extracted for this section.

========================================

[LANGUAGES]
----------------------------------------
No content extracted for this section.

========================================

