================================================================================
LLM CALL LOG - 2025-06-18 15:50:52
================================================================================

[CALL INFORMATION]
Endpoint: /hybrid_resume
Context: Resume-Mohit Kourav.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2025-06-18T15:50:52.744468
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 15.32441520690918,
  "has_image": false,
  "prompt_length": 7410,
  "response_length": 3979,
  "eval_count": 937,
  "prompt_eval_count": 1711,
  "model_total_duration": 15311910800
}

[PROMPT]
Length: 7410 characters
----------------------------------------

    CRITICAL OUTPUT FORMAT REQUIREMENTS - READ THIS FIRST:
    - Return ONLY the JSON object, no additional text or formatting
    - Do NOT use markdown code blocks (no ```json or ```)
    - Do NOT add any explanations or comments
    - Start directly with { and end directly with }
    - Ensure the JSON is valid and follows the exact schema below

    You are an expert resume parser. Your task is to extract ALL structured information from the resume sections below and format it as JSON.

    The resume content has already been pre-processed and organized into sections. Your job is to parse this structured content into the exact JSON format specified.

    CRITICAL SCHEMA REQUIREMENTS:
    1. Extract ALL information that is explicitly mentioned in the resume sections.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range"
            }
        ],
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }
        ],
        "projects": [
            {
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }
        ],
        "certifications": ["Certification Name 1", "Certification Name 2", ...],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": ["Achievement 1", "Achievement 2", ...],
        "publications": ["Publication 1", "Publication 2", ...],
        "volunteer_experience": ["Volunteer Experience 1", "Volunteer Experience 2", ...],
        "references": [],
        "summary": "Summary text or null",
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }

    STRICT FORMATTING RULES:
    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. CRITICAL: Keep skills as a simple array of strings, not as objects or dictionaries
    8. CRITICAL: Keep certifications as a simple array of strings, not as objects
    9. CRITICAL: Keep achievements as a simple array of strings, not as objects
    10. CRITICAL: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\n)
    11. CRITICAL: For projects, include all details in the description field as a single string with line breaks (\n)
    12. CRITICAL: Extract the name, email, and phone from the CONTACT INFORMATION section if available

    CONTENT CLASSIFICATION RULES:
    13. EXPERIENCE section should contain ONLY professional work experience with companies/organizations
    14. EXTRA-CURRICULAR ACTIVITIES, sports, competitions, awards should go in ACHIEVEMENTS array
    15. CERTIFICATIONS should be simple strings like "Python for Beginners - Newton School (2024)"
    16. Do NOT create experience entries for activities that are not professional work
    17. Personal activities, sports, competitions, olympiads should be in achievements, not experience

    Resume Sections:
    CONTACT INFORMATION:
Mohit Kourav
LinkedIn Github HackerEarth CodeChef Codeforces Leetcode

SUMMARY:
Aspiring Computer Science Engineer specializing in AI & ML, pursuing B.Tech from Newton School of Technology (ADYPU).
Passionate about building intelligent solutions and solving real-world problems with programming, data analysis, and machine
learning.

EDUCATION:
Bachelor of Technology (Computer Science) 2024 - 2028
Newton School Of Technology, ADYPU University Grade: 7.18/10.0
Intermediate (Class XII) 2021 - 2022
Maharastra High School Grade: 78.4%
Matriculation (Class X) 2019 - 2020
Dream India School Grade: 89.0%

SKILLS:
Computer Languages: JavaScript, CSS, HTML, Python
Software Packages: Excel, React
Soft Skills: Presentation Skills, Responsibility, Critical Thinking, Teamwork, Creativity, Research, Decision-making, Time
management, Team Building, Leadership
Others: Graphic Design
EXTRA-CURRICULAR ACTIVITIES
Led student councils, managed events, and represented students as Head Boy.
Hosted multiple school functions, showcasing coordination skills.
Contributed to community service initiatives as a social work volunteer.
Participated in inter-school competitions, enhancing teamwork and interpersonal skills.
Engaged in coding challenges and AI/ML workshops to enhance technical skills.

PROJECTS:
Statistics Calculator , ( Github ) ( Demo ) March 2025
Purpose : A tool for calculating basic statistical measures (Mean, Median, Mode) based on user-input numbers.
User Interface : Simple and interactive design for entering numbers and viewing results.
Features :
{ Mean Calculation : Computes the average of entered numbers.
{ Median Calculation : Finds the middle value of sorted numbers.
{ Mode Calculation : Identies the most frequent number(s).
{ Responsive Design : Optimized for both desktop and mobile devices.
Technology Stack : HTML, CSS, JavaScript.
Natural Remedies for a Healthy Life , ( Github ) ( Demo ) November 2024
Objective: Developed a static web application oering natural remedies and wellness tips with a user-friendly and intuitive
interface.
Findings:
Users preferred a clean, minimalistic design for quick access to information.
Responsive design was essential for a seamless experience across devices.
Skills Applied:
HTML: Structured web pages.
CSS: Styled and designed a responsive layout.
Responsive Web Design: Ensured compatibility across desktops, tablets, and mobile devices.
Tools Used:
Visual Studio Code: For development.
Netlify: For deployment and hosting.
Chrome DevTools: For debugging and responsiveness testing.
Impact: Delivered an accessible platform promoting health awareness while enhancing web development and UI/UX design
skills.

CERTIFICATIONS:
ChatGPT Prompt Engineering for Developers , DeepLearning.Ai ( Link ) February 2025
Successfully completed the "ChatGPT Prompt Engineering for Developers" course, gaining expertise in designing eective
prompts to optimize AI interactions.
Generative AI for Everyone , DeepLearning.Ai ( Link ) February 2025
Gained a comprehensive understanding of generative AI, its applications, ethical implications, and practical use cases.
AI For Everyone , DeepLearning.ai ( Link ) January 2025
Successfully completed "AI For Everyone" course, gaining a strong understanding of articial intelligence concepts, including
its societal impact, business applications, and fundamental machine learning techniques.



    IMPORTANT: Return the JSON object now (no markdown, no code blocks, just the JSON):
    
----------------------------------------

[RESPONSE]
Length: 3979 characters
----------------------------------------
```json
{
    "name": "Mohit Kourav",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School Of Technology, ADYPU University",
            "year": "2024 - 2028"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Maharastra High School",
            "year": "2021 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Dream India School",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "Excel",
        "React",
        "Presentation Skills",
        "Responsibility",
        "Critical Thinking",
        "Teamwork",
        "Creativity",
        "Research",
        "Decision-making",
        "Time management",
        "Team Building",
        "Leadership",
        "Graphic Design"
    ],
    "experience": [
        {
            "company_name": null,
            "role": null,
            "duration": null,
            "key_responsibilities": "No experience entries found"
        }
    ],
    "projects": [
        {
            "name": "Statistics Calculator",
            "description": "A tool for calculating basic statistical measures (Mean, Median, Mode) based on user-input numbers. Simple and interactive design for entering numbers and viewing results. Mean Calculation : Computes the average of entered numbers. Median Calculation : Finds the middle value of sorted numbers. Mode Calculation : Identiﬁes the most frequent number(s). Responsive Design : Optimized for both desktop and mobile devices. Technology Stack : HTML, CSS, JavaScript."
        },
        {
            "name": "Natural Remedies for a Healthy Life",
            "description": "Developed a static web application oﬀering natural remedies and wellness tips with a user-friendly and intuitive interface. Users preferred a clean, minimalistic design for quick access to information. Responsive design was essential for a seamless experience across devices. Skills Applied: HTML: Structured web pages. CSS: Styled and designed a responsive layout. Responsive Web Design: Ensured compatibility across desktops, tablets, and mobile devices. Tools Used: Visual Studio Code: For development. Netlify: For deployment and hosting. Chrome DevTools: For debugging and responsiveness testing. Impact: Delivered an accessible platform promoting health awareness while enhancing web development and UI/UX design skills."
        }
    ],
    "certifications": [
        "ChatGPT Prompt Engineering for Developers, DeepLearning.Ai ( Link ) February 2025",
        "Generative AI for Everyone, DeepLearning.Ai ( Link ) February 2025",
        "AI For Everyone, DeepLearning.ai ( Link ) January 2025"
    ],
    "domain_of_interest": [
        "AI & ML"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Led student councils, managed events, and represented students as Head Boy.",
        "Hosted multiple school functions, showcasing coordination skills.",
        "Contributed to community service initiatives as a social work volunteer.",
        "Participated in inter-school competitions, enhancing teamwork and interpersonal skills.",
        "Engaged in coding challenges and AI/ML workshops to enhance technical skills."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Aspiring Computer Science Engineer specializing in AI & ML, pursuing B.Tech from Newton School of Technology (ADYPU). Passionate about building intelligent solutions and solving real-world problems with programming, data analysis, and machine learning.",
    "personal_projects": [],
    "social_media": [
        "Github",
        "HackerEarth",
        "CodeChef",
        "Codeforces",
        "Leetcode"
    ]
}
```
----------------------------------------

================================================================================