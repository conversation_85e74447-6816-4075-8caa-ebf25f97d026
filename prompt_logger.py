"""
Prompt Logger Module

This module provides comprehensive logging functionality for LLM calls, including:
- Prompt logging with context information
- Response logging with metadata
- Organized file structure for easy debugging
- Automatic cleanup and management

File naming convention:
{timestamp}_{endpoint}_{context}_{call_type}.txt

Where:
- timestamp: YYYYMMDD_HHMMSS format
- endpoint: API endpoint name (resume, jd_parser, generate, etc.)
- context: Additional context (filename, operation type, etc.)
- call_type: Type of LLM call (main, repair, image_extract, etc.)
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import re

# Configure logger for this module
logger = logging.getLogger("prompt_logger")

# Base directory for prompt logs
PROMPT_LOGS_DIR = "prompt_logs"

# Subdirectories for different types of logs
SUBDIRS = {
    "resume": "resume_parsing",
    "jd": "job_description_parsing", 
    "generate": "general_generation",
    "image": "image_processing",
    "repair": "json_repair",
    "questions": "question_generation",
    "intervet": "interview_analysis",
    "interfix": "call_summary_processing"
}

def ensure_log_directories():
    """Ensure all necessary log directories exist."""
    try:
        # Create main directory
        os.makedirs(PROMPT_LOGS_DIR, exist_ok=True)
        
        # Create subdirectories
        for subdir in SUBDIRS.values():
            full_path = os.path.join(PROMPT_LOGS_DIR, subdir)
            os.makedirs(full_path, exist_ok=True)
            
        logger.debug(f"Ensured prompt log directories exist in {PROMPT_LOGS_DIR}")
    except Exception as e:
        logger.error(f"Failed to create prompt log directories: {e}")

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to remove invalid characters."""
    if not filename:
        return "unknown"
    
    # Remove file extension and path
    filename = os.path.splitext(os.path.basename(filename))[0]
    
    # Replace invalid characters with underscores
    filename = re.sub(r'[<>:"/\\|?*\s]', '_', filename)
    
    # Remove consecutive underscores
    filename = re.sub(r'_+', '_', filename)
    
    # Limit length
    if len(filename) > 50:
        filename = filename[:50]
    
    # Remove leading/trailing underscores
    filename = filename.strip('_')
    
    return filename if filename else "unknown"

def get_log_subdir(endpoint: str, call_type: str = "main") -> str:
    """Determine the appropriate subdirectory for the log."""
    # Map endpoints to subdirectories
    if endpoint.startswith("/resume") or "resume" in endpoint:
        return SUBDIRS["resume"]
    elif endpoint.startswith("/jd") or "jd" in endpoint:
        return SUBDIRS["jd"]
    elif endpoint.startswith("/generate") or "generate" in endpoint:
        return SUBDIRS["generate"]
    elif endpoint.startswith("/image") or "image" in call_type:
        return SUBDIRS["image"]
    elif "repair" in call_type:
        return SUBDIRS["repair"]
    elif "question" in endpoint or "question" in call_type:
        return SUBDIRS["questions"]
    elif "intervet" in endpoint:
        return SUBDIRS["intervet"]
    elif "interfix" in endpoint:
        return SUBDIRS["interfix"]
    else:
        return SUBDIRS["generate"]  # Default fallback

def generate_log_filename(endpoint: str, context: str = None, call_type: str = "main") -> str:
    """Generate a standardized filename for the log."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
    
    # Clean endpoint name
    endpoint_clean = endpoint.replace("/", "").replace("-", "_")
    if not endpoint_clean:
        endpoint_clean = "unknown"
    
    # Clean context
    context_clean = sanitize_filename(context) if context else "no_context"
    
    # Clean call type
    call_type_clean = sanitize_filename(call_type)
    
    filename = f"{timestamp}_{endpoint_clean}_{context_clean}_{call_type_clean}.txt"
    return filename

def log_llm_call(
    prompt: str,
    response: str,
    model_name: str,
    endpoint: str = "unknown",
    context: str = None,
    call_type: str = "main",
    metadata: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
) -> Optional[str]:
    """
    Log an LLM call with all relevant information.
    
    Args:
        prompt: The prompt sent to the LLM
        response: The response received from the LLM
        model_name: Name of the model used
        endpoint: API endpoint that triggered this call
        context: Additional context (e.g., filename, operation type)
        call_type: Type of call (main, repair, image_extract, etc.)
        metadata: Additional metadata to include
        error: Error message if the call failed
        
    Returns:
        Path to the created log file, or None if logging failed
    """
    try:
        # Ensure directories exist
        ensure_log_directories()
        
        # Generate filename and path
        filename = generate_log_filename(endpoint, context, call_type)
        subdir = get_log_subdir(endpoint, call_type)
        log_path = os.path.join(PROMPT_LOGS_DIR, subdir, filename)
        
        # Prepare log content
        log_content = []
        log_content.append("=" * 80)
        log_content.append(f"LLM CALL LOG - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        log_content.append("=" * 80)
        log_content.append("")
        
        # Basic information
        log_content.append("[CALL INFORMATION]")
        log_content.append(f"Endpoint: {endpoint}")
        log_content.append(f"Context: {context or 'N/A'}")
        log_content.append(f"Call Type: {call_type}")
        log_content.append(f"Model: {model_name}")
        log_content.append(f"Timestamp: {datetime.now().isoformat()}")
        
        # Add metadata if provided
        if metadata:
            log_content.append(f"Metadata: {json.dumps(metadata, indent=2)}")
        
        log_content.append("")
        
        # Error information (if any)
        if error:
            log_content.append("[ERROR]")
            log_content.append(f"Error: {error}")
            log_content.append("")
        
        # Prompt section
        log_content.append("[PROMPT]")
        log_content.append(f"Length: {len(prompt)} characters")
        log_content.append("-" * 40)
        log_content.append(prompt)
        log_content.append("-" * 40)
        log_content.append("")
        
        # Response section
        log_content.append("[RESPONSE]")
        if response:
            log_content.append(f"Length: {len(response)} characters")
            log_content.append("-" * 40)
            log_content.append(response)
            log_content.append("-" * 40)
        else:
            log_content.append("No response received")
        
        log_content.append("")
        log_content.append("=" * 80)
        
        # Write to file
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(log_content))
        
        logger.debug(f"Logged LLM call to: {log_path}")
        return log_path
        
    except Exception as e:
        logger.error(f"Failed to log LLM call: {e}")
        return None

def cleanup_old_logs(days_to_keep: int = 7) -> int:
    """
    Clean up old log files to prevent disk space issues.
    
    Args:
        days_to_keep: Number of days of logs to keep
        
    Returns:
        Number of files deleted
    """
    try:
        if not os.path.exists(PROMPT_LOGS_DIR):
            return 0
        
        import time
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
        deleted_count = 0
        
        for root, dirs, files in os.walk(PROMPT_LOGS_DIR):
            for file in files:
                if file.endswith('.txt'):
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                        except Exception as e:
                            logger.warning(f"Failed to delete old log file {file_path}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} old log files (older than {days_to_keep} days)")
        return deleted_count
        
    except Exception as e:
        logger.error(f"Failed to cleanup old logs: {e}")
        return 0

def get_log_stats() -> Dict[str, Any]:
    """Get statistics about the prompt logs."""
    try:
        if not os.path.exists(PROMPT_LOGS_DIR):
            return {"total_files": 0, "total_size": 0, "subdirs": {}}
        
        stats = {
            "total_files": 0,
            "total_size": 0,
            "subdirs": {}
        }
        
        for root, dirs, files in os.walk(PROMPT_LOGS_DIR):
            subdir_name = os.path.relpath(root, PROMPT_LOGS_DIR)
            if subdir_name == ".":
                subdir_name = "root"
            
            subdir_files = 0
            subdir_size = 0
            
            for file in files:
                if file.endswith('.txt'):
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    subdir_files += 1
                    subdir_size += file_size
            
            if subdir_files > 0:
                stats["subdirs"][subdir_name] = {
                    "files": subdir_files,
                    "size": subdir_size
                }
                stats["total_files"] += subdir_files
                stats["total_size"] += subdir_size
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get log stats: {e}")
        return {"error": str(e)}
