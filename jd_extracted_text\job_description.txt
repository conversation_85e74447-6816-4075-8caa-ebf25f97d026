# Extracted Text Debug File
# Source File: job_description.docx
# Context: jd
# Extraction Method: docx_text
# Timestamp: 2025-06-09 14:03:32
# Text Length: 2999 characters
# ================================================

SENIOR SOFTWARE ENGINEER
TechCorp Solutions | Bangalore, India | Full-time | Remote/Hybrid

COMPANY OVERVIEW
TechCorp Solutions is a leading technology company specializing in AI-powered 
enterprise solutions. We serve Fortune 500 companies worldwide and are committed 
to innovation and excellence.

JOB DESCRIPTION
We are seeking a talented Senior Software Engineer to join our growing engineering 
team. The ideal candidate will have strong experience in full-stack development, 
cloud technologies, and a passion for building scalable systems.

KEY RESPONSIBILITIES
• Design and develop scalable web applications using modern technologies
• Lead technical discussions and architectural decisions
• Mentor junior developers and conduct code reviews
• Collaborate with product managers and designers on feature development
• Implement best practices for testing, deployment, and monitoring
• Participate in on-call rotation for production support
• Contribute to technical documentation and knowledge sharing

REQUIRED QUALIFICATIONS
• Bachelor's degree in Computer Science or related field
• 5+ years of experience in software development
• Strong proficiency in Python, JavaScript, or Java
• Experience with React, Node.js, or similar web frameworks
• Knowledge of cloud platforms (AWS, Azure, or GCP)
• Experience with databases (SQL and NoSQL)
• Understanding of microservices architecture
• Familiarity with DevOps practices and CI/CD pipelines
• Strong problem-solving and communication skills

PREFERRED QUALIFICATIONS
• Master's degree in Computer Science or related field
• Experience with machine learning and data science
• Knowledge of containerization (Docker, Kubernetes)
• Experience with message queues and event-driven architecture
• Contributions to open-source projects
• Previous experience in a leadership or mentoring role
• Familiarity with Agile development methodologies

TECHNICAL SKILLS
Required: Python, JavaScript, React, Node.js, AWS, SQL, Git
Preferred: TensorFlow, Docker, Kubernetes, Redis, GraphQL, TypeScript

WHAT WE OFFER
• Competitive salary range: ₹25-35 LPA
• Comprehensive health insurance for you and your family
• Flexible working hours and remote work options
• Professional development budget of ₹50,000 annually
• Stock options and performance bonuses
• Modern office with free meals and snacks
• Collaborative and inclusive work environment
• Opportunities for international travel and conferences

EXPERIENCE LEVEL
Senior Level (5-8 years)

EDUCATION REQUIREMENTS
• Bachelor's degree in Computer Science, Engineering, or related field
• Master's degree preferred but not required

APPLICATION PROCESS
Please submit your resume along with a cover letter explaining why you're 
interested in this role. Include links to your GitHub profile and any 
relevant projects or portfolios.

LOCATION
Bangalore, India (Hybrid - 3 days in office, 2 days remote)

EMPLOYMENT TYPE
Full-time, Permanent

POSTED DATE
June 9, 2025

APPLICATION DEADLINE
July 15, 2025