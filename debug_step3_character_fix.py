#!/usr/bin/env python3
"""
STEP 3: Test the exact failing JSON with character replacement to achieve 100% success.
"""

import json

def test_character_replacement():
    """Test the exact JSON that's failing with comprehensive character replacement."""
    
    print("🔍 STEP 3: Testing Character Replacement")
    print("=" * 60)
    
    # This is the exact clean JSON from the repair log (lines 29-113) that should work but doesn't
    failing_json = '''{
    "name": "Meesala Sree Sai <PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Sri Chaitanya Junior College",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Kendriya Vidyalaya No-1, Air Force Academy",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "Mongo DB",
        "Django",
        "Express JS",
        "NodeJS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "Hadoop HDFS",
        "Data Structure"
    ],
    "experience": [
        {
            "company_name": "Spectacom Global, Gurugram, Haryana",
            "role": "SDE INTERN",
            "duration": "January 2025 - Present",
            "key_responsibilities": "Developed a leaderboard system in Django, supporting regional and individual rankings.\\nIntegrated detailed participant timing records with numerical insights for performance tracking.\\nOptimized Django ORM queries for ecient leaderboard updates and retrieval."
        }
    ],
    "projects": [
        {
            "name": "Laundry Pro",
            "description": "Laundry Pro simplies laundry services with an ecient platform for students to place and track orders\\nTech Stack: Built with Node.js, Express, Prisma, SQL.\\nFeatures:\\n{Secure login for students and admins.\\n{Admin tools for order status updates and record management."
        },
        {
            "name": "Fabrix",
            "description": "Developed a dynamic, simulated e-commerce platform with secure user authentication and product\\nbrowsing using dummy data for a modern, responsive shopping experience.\\nTech Stack: Built with React ,Tailwind CSS , and integrated Razorpay for secure\\ncheckout."
        },
        {
            "name": "Crypto - manager",
            "description": "Created a web application for managing and tracking cryptocurrency investments with real-time data and\\na responsive, interactive user interface.\\nTech Stack: Built with HTML ,CSS and JavaScript .\\nFeatures:\\n{Real-time API integration to fetch live cryptocurrency data and market trends.\\n{Simulated trading for buying, selling, and holding cryptocurrencies in a risk-free environment."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Rajya Puraskar"
    ],
    "domain_of_interest": [
        "Data Structure"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Ranked in the top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology.",
        "Qualied for the Semi-Finals inBadminton at the University Sports Fest."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Full Stack Developer skilled in React, Tailwind CSS, Node.js, Express, Django, and MySQL, with experience in scalable APIs\\nand dynamic UIs. Passionate about learning and building high-performing web applications.",
    "personal_projects": [],
    "social_media": [
        "LinkedIn",
        "Github",
        "Leetcode"
    ]
}'''

    print(f"📄 Testing JSON with special characters:")
    print(f"Length: {len(failing_json)} characters")
    
    # Test 1: Try parsing as-is (should fail)
    print(f"\n🔧 Test 1: Parse as-is (should fail)")
    try:
        data = json.loads(failing_json)
        print("✅ UNEXPECTED: JSON parsed without character replacement!")
        return True, "No character replacement needed"
    except json.JSONDecodeError as e:
        print(f"❌ EXPECTED: JSON parsing failed: {e}")
        print(f"Error at position: {e.pos}")
        if e.pos < len(failing_json):
            char_at_error = failing_json[e.pos]
            print(f"Character at error: '{char_at_error}' (ord: {ord(char_at_error)})")
            context = failing_json[max(0, e.pos-30):e.pos+30]
            print(f"Context: {repr(context)}")
    
    # Test 2: Apply character replacement
    print(f"\n🔧 Test 2: Apply character replacement")
    fixed_json = failing_json
    
    # Comprehensive character replacements
    char_replacements = {
        # Common OCR errors found in the logs
        'ecient': 'efficient',
        'simplies': 'simplifies', 
        'Qualied': 'Qualified',
        'inBadminton': 'in Badminton',
        'inScouts': 'in Scouts',
        'dierent': 'different',
        # Ligatures
        'ﬁ': 'fi', 'ﬂ': 'fl', 'ﬃ': 'ffi', 'ﬄ': 'ffl', 'ﬀ': 'ff',
        'ﬆ': 'st', 'ﬅ': 'ft',
        # Quotes
        '"': '"', '"': '"', ''': "'", ''': "'",
        # Dashes
        '–': '-', '—': '-', '―': '-', '‒': '-', '−': '-',
        # Accented characters
        'Ö': 'O', 'ö': 'o', 'Ü': 'U', 'ü': 'u', 'Ä': 'A', 'ä': 'a',
        'É': 'E', 'é': 'e', 'È': 'E', 'è': 'e', 'Ê': 'E', 'ê': 'e',
        'Á': 'A', 'á': 'a', 'À': 'A', 'à': 'a', 'Â': 'A', 'â': 'a',
        'Í': 'I', 'í': 'i', 'Ì': 'I', 'ì': 'i', 'Î': 'I', 'î': 'i',
        'Ó': 'O', 'ó': 'o', 'Ò': 'O', 'ò': 'o', 'Ô': 'O', 'ô': 'o',
        'Ú': 'U', 'ú': 'u', 'Ù': 'U', 'ù': 'u', 'Û': 'U', 'û': 'u',
        'Ñ': 'N', 'ñ': 'n', 'Ç': 'C', 'ç': 'c',
        # Other symbols
        '…': '...', '€': 'EUR', '£': 'GBP', '¢': 'cents',
        '©': '(c)', '®': '(r)', '™': '(tm)', '°': ' degrees',
        '±': '+/-', '×': 'x', '÷': '/', '≤': '<=', '≥': '>=', '≠': '!=',
        '≈': '~=', '∞': 'infinity', '∑': 'sum', '∏': 'product',
        # Greek letters
        'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta',
        'π': 'pi', 'σ': 'sigma', 'μ': 'mu', 'λ': 'lambda'
    }
    
    replacements_made = []
    for old_char, new_char in char_replacements.items():
        if old_char in fixed_json:
            fixed_json = fixed_json.replace(old_char, new_char)
            replacements_made.append(f"'{old_char}' → '{new_char}'")
    
    if replacements_made:
        print(f"Applied {len(replacements_made)} character replacements:")
        for replacement in replacements_made[:5]:  # Show first 5
            print(f"   {replacement}")
        if len(replacements_made) > 5:
            print(f"   ... and {len(replacements_made) - 5} more")
    else:
        print("No character replacements needed")
    
    try:
        data = json.loads(fixed_json)
        print("✅ SUCCESS: JSON parsing worked after character replacement!")
        print(f"Name: {data.get('name')}")
        print(f"Education: {len(data.get('education', []))} entries")
        print(f"Skills: {len(data.get('skills', []))} items")
        print(f"Experience: {len(data.get('experience', []))} entries")
        print(f"Projects: {len(data.get('projects', []))} items")
        return True, "Character replacement successful"
    except json.JSONDecodeError as e:
        print(f"❌ FAILED: JSON parsing still failed after character replacement: {e}")
        print(f"Error at position: {e.pos}")
        if e.pos < len(fixed_json):
            char_at_error = fixed_json[e.pos]
            print(f"Character at error: '{char_at_error}' (ord: {ord(char_at_error)})")
            context = fixed_json[max(0, e.pos-30):e.pos+30]
            print(f"Context: {repr(context)}")
        return False, f"Still failing: {e}"

if __name__ == "__main__":
    print("🐛 SYSTEMATIC DEBUGGING - STEP 3")
    print()
    
    success, result = test_character_replacement()
    
    print(f"\n{'='*60}")
    print("📊 STEP 3 RESULTS")
    print(f"{'='*60}")
    
    if success:
        print("🎉 SUCCESS: Character replacement fixes the JSON parsing!")
        print("✅ We now know exactly what needs to be fixed")
        print("✅ The solution is: Nuclear cleanup + Character replacement")
        print("\n💡 Next steps:")
        print("1. Add character replacement back to the nuclear cleanup")
        print("2. Apply it to both self-healing and main parsing")
        print("3. Test with all problematic resumes")
        print("4. Verify 100% success rate")
    else:
        print("❌ FAILED: Character replacement didn't fix the issue")
        print(f"Result: {result}")
        print("\n💡 Next steps:")
        print("1. Investigate the specific error above")
        print("2. Add more character replacements if needed")
        print("3. Consider alternative approaches")
        print("4. Debug further until 100% success")
