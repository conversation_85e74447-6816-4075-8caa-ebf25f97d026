{"event": "session_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "timestamp": "2025-06-18T13:15:19.730668", "message": "New API session started"}
{"event": "request_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "00753236-e61d-4fe1-96e5-f3a3105ec7b3", "endpoint": "/docs", "timestamp": "2025-06-18T13:21:03.238601", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "00753236-e61d-4fe1-96e5-f3a3105ec7b3", "endpoint": "/docs", "timestamp": "2025-06-18T13:21:03.238601", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc6eca5d-5579-4c8f-a29c-1a303b142a46", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:21:03.332645", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc6eca5d-5579-4c8f-a29c-1a303b142a46", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:21:03.342460", "total_time_seconds": 0.009814977645874023, "status_code": 200, "message": "Request completed in 0.0098s with status 200"}
{"event": "request_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.698680", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.734790", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.735791", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.735791", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.735791", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.735791", "file_processing_time": 0.03105926513671875, "message": "Custom metric: file_processing_time=0.03105926513671875"}
{"event": "request_complete", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "ad4be84a-0c8e-4177-b745-a76596b5aa62", "endpoint": "/section3", "timestamp": "2025-06-18T13:21:14.750470", "total_time_seconds": 0.051789283752441406, "status_code": 200, "message": "Request completed in 0.0518s with status 200"}
{"event": "request_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.524649", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.911917", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.911917", "file_size_bytes": 118090, "message": "Custom metric: file_size_bytes=118090"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.911917", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.912916", "extracted_text_length": 22462, "message": "Custom metric: extracted_text_length=22462"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.912916", "file_processing_time": 0.36614084243774414, "message": "Custom metric: file_processing_time=0.36614084243774414"}
{"event": "request_complete", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "8e1023ea-9c29-41de-9ff5-92aef1336c56", "endpoint": "/section3", "timestamp": "2025-06-18T13:22:19.948169", "total_time_seconds": 0.423520565032959, "status_code": 200, "message": "Request completed in 0.4235s with status 200"}
{"event": "request_start", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.767972", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.883193", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.883193", "file_size_bytes": 709602, "message": "Custom metric: file_size_bytes=709602"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.883193", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.883193", "extracted_text_length": 3123, "message": "Custom metric: extracted_text_length=3123"}
{"event": "custom_metric", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.883193", "file_processing_time": 0.08752775192260742, "message": "Custom metric: file_processing_time=0.08752775192260742"}
{"event": "request_complete", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "request_id": "cc20ccd6-e5d4-4251-9cef-ccf9a6c58975", "endpoint": "/section3", "timestamp": "2025-06-18T13:23:49.890190", "total_time_seconds": 0.12221813201904297, "status_code": 200, "message": "Request completed in 0.1222s with status 200"}
{"event": "session_end", "session_id": "082effea-754b-4f89-b261-dbb03fb8c2b6", "timestamp": "2025-06-18T13:25:50.054003", "message": "API session ended"}
