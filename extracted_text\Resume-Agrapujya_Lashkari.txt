# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON><PERSON><PERSON><PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 16:52:12
# Text Length: 2526 characters
# ================================================

Agrapujya <PERSON>kari
LinkedIn Github Leetcode
PROFESSIONAL SUMMARY
Enthusiastic and dedicated Full Stack Developer with a passion for learning and applying new technologies, ready to contribute
to creating comprehensive and high-performing web applications.
EDUCATION
Bachelor of Technology (Articial intelligence) 2023 - 2027
Newton School Of Technology, Rishihood University Grade: 8.7/10.0
Intermediate (Class XII) 2020 - 2021
Shri Guru Tegh Bahadur Academy Grade: 83.2%
Matriculation (Class X) 2018 - 2019
Shri Guru Tegh Bahadur Academy Grade: 89.4%
INTERNSHIPS
Full-Stack Developer June 2024 - July 2024
Rewardwise Remote
Developed a fully functional website using Next.js for fast, dynamic pages.
Designed and implemented an intuitive admin dashboard using React.js for management.
Integrated and managed APIs to retrieve, update, and display company contacts.
Containerized the admin dashboard with <PERSON><PERSON> for consistent development and deployment.
Improved communication skills by collaborating with teams and presenting technical solutions.
PROJECTS
Note Taking App , ( Github ) ( Demo ) November 2024
Tech Stack: React.js, Node.js, Redux, MongoDB, Express.js
Features: Authentication, Create-Read-Update-Delete Notes, pin important Notes
Description: This note-taking app provides a streamlined experience for users to securely manage their notes. Users can
create accounts through a signup process and securely log in to access their personal notes. The app allows users to save
notes, edit content, delete entries, and pin important notes for quick access. With its user-friendly design, the app is a
perfect tool for staying organized, capturing ideas, and prioritizing important tasks.
Blog App , ( Github ) ( Demo ) September 2024
Tech Stack :HTML, CSS, Javascript, React-vite
Features : Searching, Sorting, Pagination and Authentication
Description : This blog app features user authentication, allowing secure logins and personalized access. It includes
search, sorting, and pagination for ecient content navigation. Upcoming features include post creation, liking, and a
favorites section for users.
SKILLS
Computer Languages: Java, C, JavaScript, CSS, HTML, Python, C++
Software Packages: React, MySQL, Express JS, NodeJS
Additional Courses: Data Structure
Soft Skills: Communication Skills, Critical Thinking, Decision-making, Time management, Leadership
EXTRA-CURRICULAR ACTIVITIES
Co-ordinator of Tech Quiz in University Fest.
Ranked 2nd in StealthFire Hackathon organised in the college.
