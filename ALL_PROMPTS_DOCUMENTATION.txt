================================================================================
COMPLETE PROMPTS DOCUMENTATION - GEMMA API ENDPOINTS
================================================================================
Generated: 2025-06-17
Model: Gemma 3:4b via Ollama
Application: Resume/Document Processing API

This document contains ALL prompts used across ALL endpoints in the application.

================================================================================
1. RESUME PARSING ENDPOINT - /resume
================================================================================

MAIN RESUME PARSING PROMPT:
----------------------------
You are an expert resume parser. Extract information from this resume text and return it in valid JSON format.

Extract the following information:
- name: Full name of the person
- email: Email address
- phone: Phone number
- location: Location/address
- summary: Professional summary or objective
- experience: Array of work experiences with company_name, role, duration, description
- education: Array of education with degree, institution, year, details
- skills: Array of skills (technical and soft skills)
- projects: Array of projects with name, description, technologies
- certifications: Array of certifications with name, issuer, year
- achievements: Array of achievements or awards
- languages: Array of languages with proficiency levels

Return ONLY valid JSON. Do not include any explanations or additional text.

Resume text:
{extracted_text}

Important: 
- Extract only information that is clearly present in the resume
- Use null for missing information
- Ensure the JSON is properly formatted and valid
- Do not hallucinate or add information not in the resume

JSON REPAIR PROMPT (when JSON is malformed):
--------------------------------------------
The following JSON response is malformed and needs to be fixed. Please return a corrected, valid JSON response:

{malformed_json}

Requirements:
- Fix any syntax errors (missing commas, quotes, brackets)
- Ensure all strings are properly quoted
- Maintain the original data structure and content
- Return ONLY the corrected JSON, no explanations

================================================================================
2. JOB DESCRIPTION PARSING ENDPOINT - /jd_parser
================================================================================

JOB DESCRIPTION PARSING PROMPT:
-------------------------------
You are an expert job description analyzer. Extract information from this job description and return it in valid JSON format.

Extract the following information:
- job_title: The job title/position
- company_name: Company name (if mentioned)
- location: Job location
- job_type: Employment type (full-time, part-time, contract, etc.)
- experience_level: Required experience level
- salary_range: Salary information (if mentioned)
- job_summary: Brief summary of the role
- responsibilities: Array of key responsibilities
- requirements: Array of required qualifications/skills
- preferred_qualifications: Array of preferred qualifications
- benefits: Array of benefits offered
- technologies: Array of technologies/tools mentioned
- skills_required: Array of required skills
- education_requirements: Education requirements
- application_deadline: Application deadline (if mentioned)

Return ONLY valid JSON. Do not include any explanations or additional text.

Job description text:
{extracted_text}

Important:
- Extract only information that is clearly present in the job description
- Use null for missing information
- Ensure the JSON is properly formatted and valid
- Do not hallucinate or add information not in the job description

================================================================================
3. SECTION EXTRACTION ENDPOINTS - /section & /section2
================================================================================

3A. INDIVIDUAL SECTION EXTRACTION PROMPTS (/section):
-----------------------------------------------------

SUMMARY SECTION PROMPT:
Look for a section in the resume with headings like "SUMMARY", "PROFESSIONAL SUMMARY", "OBJECTIVE", "CAREER OBJECTIVE", "PROFILE", or similar.
Extract ONLY the text that appears directly under that specific section heading. This should be a brief overview or objective statement.
Do not include contact information, work experience, or other sections. Return exactly what is written under the summary section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

EDUCATION SECTION PROMPT:
Look for a section in the resume with headings like "EDUCATION", "ACADEMIC BACKGROUND", "QUALIFICATIONS", or "EDUCATIONAL QUALIFICATIONS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

EXPERIENCE SECTION PROMPT:
Look for a section in the resume with headings like "EXPERIENCE", "WORK EXPERIENCE", "PROFESSIONAL EXPERIENCE", "EMPLOYMENT HISTORY", "CAREER HISTORY", or "EMPLOYMENT".
Extract ONLY the text that appears directly under that specific section heading. This should include job titles, company names, dates, and job responsibilities.
Do not include information from other sections like achievements, projects, skills, or summary. Return exactly what is written under the work experience section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

SKILLS SECTION PROMPT:
Look for a section in the resume with headings like "SKILLS", "TECHNICAL SKILLS", "CORE COMPETENCIES", "TECHNOLOGIES", or "EXPERTISE".
For skills section only: Extract and compile ALL skills mentioned throughout the entire resume, including technical skills, programming languages, tools, frameworks, and soft skills.
Present them as a comprehensive list. This is the only section where you should gather information from the entire document.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

PROJECTS SECTION PROMPT:
Look for a section in the resume with headings like "PROJECTS", "PERSONAL PROJECTS", "ACADEMIC PROJECTS", or "KEY PROJECTS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

CERTIFICATIONS SECTION PROMPT:
Look for a section in the resume with headings like "CERTIFICATIONS", "CERTIFICATES", "LICENSES", "PROFESSIONAL CERTIFICATIONS", or "CREDENTIALS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

ACHIEVEMENTS SECTION PROMPT:
Look for a section in the resume with headings like "ACHIEVEMENTS", "AWARDS", "HONORS", "ACCOMPLISHMENTS", "RECOGNITION", or "ACCOLADES".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections like experience or projects.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

LANGUAGES SECTION PROMPT:
Look for a section in the resume with headings like "LANGUAGES", "LANGUAGE SKILLS", "LINGUISTIC ABILITIES", or "SPOKEN LANGUAGES".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content

3B. SINGLE CALL SECTION EXTRACTION PROMPT (/section2):
-------------------------------------------------------

COMPREHENSIVE SECTION EXTRACTION PROMPT:
You are an expert resume parser. Extract sections from this resume by finding section headings and copying the content that appears directly under each heading.

IMPORTANT EXTRACTION RULES:
1. Look for section headings (like "SUMMARY", "EDUCATION", "EXPERIENCE", etc.)
2. Extract ONLY the text that appears directly under each specific section heading
3. Do NOT mix content from different sections
4. Do NOT include information that appears under other section headings
5. For SKILLS section only: Compile ALL skills mentioned throughout the entire resume
6. Copy the content exactly as it appears, nothing more, nothing less

Return the response in this exact format:

[SUMMARY]
(Extract only from summary/objective/profile section or 'NOT_FOUND' if not present)

[EDUCATION]
(Extract only from education/academic section or 'NOT_FOUND' if not present)

[EXPERIENCE]
(Extract only from work experience/employment section - job titles, companies, dates, responsibilities or 'NOT_FOUND' if not present)

[SKILLS]
(Compile ALL skills from entire resume - this is the only exception to the rule)

[PROJECTS]
(Extract only from projects section or 'NOT_FOUND' if not present)

[CERTIFICATIONS]
(Extract only from certifications/licenses section or 'NOT_FOUND' if not present)

[ACHIEVEMENTS]
(Extract only from achievements/awards section or 'NOT_FOUND' if not present)

[LANGUAGES]
(Extract only from languages section or 'NOT_FOUND' if not present)

Resume Text:
{text}

Remember: Be literal and section-specific. If a project is listed under "ACHIEVEMENTS", do NOT include it in "PROJECTS". Only include content that appears directly under the relevant section heading.

================================================================================
4. QUESTION GENERATION ENDPOINTS - /jd_questions & /intervet
================================================================================

4A. JOB DESCRIPTION QUESTION GENERATION PROMPT (/jd_questions):
---------------------------------------------------------------

QUESTION GENERATION PROMPT (per category):
You are an expert interviewer. Based on this job description, generate {num_questions} high-quality interview questions for the "{category}" category.

Job Description:
{jd_text}

Requirements:
- Generate exactly {num_questions} questions
- Questions should be specific to the job requirements
- Make questions practical and relevant
- Avoid generic questions
- Focus on the "{category}" aspect
- Return only the questions, one per line
- Number each question (1., 2., 3., etc.)

Categories and their focus:
- Technical: Programming, tools, technologies, technical problem-solving
- Experience: Past work experience, achievements, challenges faced
- Behavioral: Soft skills, teamwork, leadership, communication
- Situational: Hypothetical scenarios, problem-solving approaches

4B. RESUME-JD MATCHING QUESTION GENERATION PROMPT (/intervet):
--------------------------------------------------------------

MATCHING QUESTION GENERATION PROMPT (per category):
You are an expert interviewer. Based on this resume and job description, generate {num_questions} targeted interview questions for the "{category}" category.

Resume:
{resume_text}

Job Description:
{jd_text}

Requirements:
- Generate exactly {num_questions} questions
- Questions should bridge the resume and job requirements
- Focus on gaps, strengths, and relevant experience
- Make questions specific to this candidate and role
- Avoid generic questions
- Focus on the "{category}" aspect
- Return only the questions, one per line
- Number each question (1., 2., 3., etc.)

Categories and their focus:
- Technical: Match technical skills from resume to job requirements
- Experience: Connect past experience to job responsibilities
- Behavioral: Assess soft skills relevant to the role
- Situational: Test problem-solving for job-specific scenarios

================================================================================
5. GENERAL GENERATION ENDPOINT - /generate
================================================================================

CONVERSATIONAL PROMPT:
{history}
User: {prompt}
Assistant:

Note: This endpoint uses the conversation history and user prompt directly without additional system prompts.

================================================================================
6. IMAGE PROCESSING ENDPOINT - /generate_image
================================================================================

IMAGE ANALYSIS PROMPT:
{prompt}

This document contains important text. Please extract ALL text content from this image.
Format your response as plain text only, preserving the structure as much as possible.

Note: The user prompt is combined with the image extraction instruction.

================================================================================
7. IMAGE TEXT EXTRACTION (Internal Function)
================================================================================

IMAGE TEXT EXTRACTION PROMPT:
This document contains important text. Please extract ALL text content from this image.
Format your response as plain text only, preserving the structure as much as possible.

Important:
- Extract ALL visible text
- Maintain the original structure and formatting as much as possible
- Include headers, bullet points, and any structured content
- Do not add any explanations or commentary
- Return only the extracted text

================================================================================
8. VAPI CALL SUMMARY PROCESSING - /interfix
================================================================================

VAPI SUMMARY PROCESSING PROMPT:
You are an expert HR assistant. Analyze this VAPI (Voice AI) call summary and extract key information about the candidate.

Call Summary:
{summary}

Extract and return the following information in a structured format:

**Candidate Information:**
- Notice Period: [Extract notice period mentioned]
- Salary Expectation: [Extract salary expectations]
- Reason for Job Change: [Extract reasons for changing jobs]
- Work Preference: [Extract preference for remote/office/hybrid]
- Availability: [Extract availability information]
- Key Skills Mentioned: [List any skills discussed]
- Experience Level: [Extract experience level if mentioned]
- Location Preference: [Extract location preferences]

**Call Quality:**
- Overall Assessment: [Brief assessment of the call]
- Candidate Engagement: [How engaged was the candidate]
- Areas of Interest: [What the candidate seemed most interested in]

**Next Steps:**
- Recommended Actions: [Suggest next steps based on the call]
- Red Flags: [Any concerns or red flags]
- Strengths: [Candidate's apparent strengths]

Provide a concise but comprehensive analysis based on the call summary provided.

================================================================================
9. JSON REPAIR PROMPTS (Used across multiple endpoints)
================================================================================

JSON REPAIR PROMPT:
The following JSON response is malformed and needs to be fixed. Please return a corrected, valid JSON response:

{malformed_json}

Requirements:
- Fix any syntax errors (missing commas, quotes, brackets)
- Ensure all strings are properly quoted
- Maintain the original data structure and content
- Return ONLY the corrected JSON, no explanations

================================================================================
10. PROMPT LOGGING METADATA
================================================================================

All prompts are logged with the following metadata:
- Endpoint: The API endpoint that triggered the call
- Context: Additional context (filename, operation type, etc.)
- Call Type: Type of call (main, repair, image_extract, etc.)
- Model Name: gemma3:4b
- Timestamp: ISO format timestamp
- Processing Time: Time taken for the call
- Confidence Score: Quality assessment of the response
- Token Usage: Prompt and response token counts

Conversation Logging:
- Multiple calls for same resume are saved in conversation folders
- Each call is logged individually with full prompt/response
- Final summary includes all extraction results
- Folder naming: {timestamp}_{resume_name}_{method}_conversation

================================================================================
END OF PROMPTS DOCUMENTATION
================================================================================

Total Endpoints with Prompts: 10
Total Prompt Variations: 15+
Model Used: Gemma 3:4b via Ollama
Last Updated: 2025-06-17

Note: All prompts are designed to work with the Gemma 3:4b model and may need
adjustment for other models. The prompts emphasize literal extraction and
boundary enforcement to prevent hallucinations and cross-contamination.
