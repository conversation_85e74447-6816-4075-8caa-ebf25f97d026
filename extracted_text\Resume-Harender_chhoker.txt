# Extracted Text Debug File
# Source File: Resume-Harender chhoker.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:49:55
# Text Length: 2308 characters
# ================================================

Harender Chhoker
LinkedIn Github CodeChef Leetcode
PROFESSIONAL SUMMARY
Passionate developer skilled in HTML, CSS, JavaScript, React.js, and Python. Strong problem-solving and algorithmic
thinking, with 40+ LeetCode problems solved and a 600+ CodeForces rating
EDUCATION
Bachelor of Technology (Articial intelligence) 2024 - 2028
Newton School of Technology , Rishihood University Grade: 7.0/10.0
Intermediate (Class XII) 2023 - 2024
St lukes sr sec school Grade: 83.2%
Matriculation (Class X) 2021 - 2022
RD public school Grade: 7.0/10.0
PROJECTS
Age Calculator , ( Github ) ( Demo ) March 2025
Tech Stack: HTML,CSS,Javascript
Description: An age calculator web application that uses HTML, CSS, and JavaScript to calculate and display a user's
age based on their birthdate.
Features: Features include input for birthdate, automatic age calculation, real-time display of age in years, months, and
days, and a user-friendly interface.
Calculator , ( Github ) ( Demo ) March 2025
Tech Stack: HTML,CSS,Javascript
Description: A simple web-based calculator built with HTML, CSS, and JavaScript that performs basic arithmetic
operations like addition, subtraction, multiplication, and division.
Features: Features include a simple user interface, support for basic arithmetic operations, and clear/reset functionality
for easy use.
Stopwatch , ( Github ) ( Demo ) March 2025
Tech Stack: HTML,CSS,React Js
Description: Built a dynamic and interactive stopwatch application using React, featuring start, stop, reset, and time
display functionality.
Features: Features include start, stop, and reset buttons, along with a real-time display of elapsed time.
Dive-into-creativity , ( Github ) ( Demo ) December 2024
Tech Stack: HTML,CSS
Description: Converted a Figma design into a fully responsive prototype using HTML and CSS, replicating the visual
and interactive elements.
Features: Features a clean, responsive layout with interactive elements, ensuring seamless user experience across dierent
devices.
SKILLS
Computer Languages: Python, JavaScript, CSS, HTML
Software Packages: Figma, Excel
Additional Courses: Data Structure
Soft Skills: Communication Skills, Teamwork, Creativity, Time management, Leadership, Written communication
Others: Social Media
EXTRA-CURRICULAR ACTIVITIES
Volleyball
