{"event": "session_start", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "timestamp": "2025-06-18T14:52:03.045963", "message": "New API session started"}
{"event": "request_start", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.086339", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.103404", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.104756", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.104756", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.104756", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:14.104756", "file_processing_time": 0.015001535415649414, "message": "Custom metric: file_processing_time=0.015001535415649414"}
{"event": "request_complete", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "request_id": "718dab2a-58d6-4d64-8edf-6b91145344fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:52:39.699423", "total_time_seconds": 25.613083600997925, "status_code": 200, "message": "Request completed in 25.6131s with status 200"}
{"event": "session_end", "session_id": "f4401b28-6ee1-41c1-98d4-105726198136", "timestamp": "2025-06-18T14:55:16.997310", "message": "API session ended"}
