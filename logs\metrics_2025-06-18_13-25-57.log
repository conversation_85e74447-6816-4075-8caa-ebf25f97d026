{"event": "session_start", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "timestamp": "2025-06-18T13:25:57.118686", "message": "New API session started"}
{"event": "request_start", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "7b7aebb8-fefd-4cd3-bd31-05b57ece5ee5", "endpoint": "/", "timestamp": "2025-06-18T13:25:58.549896", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "7b7aebb8-fefd-4cd3-bd31-05b57ece5ee5", "endpoint": "/", "timestamp": "2025-06-18T13:25:58.550896", "total_time_seconds": 0.0010001659393310547, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "f37d80f8-ef4a-4573-b68e-720a034d0a98", "endpoint": "/docs", "timestamp": "2025-06-18T13:26:01.042908", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "f37d80f8-ef4a-4573-b68e-720a034d0a98", "endpoint": "/docs", "timestamp": "2025-06-18T13:26:01.042908", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "fe207d5f-a1aa-4473-8534-2fa54dbc00b8", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:26:01.139246", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "request_id": "fe207d5f-a1aa-4473-8534-2fa54dbc00b8", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:26:01.150740", "total_time_seconds": 0.011493444442749023, "status_code": 200, "message": "Request completed in 0.0115s with status 200"}
{"event": "session_end", "session_id": "e8d43f7f-f907-4b4e-88bb-8965ecec912d", "timestamp": "2025-06-18T13:30:10.973819", "message": "API session ended"}
