#!/usr/bin/env python3
"""
Final test to understand why the JSON parsing is failing.
"""

import json

def test_exact_json_with_special_chars():
    """Test the exact JSON with special characters."""
    
    # Exact JSON from the log with special characters
    json_with_special_chars = '''{
    "name": "<PERSON><PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology , Rishihood University",
            "year": "2024 - 2028"
        }
    ],
    "skills": [
        "Python",
        "JavaScript"
    ],
    "projects": [
        {
            "name": "Calculator Project",
            "description": "Basic arithmetic (+, , Ö, ö), real-time display updates"
        }
    ],
    "certifications": [],
    "achievements": [],
    "summary": "Test summary"
}'''

    print("🧪 Testing JSON with Special Characters")
    print("=" * 60)
    
    print(f"📄 Original JSON:")
    print(f"Length: {len(json_with_special_chars)} characters")
    
    # Test 1: Try parsing as-is
    print(f"\n🔧 Test 1: Parse as-is")
    try:
        data = json.loads(json_with_special_chars)
        print("✅ SUCCESS: JSON parses correctly with special characters!")
        print(f"Name: {data['name']}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ FAILED: {e}")
        print(f"Error position: {e.pos}")
        if e.pos < len(json_with_special_chars):
            char_at_error = json_with_special_chars[e.pos]
            print(f"Character at error: '{char_at_error}' (ord: {ord(char_at_error)})")
            context = json_with_special_chars[max(0, e.pos-20):e.pos+20]
            print(f"Context: {repr(context)}")
    
    # Test 2: Character replacement
    print(f"\n🔧 Test 2: Character replacement")
    fixed_json = json_with_special_chars
    
    char_replacements = {
        'ﬁ': 'fi',
        'Ö': 'O',
        'ö': 'o',
    }
    
    for old_char, new_char in char_replacements.items():
        fixed_json = fixed_json.replace(old_char, new_char)
        print(f"Replaced '{old_char}' with '{new_char}'")
    
    try:
        data = json.loads(fixed_json)
        print("✅ SUCCESS: JSON parses after character replacement!")
        print(f"Name: {data['name']}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ FAILED: {e}")
        print(f"Error position: {e.pos}")
    
    # Test 3: ASCII encoding
    print(f"\n🔧 Test 3: ASCII encoding")
    try:
        ascii_json = json_with_special_chars.encode('ascii', 'ignore').decode('ascii')
        data = json.loads(ascii_json)
        print("✅ SUCCESS: JSON parses after ASCII encoding!")
        print(f"Name: {data['name']}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ FAILED: {e}")
    
    return False

def test_simple_json():
    """Test a simple JSON to make sure basic parsing works."""
    
    print(f"\n🧪 Testing Simple JSON")
    print("=" * 60)
    
    simple_json = '''{
    "name": "Test Name",
    "email": null,
    "skills": ["Python", "JavaScript"]
}'''
    
    try:
        data = json.loads(simple_json)
        print("✅ SUCCESS: Simple JSON parses correctly!")
        print(f"Name: {data['name']}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ FAILED: {e}")
        return False

def main():
    """Main test function."""
    
    print("🐛 Final JSON Parsing Debug")
    print()
    
    # Test simple JSON first
    simple_works = test_simple_json()
    
    # Test complex JSON with special characters
    complex_works = test_exact_json_with_special_chars()
    
    print(f"\n{'='*60}")
    print("📊 SUMMARY")
    print(f"{'='*60}")
    
    print(f"Simple JSON: {'✅ Works' if simple_works else '❌ Failed'}")
    print(f"Complex JSON: {'✅ Works' if complex_works else '❌ Failed'}")
    
    if simple_works and complex_works:
        print(f"\n🎉 JSON parsing is working correctly!")
        print("The issue must be elsewhere in the code pipeline")
    elif simple_works and not complex_works:
        print(f"\n⚠️ Special characters are causing JSON parsing to fail")
        print("The character replacement logic needs to be applied")
    else:
        print(f"\n❌ Basic JSON parsing is broken")
        print("There's a fundamental issue with the JSON library")

if __name__ == "__main__":
    main()
