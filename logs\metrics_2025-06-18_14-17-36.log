{"event": "session_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "timestamp": "2025-06-18T14:17:36.707603", "message": "New API session started"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "f4758323-8423-428f-9c01-e693cbbf63c8", "endpoint": "/", "timestamp": "2025-06-18T14:20:34.369387", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "f4758323-8423-428f-9c01-e693cbbf63c8", "endpoint": "/", "timestamp": "2025-06-18T14:20:34.369387", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.425237", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.442234", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.443235", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.443235", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.443235", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:20:36.443235", "file_processing_time": 0.012998104095458984, "message": "Custom metric: file_processing_time=0.012998104095458984"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4deb13d9-f99c-463c-8ce3-57d9fb20e78f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:05.275307", "total_time_seconds": 28.850069761276245, "status_code": 200, "message": "Request completed in 28.8501s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.349031", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.363030", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.363030", "file_size_bytes": 71498, "message": "Custom metric: file_size_bytes=71498"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.363030", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.363030", "extracted_text_length": 2507, "message": "Custom metric: extracted_text_length=2507"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:09.363030", "file_processing_time": 0.01199960708618164, "message": "Custom metric: file_processing_time=0.01199960708618164"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0da14bbb-9fd3-47c7-a7a5-cc6f4b35fc1e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:24.552510", "total_time_seconds": 15.203479290008545, "status_code": 200, "message": "Request completed in 15.2035s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.608430", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.646878", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.646878", "file_size_bytes": 96268, "message": "Custom metric: file_size_bytes=96268"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.646878", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.646878", "extracted_text_length": 4893, "message": "Custom metric: extracted_text_length=4893"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:21:28.646878", "file_processing_time": 0.03544759750366211, "message": "Custom metric: file_processing_time=0.03544759750366211"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0b3b74cd-fea5-41da-89f0-609e155bb7ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:02.206903", "total_time_seconds": 33.598472595214844, "status_code": 200, "message": "Request completed in 33.5985s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.268473", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.284491", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.284491", "file_size_bytes": 81582, "message": "Custom metric: file_size_bytes=81582"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.284491", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.284491", "extracted_text_length": 3080, "message": "Custom metric: extracted_text_length=3080"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:06.284491", "file_processing_time": 0.014021158218383789, "message": "Custom metric: file_processing_time=0.014021158218383789"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "16de02f6-4c82-46e0-bc27-b40062b6804e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:21.604266", "total_time_seconds": 15.335792779922485, "status_code": 200, "message": "Request completed in 15.3358s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.671627", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.685629", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.685629", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.685629", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.686628", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:25.686628", "file_processing_time": 0.012004852294921875, "message": "Custom metric: file_processing_time=0.012004852294921875"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "2ed369d3-e41c-4160-bcf9-a6d4d33e1028", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:53.549311", "total_time_seconds": 27.877684116363525, "status_code": 200, "message": "Request completed in 27.8777s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.592972", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.604970", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.604970", "file_size_bytes": 70963, "message": "Custom metric: file_size_bytes=70963"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.604970", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.606375", "extracted_text_length": 1872, "message": "Custom metric: extracted_text_length=1872"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:22:57.606375", "file_processing_time": 0.009999752044677734, "message": "Custom metric: file_processing_time=0.009999752044677734"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "c88b25e6-8982-4ff6-92b4-d11b58073fcf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:19.799978", "total_time_seconds": 22.207006216049194, "status_code": 200, "message": "Request completed in 22.2070s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.845065", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.861068", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.861068", "file_size_bytes": 72971, "message": "Custom metric: file_size_bytes=72971"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.861068", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.861068", "extracted_text_length": 3176, "message": "Custom metric: extracted_text_length=3176"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:23.861068", "file_processing_time": 0.01400446891784668, "message": "Custom metric: file_processing_time=0.01400446891784668"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "bce6b2eb-6f4a-4822-91f1-15f542c1c966", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:55.261545", "total_time_seconds": 31.416480779647827, "status_code": 200, "message": "Request completed in 31.4165s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.307302", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.348482", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.349483", "file_size_bytes": 76118, "message": "Custom metric: file_size_bytes=76118"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.349483", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.349483", "extracted_text_length": 3345, "message": "Custom metric: extracted_text_length=3345"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:23:59.349483", "file_processing_time": 0.03918004035949707, "message": "Custom metric: file_processing_time=0.03918004035949707"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "4d63fa64-5e5d-4a19-8b03-5b74c62e85dd", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:32.965770", "total_time_seconds": 33.65846800804138, "status_code": 200, "message": "Request completed in 33.6585s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.019892", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.035406", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.035406", "file_size_bytes": 81408, "message": "Custom metric: file_size_bytes=81408"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.035406", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.036409", "extracted_text_length": 2452, "message": "Custom metric: extracted_text_length=2452"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:24:37.036409", "file_processing_time": 0.01351618766784668, "message": "Custom metric: file_processing_time=0.01351618766784668"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "0c625e6a-a9d9-4fde-a8d3-cfd37c08401d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:05.984850", "total_time_seconds": 28.96495771408081, "status_code": 200, "message": "Request completed in 28.9650s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.061569", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.075569", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.075569", "file_size_bytes": 70825, "message": "Custom metric: file_size_bytes=70825"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.075569", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.075569", "extracted_text_length": 2308, "message": "Custom metric: extracted_text_length=2308"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:10.076570", "file_processing_time": 0.010998725891113281, "message": "Custom metric: file_processing_time=0.010998725891113281"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "717333e5-f729-4585-8ab6-fd6c5bf55eb2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:35.107043", "total_time_seconds": 25.0454740524292, "status_code": 200, "message": "Request completed in 25.0455s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.178940", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.193937", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.193937", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.195280", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.195280", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:25:39.195280", "file_processing_time": 0.01199793815612793, "message": "Custom metric: file_processing_time=0.01199793815612793"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "fce19329-e26b-45a3-a246-48cf2cd53de5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:07.150341", "total_time_seconds": 27.97140097618103, "status_code": 200, "message": "Request completed in 27.9714s with status 200"}
{"event": "request_start", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.215637", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.228654", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.228654", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.228654", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.228654", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:11.228654", "file_processing_time": 0.012000083923339844, "message": "Custom metric: file_processing_time=0.012000083923339844"}
{"event": "request_complete", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "request_id": "51481b32-a45a-4013-9f1e-1af57bbb254f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:26:39.309587", "total_time_seconds": 28.093950033187866, "status_code": 200, "message": "Request completed in 28.0940s with status 200"}
{"event": "session_end", "session_id": "de0aaa5b-7680-4717-82c6-dbf6d73d0def", "timestamp": "2025-06-18T14:27:36.366362", "message": "API session ended"}
