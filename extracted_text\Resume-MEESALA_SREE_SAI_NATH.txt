# Extracted Text Debug File
# Source File: Resume-MEESALA SREE SAI NATH.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 16:15:51
# Text Length: 2973 characters
# ================================================

Meesala Sree Sai Nath
LinkedIn Github Leetcode
PROFESSIONAL SUMMARY
Full Stack Developer skilled in React, Tailwind CSS, Node.js, Express, Django, and MySQL, with experience in scalable APIs
and dynamic UIs. Passionate about learning and building high-performing web applications.
EDUCATION
Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology , Rishihood University Grade: 8.38/10.0
Intermediate (Class XII) 2020 - 2022
Sri Chaitanya Junior College Grade: 97.1%
Matriculation (Class X) 2019 - 2020
Kendriya Vidyalaya No-1 , Air Force Academy Grade: 90.0%
INTERNSHIPS
SDE INTERN January 2025 - Present
Spectacom Global Gurugram,Haryana
Developed a leaderboard system in Django , supporting regional and individual rankings .
Integrated detailed participant timing records with numerical insights for performance tracking.
Optimized Django ORM queries for ecient leaderboard updates and retrieval.
PROJECTS
Laundry Pro , ( Github ) ( Demo ) October 2024
Tech Stack: Built with Node.js, Express, Prisma, SQL.
Description: Laundry Pro simplies laundry services with an ecient platform for students to place and track orders
and admins to manage operations.
Features:
{Secure login for students and admins.
{Admin tools for order status updates and record management.
Fabrix , ( Github ) ( Demo ) September 2024
Tech Stack: Built with React ,Tailwind CSS , and integrated Razorpay for a modern, responsive e-commerce
platform.
Description: Developed a dynamic, simulated e-commerce platform with secure user authentication and product
browsing using dummy data for a modern, responsive shopping experience.
Features:
{Secure user authentication with personalized features.
{Product browsing and ltering using dummy data, Shopping cart management with Razorpay integration for secure
checkout.
Crypto - manager , ( Github ) ( Demo ) March 2024
Tech Stack: Built with HTML ,CSS and JavaScript .
Description: Created a web application for managing and tracking cryptocurrency investments with real-time data and
a responsive, interactive user interface.
Features:
{Real-time API integration to fetch live cryptocurrency data and market trends.
{Simulated trading for buying, selling, and holding cryptocurrencies in a risk-free environment..
SKILLS
Computer Languages: Java, JavaScript, CSS, HTML, Python
Software Packages: React, Mongo DB, Django, Express JS, NodeJS, Prisma ORM, MySQL, Tailwind, Hadoop HDFS
Additional Courses: Data Structure
Soft Skills: Presentation Skills, Teamwork, Time management, Leadership
EXTRA-CURRICULAR ACTIVITIES
Reliance Foundation Undergraduate Scholar, receiving a Merit-based scholarship for my undergraduate studies.
Ranked in the top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology.
Earned the Rajya Puraskar award inScouts and Guides after completing rigorous training and Community service.
Qualied for the Semi-Finals inBadminton at the University Sports Fest .
