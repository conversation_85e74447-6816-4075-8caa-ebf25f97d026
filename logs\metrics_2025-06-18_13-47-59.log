{"event": "session_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "timestamp": "2025-06-18T13:47:59.583352", "message": "New API session started"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "1ef869a9-d6bd-4e62-bbf9-a23f6838eb3c", "endpoint": "/", "timestamp": "2025-06-18T13:48:55.507629", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "1ef869a9-d6bd-4e62-bbf9-a23f6838eb3c", "endpoint": "/", "timestamp": "2025-06-18T13:48:55.508629", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.540449", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.664111", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.664111", "file_size_bytes": 157348, "message": "Custom metric: file_size_bytes=157348"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.664111", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.664111", "extracted_text_length": 12576, "message": "Custom metric: extracted_text_length=12576"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:48:57.664111", "file_processing_time": 0.12066316604614258, "message": "Custom metric: file_processing_time=0.12066316604614258"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "ca7d99cd-abd1-4f27-8aa8-7c01ff74a5c4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:13.146103", "total_time_seconds": 15.605653762817383, "status_code": 200, "message": "Request completed in 15.6057s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.194501", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.207501", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.208508", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.208508", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.208508", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:17.208508", "file_processing_time": 0.011001825332641602, "message": "Custom metric: file_processing_time=0.011001825332641602"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "33afe7b6-931d-4aea-80f1-2df8eb7b0b5e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:49:31.253579", "total_time_seconds": 14.059078216552734, "status_code": 200, "message": "Request completed in 14.0591s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "dae3751a-4ae2-4d00-ba7e-452d8f49b407", "endpoint": "/", "timestamp": "2025-06-18T13:50:27.415713", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "dae3751a-4ae2-4d00-ba7e-452d8f49b407", "endpoint": "/", "timestamp": "2025-06-18T13:50:27.416711", "total_time_seconds": 0.000997304916381836, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.476098", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.671978", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.671978", "file_size_bytes": 118090, "message": "Custom metric: file_size_bytes=118090"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.672976", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.672976", "extracted_text_length": 22462, "message": "Custom metric: extracted_text_length=22462"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:50:29.672976", "file_processing_time": 0.19287467002868652, "message": "Custom metric: file_processing_time=0.19287467002868652"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d9ce1ee6-f5ea-45e1-877e-08fbe75115d0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:03.787966", "total_time_seconds": 34.311867475509644, "status_code": 200, "message": "Request completed in 34.3119s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.843914", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.971184", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.972204", "file_size_bytes": 386748, "message": "Custom metric: file_size_bytes=386748"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.972204", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.972204", "extracted_text_length": 17173, "message": "Custom metric: extracted_text_length=17173"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:06.972204", "file_processing_time": 0.12226080894470215, "message": "Custom metric: file_processing_time=0.12226080894470215"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "e8198210-46bd-435b-8274-45bf78217c3e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:22.214942", "total_time_seconds": 15.371027708053589, "status_code": 200, "message": "Request completed in 15.3710s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.275412", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.378171", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.379166", "file_size_bytes": 157348, "message": "Custom metric: file_size_bytes=157348"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.379166", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.379166", "extracted_text_length": 12576, "message": "Custom metric: extracted_text_length=12576"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:25.379166", "file_processing_time": 0.09924864768981934, "message": "Custom metric: file_processing_time=0.09924864768981934"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "714205fe-1ea1-4ead-9a19-8de11855bc81", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:51:59.400486", "total_time_seconds": 34.12507390975952, "status_code": 200, "message": "Request completed in 34.1251s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.437586", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.452585", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.452585", "file_size_bytes": 72943, "message": "Custom metric: file_size_bytes=72943"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.452585", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.452585", "extracted_text_length": 3091, "message": "Custom metric: extracted_text_length=3091"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:02.453591", "file_processing_time": 0.012999296188354492, "message": "Custom metric: file_processing_time=0.012999296188354492"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "d547d7f6-2ff6-4146-abaa-6527f14b84a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:19.105118", "total_time_seconds": 16.667531728744507, "status_code": 200, "message": "Request completed in 16.6675s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.149674", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.166663", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.166663", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.166663", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.166663", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:22.167663", "file_processing_time": 0.014989852905273438, "message": "Custom metric: file_processing_time=0.014989852905273438"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "4130eead-ab3d-41fe-bb15-bde5c9a8afde", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:52.514810", "total_time_seconds": 30.36513614654541, "status_code": 200, "message": "Request completed in 30.3651s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.562399", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.578914", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.578914", "file_size_bytes": 84438, "message": "Custom metric: file_size_bytes=84438"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.578914", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.578914", "extracted_text_length": 2855, "message": "Custom metric: extracted_text_length=2855"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:52:55.578914", "file_processing_time": 0.013512849807739258, "message": "Custom metric: file_processing_time=0.013512849807739258"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "2388f349-3e34-43ee-828c-fa91ffc1d537", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:11.224559", "total_time_seconds": 15.662160158157349, "status_code": 200, "message": "Request completed in 15.6622s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.254683", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.269684", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.269684", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.269684", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.269684", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:14.269684", "file_processing_time": 0.013000965118408203, "message": "Custom metric: file_processing_time=0.013000965118408203"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "635250b0-6c91-4c29-9ab7-2bee1349f797", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:29.764567", "total_time_seconds": 15.509883403778076, "status_code": 200, "message": "Request completed in 15.5099s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.816044", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.834044", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.834044", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.834044", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.834044", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:53:32.834044", "file_processing_time": 0.015999555587768555, "message": "Custom metric: file_processing_time=0.015999555587768555"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "bf6ee921-3d6b-46e1-b63c-afd29985f566", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:07.692667", "total_time_seconds": 34.876622438430786, "status_code": 200, "message": "Request completed in 34.8766s with status 200"}
{"event": "request_start", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.725734", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.791889", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.791889", "file_size_bytes": 3449572, "message": "Custom metric: file_size_bytes=3449572"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.792889", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.792889", "extracted_text_length": 16137, "message": "Custom metric: extracted_text_length=16137"}
{"event": "custom_metric", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:10.792889", "file_processing_time": 0.03313493728637695, "message": "Custom metric: file_processing_time=0.03313493728637695"}
{"event": "request_complete", "session_id": "65340d26-1a40-45e0-8133-d1f086d088e1", "request_id": "34e1819b-fd6d-43b0-8008-9e50e3fd713a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:54:22.248450", "total_time_seconds": 11.52271580696106, "status_code": 200, "message": "Request completed in 11.5227s with status 200"}
