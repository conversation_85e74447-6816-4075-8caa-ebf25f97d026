{"event": "session_start", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "timestamp": "2025-06-18T16:44:12.881890", "message": "New API session started"}
{"event": "request_start", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "ee43d96f-c610-489c-8275-9da792764eb4", "endpoint": "/", "timestamp": "2025-06-18T16:44:38.752314", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "ee43d96f-c610-489c-8275-9da792764eb4", "endpoint": "/", "timestamp": "2025-06-18T16:44:38.753314", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "1f99e748-c275-4c03-b69a-b02cec974f61", "endpoint": "/", "timestamp": "2025-06-18T16:44:51.727720", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "1f99e748-c275-4c03-b69a-b02cec974f61", "endpoint": "/", "timestamp": "2025-06-18T16:44:51.728720", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.766960", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.782959", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.782959", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.783958", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.783958", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:44:53.783958", "file_processing_time": 0.013998270034790039, "message": "Custom metric: file_processing_time=0.013998270034790039"}
{"event": "request_complete", "session_id": "48ae7865-f595-4e2d-a6a4-9258a34271c8", "request_id": "caeb08bc-b785-4048-bf58-6b6e45e3a1b7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:45:43.572027", "total_time_seconds": 49.80506658554077, "status_code": 200, "message": "Request completed in 49.8051s with status 200"}
