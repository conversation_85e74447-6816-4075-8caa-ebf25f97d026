{"event": "session_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "timestamp": "2025-06-18T16:28:36.413647", "message": "New API session started"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "30afb549-f9a0-40c2-a2b8-1a632b5de290", "endpoint": "/", "timestamp": "2025-06-18T16:29:06.071949", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "30afb549-f9a0-40c2-a2b8-1a632b5de290", "endpoint": "/", "timestamp": "2025-06-18T16:29:06.073455", "total_time_seconds": 0.0015058517456054688, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "a5539244-ad4d-4754-9182-ee36add61100", "endpoint": "/", "timestamp": "2025-06-18T16:29:16.391099", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "a5539244-ad4d-4754-9182-ee36add61100", "endpoint": "/", "timestamp": "2025-06-18T16:29:16.393100", "total_time_seconds": 0.0020008087158203125, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.433083", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.478461", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.478461", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.478461", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.478461", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:29:18.479462", "file_processing_time": 0.04086446762084961, "message": "Custom metric: file_processing_time=0.04086446762084961"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "111a61c8-3c53-4239-a28a-8542c9f5fc86", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:30:04.650612", "total_time_seconds": 46.21752882003784, "status_code": 200, "message": "Request completed in 46.2175s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "721859fe-c678-476b-b80a-cfaac7372d4b", "endpoint": "/", "timestamp": "2025-06-18T16:32:00.582925", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "721859fe-c678-476b-b80a-cfaac7372d4b", "endpoint": "/", "timestamp": "2025-06-18T16:32:00.582925", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.637091", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.652089", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.652089", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.652089", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.652089", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:02.652089", "file_processing_time": 0.013001441955566406, "message": "Custom metric: file_processing_time=0.013001441955566406"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "9f8bed8d-7bac-46be-b911-7a17c49bf40f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:40.056874", "total_time_seconds": 37.419782638549805, "status_code": 200, "message": "Request completed in 37.4198s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.145343", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.158343", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.158343", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.159344", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.159344", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:32:44.159344", "file_processing_time": 0.010993719100952148, "message": "Custom metric: file_processing_time=0.010993719100952148"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "4d6d9b3c-c35c-4d33-86e6-4f1d490f058b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:11.886973", "total_time_seconds": 27.741629123687744, "status_code": 200, "message": "Request completed in 27.7416s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.946059", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.960062", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.960062", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.960062", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.960062", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:15.960062", "file_processing_time": 0.012003898620605469, "message": "Custom metric: file_processing_time=0.012003898620605469"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8ac73467-db92-41c4-befb-d10672719fe4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:33:59.320281", "total_time_seconds": 43.37422227859497, "status_code": 200, "message": "Request completed in 43.3742s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.376302", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.393302", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.393302", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.393302", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.393302", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:03.393302", "file_processing_time": 0.012996196746826172, "message": "Custom metric: file_processing_time=0.012996196746826172"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "89e5d3e7-e745-4178-bcb1-9fa05e675b0a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:50.337597", "total_time_seconds": 46.96129560470581, "status_code": 200, "message": "Request completed in 46.9613s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.411224", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.424674", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.425674", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.425674", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.425674", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:34:54.425674", "file_processing_time": 0.011029720306396484, "message": "Custom metric: file_processing_time=0.011029720306396484"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "8df82b38-58af-4f56-b0b6-78da2ed5d8ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:08.266527", "total_time_seconds": 13.855302810668945, "status_code": 200, "message": "Request completed in 13.8553s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.333441", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.347444", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.347444", "file_size_bytes": 70825, "message": "Custom metric: file_size_bytes=70825"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.347444", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.347444", "extracted_text_length": 2308, "message": "Custom metric: extracted_text_length=2308"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:12.347444", "file_processing_time": 0.012003421783447266, "message": "Custom metric: file_processing_time=0.012003421783447266"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "e1876547-d9b2-4758-9c10-bdf559bb3585", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:35:54.543430", "total_time_seconds": 42.209989070892334, "status_code": 200, "message": "Request completed in 42.2100s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "3a77fa3f-d169-45fe-949a-fc27432ec6a3", "endpoint": "/", "timestamp": "2025-06-18T16:40:34.127759", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "3a77fa3f-d169-45fe-949a-fc27432ec6a3", "endpoint": "/", "timestamp": "2025-06-18T16:40:34.128757", "total_time_seconds": 0.0009980201721191406, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.177752", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.191970", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.191970", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.191970", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.192977", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:40:36.192977", "file_processing_time": 0.012020349502563477, "message": "Custom metric: file_processing_time=0.012020349502563477"}
{"event": "request_complete", "session_id": "bc7cc399-5c1d-4128-8fd1-cc3bfdb49695", "request_id": "336f608a-4881-4010-804c-9f8fa273d3a5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:41:18.840174", "total_time_seconds": 42.66242289543152, "status_code": 200, "message": "Request completed in 42.6624s with status 200"}
