================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: Charlie Resume.pdf
Extraction Method: multiple_calls
Timestamp: 2025-06-17T17:42:16.414350
Total Sections Extracted: 8
Processing Time: 81.8205497264862 seconds
Total LLM Calls: 8
Overall Confidence: 0.9750000000000001
================================================================================

[SUMMARY]
----------------------------------------
PROFESSIONAL SUMMARY
● 7 years of experience in developing and maintaining strong backend applications using 
Java and Spring Boot , delivering scalable, high- performance applications that handle a 
large quantity of daily transactions, focusing on optimizing performance, reliability, and production- quality software to meet enterprise -level demands.  
● Proficient in leveraging the full suite of Spring Framework components, including Spring MVC , Spring Data JPA , and Spring Security , to build secure, maintainable, 
and highly modular backend services, ensuring seamless integration with other systems and applications while adhering to modern software development best practices.  
● Skilled in designing, Object -Oriented Programming (OOP)  and implementing secure 
RESTful APIs using Spring Boot , with a strong focus on authentication and 
authorization mechanisms using OAuth2 and JWT , ensuring that sensitive data and 
operations are protected across distributed systems.  
● Experienced in managing data persistence with Hibernate and JPA, working extensively 
with relational databases such as Oracle, MySQL , and PostgreSQL , as well as NoSQL 
databases like MongoDB and Cassandra DB , to ensure data integrity, high availability, 
and optimized query performance in large -scale applications.  
● Proficient in deploying and managing scalable backend services on cloud platforms such 
as AWS , utilizing Docker for containerization and Kubernetes for orchestration, 
ensuring seamless scalability, fault tolerance, and high availability across cloud environments.  
● Skilled in implementing real- time data processing and event -driven architectures using 
Apache Kafka and RabbitMQ , enabling high- throughput, reliable messaging systems 
that handle massive data streams and support distributed microservices architectures.  
● Experienced in working with Big Data technologies such as Apache Spark and 
Kinesis , for data processing and analysis.  
● Developed and maintained Spring Boot applications for various business domains.  
● Collaborated with cross-functional teams to deliver high-quality software solutions.  
● Actively involved in code reviews and knowledge sharing within the development team.  
● Continuously learned and adapted to new technologies and methodologies.

========================================

[EDUCATION]
----------------------------------------
EDUCATION

Stevens Institute of technology 
Hoboken, New Jersey, USA 
Master of Science in Computer Science. 
09, 2021 - 05, 2023

========================================

[EXPERIENCE]
----------------------------------------
EXPERIENCE

Wells Fargo
Java Developer Aug 2023 - Present
Project description :
Wells Fargo is a leading financial institution providing a wide array of banking and financial services. As part of the Credit Monitoring project at Wells Fargo, I am responsible for the development of the backend of the Monitoring system. My primary focus is on monitoring the transactions used by Wells Fargo credit cards and migrating the real-time transaction status details from backend to frontend, ensuring seamless integration and enhanced user experience. This project plays a significant role in optimizing risk mitigation strategies and improving the stability of the bank’s risk management processes. Environment :

Java, Spring Boot, Spring Security, JPA, Hibernate, Apache Kafka, Maven, Jenkins, MYSQL, RESTFUL API, Unit Test, JUnit, Mockito, React, Material UI, AWS, Agile, Scrum, Git.
Responsibilities :
• Utilized Java within the Spring Boot ecosystem in IntelliJ IDEA to develop and enhance the backend services.
• Utilized JavaScript within the React library to build interactive and dynamic user interface and using Material UI for rendering beautiful components.
• Design and develop complex RESTful APIs using Spring Boot, enabling lightweight and efficient communication between frontend and backend.
• Integrated authentication and authorization mechanisms such as OAuth2 and JWT to ensure that only users with permission can access sensitive risk monitoring processes.
• Architected highly scalable microservices utilizing Spring Boot and Apache Kafka, managing millions of transactions daily with minimal latency.
• Developed a robust event-driven architecture using Kafka, enabling stable and scalable message processing across the system, ensuring that critical financial events such as trade transactions and risk alerts were processed in time.
• Optimized complex SQL queries to retrieve data from Cassandra Database and to perform RESTful services with high availability, scalability and fault tolerance.
• Collaborated with the DevOps team to streamline CI/CD pipelines using Jenkins, automating the build, test, and deployment processes to ensure rapid and reliable feature releases.
• Worked closely with the frontend team to integrate backend services with a React-based frontend, ensuring smooth real-time logistics updates, reducing latency in data delivery and displaying a comfortable user interface.
• Implemented application testability and diagnostics and fixing bugs with the help of JUnit and Mockito.

L'Oréal
Role: Java Developer Feb 2020 - Aug 2021
Project description : As a member of the development team at Loreal, I contributed to the development of an online shopping website designed to simplify buyers’ shopping process and decrease the difficulty for sellers to sell their goods. The website allows sellers to create or update their products in a simple form and allows customers to pay in multiple ways. I am responsible for designing the product attributes, which ensures the product’s information is well stored in the database and easy to retrieve and mapping. And it allows the frontend team to easily get the data and display the product on the webpage.
Environment :
Java, Spring MVC, Spring Boot, Hibernate, React, HTML5, CSS3, Bootstrap, MySQL, JSON, TCS, SCSS, VSCode, Agile, Scrum.

IGN Entertainment
Role: Full stack Developer Sep 2018 - Feb 2020
Project description : The project aimed to build an online evaluation system related to films, TV series, books, and animations, etc. The website serves as a film and television works search engine and opinion exchange area. I contributed to the development of the backend. The app allows users to click an item such as a film, a TV series or a book, and then lead them to the details page and present the picture, cast list, comments about the item. The rate, review and comments about the item are listed below the cast list and it allows users to share and exchange their opinions about the item here, which assists users to get an intuitive overview of the item.
Environment :
Java, JavaScript, Spring MVC, Spring

========================================

[SKILLS]
----------------------------------------
SKILLS
● Programming Languages : Java, JavaScript, TypeScript, Python, C++, SQL
● Technologies: GraphQL, Microservices, Kafka, Unit Testing, CI/CD, HTML, CSS, React.js, Redux, Express.js, Node.js, Restful APIs, RDBMS (Oracle, MYSQL, PostgreSQL), NoSQL (MongoDB, Cassandra DB), Spark
● Developer Tools: Maven, Git, Postman, Webpack, Babel, NPM, Docker, Kubernetes
● Frameworks: Spring Boot, Spring MVC, Spring Data JPA, Hibernate, React, Mockito
● OS: Windows, MacOS, Linux
● Cloud Platforms: AWS, Heroku
● Database: MySQL, MongoDB, PostgreSQL, Redis
● Project Management: Scrum, Jira, Agile
● Platforms and IDE: Visual Studio Code, IntelliJ IDEA, PyCharm, Eclipse

========================================

[PROJECTS]
----------------------------------------
PROJECTS

Wells Fargo
Java Developer Aug 2023 - Present
Project description :
Wells Fargo is a leading financial institution providing a wide array of banking and financial services. As part of the Credit Monitoring project at Wells Fargo, I am responsible for the development of the backend of the Monitoring system. My primary focus is on monitoring the transactions used by Wells Fargo credit cards and migrating the real-time transaction status details from backend to frontend, ensuring seamless integration and enhanced user experience. This project plays a significant role in optimizing risk mitigation strategies and improving the stability of the bank’s risk management processes. Environment :
Java, Spring Boot, Spring Security, JPA, Hibernate, Apache Kafka, Maven, Jenkins, MYSQL, RESTFUL API, Unit Test, JUnit, Mockito, React, Material UI, AWS, Agile, Scrum, Git.
Responsibilities :
• Utilized Java within the Spring Boot ecosystem in IntelliJ IDEA to develop and enhance the backend services.
• Utilized JavaScript within the React library to build interactive and dynamic user interface and using Material UI for rendering beautiful components.
• Design and develop complex RESTful APIs using Spring Boot, enabling lightweight and efficient communication between frontend and backend.
• Integrated authentication and authorization mechanisms such as OAuth2 and JWT to ensure that only users with permission can access sensitive risk monitoring processes.
• Architected highly scalable microservices utilizing Spring Boot and Apache Kafka, managing millions of transactions daily with minimal latency.
• Developed a robust event-driven architecture using Kafka, enabling stable and scalable message processing across the system, ensuring that critical financial events such as trade transactions and risk alerts were processed in time.
• Optimized complex SQL queries in MySQL database, storing and managing user information and transaction details through operations ( CRUD).
• Used JavaScript as main programming language in frontend design, and used React Library to build modular and scalable components for the web application.
• Implemented comprehensive unit and integration tests using JUnit and Mockito to ensure code quality and reliability.
• Collaborated closely with the DevOps team to streamline CI/CD pipelines, automating the build, test, and deployment processes to ensure rapid and reliable feature releases.
• Worked closely with the frontend team to integrate backend services with a React-based frontend, ensuring smooth real-time logistics updates, reducing latency in data delivery and displaying a comfortable user interface.
• Developed and maintained automated testing frameworks using JUnit and Mockito to verify functionality and ensure code quality.
• Designed and implemented scalable and resilient backend services using Spring Boot and microservice architecture.

Target
Java Developer Jan 2022 - Present
Project description :
As a software engineer, I am focused on building a modern, scalable, and reliable platform for our client’s retail operations. I am responsible for designing, developing, and maintaining backend services that power our e-commerce platform, ensuring a seamless and engaging customer experience. Environment :
Java, Spring Boot, Spring Cloud, MySQL, Redis, RabbitMQ, Docker, Kubernetes, AWS, Agile, Scrum, Git.
Responsibilities :
• Designed and developed RESTful APIs using Spring Boot and Spring Cloud for managing product catalog, user accounts, and order processing.
• Implemented microservices architecture to enable independent deployment and scaling of individual services.
• Utilized Spring Data JPA with MySQL for persistent data storage and retrieval.
• Leveraged Redis for caching frequently accessed data to improve application performance.
• Utilized RabbitMQ for asynchronous message processing, such as order confirmation and notification sending.
• Implemented containerization with Docker and orchestration with Kubernetes for application deployment and management.
• Contributed to the development of a CI/CD pipeline using Jenkins for automated build, test, and deployment.
• Participated in code reviews, sprint planning, and daily stand-up meetings.
• Worked closely with the frontend development team to ensure seamless integration of backend services.
• Developed and maintained unit and integration tests

========================================

[CERTIFICATIONS]
----------------------------------------
PROFESSIONAL SUMMARY
● 7 years of experience in developing and maintaining strong backend applications using 
Java and Spring Boot , delivering scalable, high- performance applications that handle a 
large quantity of daily transactions, focusing on optimizing performance, reliability, and production- quality software to meet enterprise -level demands. 
● Proficient in leveraging the full suite of Spring Framework components, including Spring MVC , Spring Data JPA , and Spring Security , to build secure, maintainable, 
and highly modular backend services, ensuring seamless integration with other systems and applications while adhering to modern software development best practices. 
● Skilled in designing, Object -Oriented Programming (OOP)  and implementing secure 
RESTful APIs using Spring Boot , with a strong focus on authentication and 
authorization mechanisms using OAuth2 and JWT , ensuring that sensitive data and 
operations are protected across distributed systems. 
● Experienced in managing data persistence with Hibernate and JPA, working extensively 
with relational databases such as Oracle, MySQL , and PostgreSQL , as well as NoSQL 
databases like MongoDB and Cassandra DB , to ensure data integrity, high availability, 
and optimized query performance in large -scale applications.  
● Proficient in deploying and managing scalable backend services on cloud platforms such 
as AWS , utilizing Docker for containerization and Kubernetes for orchestration, 
ensuring seamless scalability, fault tolerance, and high availability across cloud environments. 
● Skilled in implementing real -time data processing and event -driven architectures using 
Apache Kafka and RabbitMQ , enabling high- throughput, reliable messaging systems 
that handle massive data streams and support distributed microservices architectures.  
● Experienced in working with Big Data technologies such as Apache Spark and 
PySpark for processing and analyzing large datasets, allowing for the efficient execution of complex analytical tasks and generating valuable insights. 
● Proficient in utilizing various software development methodologies, including Agile and Waterfall, to deliver high-quality software solutions within defined timelines and budgets. 

EDUCATION

Stevens Institute of technology
Hoboken, New Jersey, USA
Master of Science in Computer Science.
09, 2021 - 05, 2023

========================================

[ACHIEVEMENTS]
----------------------------------------
ACHIEVEMENTS

● 7 years of experience in developing and maintaining strong backend applications using Java and Spring Boot , delivering scalable, high- performance applications that handle a large quantity of daily transactions, focusing on optimizing performance, reliability, and production- quality software to meet enterprise -level demands. 
● Proficient in leveraging the full suite of Spring Framework components, including Spring MVC , Spring Data JPA , and Spring Security , to build secure, maintainable, and highly modular backend services, ensuring seamless integration with other systems and applications while adhering to modern software development best practices. 
● Skilled in designing, Object -Oriented Programming (OOP)  and implementing secure RESTful APIs using Spring Boot , with a strong focus on authentication and authorization mechanisms using OAuth2 and JWT , ensuring that sensitive data and operations are protected across distributed systems. 
● Experienced in managing data persistence with Hibernate and JPA, working extensively with relational databases such as Oracle, MySQL , and PostgreSQL , as well as NoSQL databases like MongoDB and Cassandra DB , to ensure data integrity, high availability, and optimized query performance in large -scale applications. 
● Proficient in deploying and managing scalable backend services on cloud platforms such as AWS , utilizing Docker for containerization and Kubernetes for orchestration, ensuring seamless scalability, fault tolerance, and high availability across cloud environments. 
● Skilled in implementing real- time data processing and event -driven architectures using Apache Kafka and RabbitMQ , enabling high- throughput, reliable messaging systems that handle massive data streams and support distributed microservices architectures. 
● Experienced in working with Big Data technologies such as Apache Spark and PySpark for processing and analyzing large datasets, allowing for the efficient handling of complex data workflows and enabling real -time data analytics in resource- intensive environments.

========================================

[LANGUAGES]
----------------------------------------
LANGUAGES
English
NOT_FOUND

========================================

