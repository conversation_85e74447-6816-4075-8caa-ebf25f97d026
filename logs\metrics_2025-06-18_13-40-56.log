{"event": "session_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "timestamp": "2025-06-18T13:40:56.685030", "message": "New API session started"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "6d11759f-6c6c-4255-adec-b34b605ff022", "endpoint": "/", "timestamp": "2025-06-18T13:41:36.779700", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "6d11759f-6c6c-4255-adec-b34b605ff022", "endpoint": "/", "timestamp": "2025-06-18T13:41:36.780699", "total_time_seconds": 0.0009989738464355469, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "6326a1b9-be34-42fb-8328-5b76f2b9470c", "endpoint": "/", "timestamp": "2025-06-18T13:41:46.669949", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "6326a1b9-be34-42fb-8328-5b76f2b9470c", "endpoint": "/", "timestamp": "2025-06-18T13:41:46.670947", "total_time_seconds": 0.000997781753540039, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.737157", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.973590", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.973590", "file_size_bytes": 118090, "message": "Custom metric: file_size_bytes=118090"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.973590", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.974594", "extracted_text_length": 22462, "message": "Custom metric: extracted_text_length=22462"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:41:48.974594", "file_processing_time": 0.23243045806884766, "message": "Custom metric: file_processing_time=0.23243045806884766"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1cdf0292-5486-4672-bba8-d20e442a326c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:07.615762", "total_time_seconds": 18.878604888916016, "status_code": 200, "message": "Request completed in 18.8786s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.672567", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.801809", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.801809", "file_size_bytes": 386748, "message": "Custom metric: file_size_bytes=386748"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.801809", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.801809", "extracted_text_length": 17173, "message": "Custom metric: extracted_text_length=17173"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:10.801809", "file_processing_time": 0.12624359130859375, "message": "Custom metric: file_processing_time=0.12624359130859375"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "1eaa45ee-56a4-449c-ad52-717c5676a4c1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:43.133914", "total_time_seconds": 32.46134662628174, "status_code": 200, "message": "Request completed in 32.4613s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.201654", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.322583", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.322583", "file_size_bytes": 157348, "message": "Custom metric: file_size_bytes=157348"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.323581", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.323581", "extracted_text_length": 12576, "message": "Custom metric: extracted_text_length=12576"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:42:46.323581", "file_processing_time": 0.11792564392089844, "message": "Custom metric: file_processing_time=0.11792564392089844"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "d0923fc9-3ed1-4436-b104-8803c926ca40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:18.982073", "total_time_seconds": 32.78041863441467, "status_code": 500, "message": "Request completed in 32.7804s with status 500"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.014011", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.113522", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.113522", "file_size_bytes": 157348, "message": "Custom metric: file_size_bytes=157348"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.113522", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.113522", "extracted_text_length": 12576, "message": "Custom metric: extracted_text_length=12576"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.113522", "file_processing_time": 0.09751415252685547, "message": "Custom metric: file_processing_time=0.09751415252685547"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "73d7c0f7-2e6e-4922-8036-1f45d689c735", "endpoint": "/section3", "timestamp": "2025-06-18T13:43:21.141001", "total_time_seconds": 0.12699031829833984, "status_code": 200, "message": "Request completed in 0.1270s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.881949", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.914870", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.914870", "file_size_bytes": 72943, "message": "Custom metric: file_size_bytes=72943"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.914870", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.914870", "extracted_text_length": 3091, "message": "Custom metric: extracted_text_length=3091"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:43:57.914870", "file_processing_time": 0.0309145450592041, "message": "Custom metric: file_processing_time=0.0309145450592041"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "dfbe4a08-1aea-4881-9b7c-466280da2e55", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:11.006885", "total_time_seconds": 13.12493634223938, "status_code": 200, "message": "Request completed in 13.1249s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.058773", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.074773", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.074773", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.074773", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.074773", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:14.074773", "file_processing_time": 0.014002561569213867, "message": "Custom metric: file_processing_time=0.014002561569213867"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "ed21fd9e-8e27-49c7-83a4-7622b23fdb40", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:46.214587", "total_time_seconds": 32.15581464767456, "status_code": 200, "message": "Request completed in 32.1558s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.272513", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.288876", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.288876", "file_size_bytes": 84438, "message": "Custom metric: file_size_bytes=84438"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.288876", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.289876", "extracted_text_length": 2855, "message": "Custom metric: extracted_text_length=2855"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:44:49.289876", "file_processing_time": 0.01435995101928711, "message": "Custom metric: file_processing_time=0.01435995101928711"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "b1e7b534-5d7a-4590-9005-09984eb3a9de", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:05.711291", "total_time_seconds": 16.438778400421143, "status_code": 200, "message": "Request completed in 16.4388s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.793253", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.807252", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.807252", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.807252", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.807252", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:08.807252", "file_processing_time": 0.011001110076904297, "message": "Custom metric: file_processing_time=0.011001110076904297"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "8dc54738-c251-4f81-babc-c4bd9afc6e47", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:24.203797", "total_time_seconds": 15.41054391860962, "status_code": 500, "message": "Request completed in 15.4105s with status 500"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.226945", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.242138", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.242138", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.242138", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.243139", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.243139", "file_processing_time": 0.01119542121887207, "message": "Custom metric: file_processing_time=0.01119542121887207"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "49d4a5c5-56e7-45f4-a485-6121e18ce8a4", "endpoint": "/section3", "timestamp": "2025-06-18T13:45:26.264564", "total_time_seconds": 0.037619590759277344, "status_code": 200, "message": "Request completed in 0.0376s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.278563", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.324385", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.324385", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.324385", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.324385", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:45:45.325384", "file_processing_time": 0.04381728172302246, "message": "Custom metric: file_processing_time=0.04381728172302246"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "12976961-e5d2-4a82-aa30-a45a61872e36", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:11.458481", "total_time_seconds": 26.179917812347412, "status_code": 200, "message": "Request completed in 26.1799s with status 200"}
{"event": "request_start", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.500931", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.589944", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.589944", "file_size_bytes": 3449572, "message": "Custom metric: file_size_bytes=3449572"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.589944", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.590945", "extracted_text_length": 16137, "message": "Custom metric: extracted_text_length=16137"}
{"event": "custom_metric", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:14.590945", "file_processing_time": 0.051764726638793945, "message": "Custom metric: file_processing_time=0.051764726638793945"}
{"event": "request_complete", "session_id": "6b0d8711-9cfa-4874-b207-31914d37c317", "request_id": "7c83f825-6134-4ebb-9e54-1413417dea37", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:46:48.871303", "total_time_seconds": 34.3703727722168, "status_code": 200, "message": "Request completed in 34.3704s with status 200"}
