{"event": "session_start", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "timestamp": "2025-06-18T16:46:49.276816", "message": "New API session started"}
{"event": "request_start", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "72e5613b-e76e-405e-be77-cc2c2b7117f2", "endpoint": "/", "timestamp": "2025-06-18T16:47:15.824350", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "72e5613b-e76e-405e-be77-cc2c2b7117f2", "endpoint": "/", "timestamp": "2025-06-18T16:47:15.826350", "total_time_seconds": 0.0009996891021728516, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "2ef94d07-53e9-4e20-ac63-1d79ea4e759f", "endpoint": "/", "timestamp": "2025-06-18T16:47:26.623033", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "2ef94d07-53e9-4e20-ac63-1d79ea4e759f", "endpoint": "/", "timestamp": "2025-06-18T16:47:26.624033", "total_time_seconds": 0.0009996891021728516, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.652825", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.671516", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.671516", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.672521", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.672521", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:47:28.672521", "file_processing_time": 0.015508651733398438, "message": "Custom metric: file_processing_time=0.015508651733398438"}
{"event": "request_complete", "session_id": "b73368b9-becb-481e-9cb9-5fd13c334424", "request_id": "c0cb7403-1743-469d-8397-94f3636fab08", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:48:15.061955", "total_time_seconds": 46.40912938117981, "status_code": 200, "message": "Request completed in 46.4091s with status 200"}
