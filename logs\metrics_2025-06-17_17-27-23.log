{"event": "session_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "timestamp": "2025-06-17T17:27:23.402625", "message": "New API session started"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "6dafa338-00fc-4776-89fe-6d4936dedd8f", "endpoint": "/", "timestamp": "2025-06-17T17:27:25.459072", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "6dafa338-00fc-4776-89fe-6d4936dedd8f", "endpoint": "/", "timestamp": "2025-06-17T17:27:25.462074", "total_time_seconds": 0.0030019283294677734, "status_code": 200, "message": "Request completed in 0.0030s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b48878b4-fb10-4596-8644-c42a56dfd929", "endpoint": "/docs", "timestamp": "2025-06-17T17:27:28.292800", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b48878b4-fb10-4596-8644-c42a56dfd929", "endpoint": "/docs", "timestamp": "2025-06-17T17:27:28.294292", "total_time_seconds": 0.0014913082122802734, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5f53349c-24b5-45b8-b10f-bca3d63fb5cc", "endpoint": "/openapi.json", "timestamp": "2025-06-17T17:27:28.596530", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5f53349c-24b5-45b8-b10f-bca3d63fb5cc", "endpoint": "/openapi.json", "timestamp": "2025-06-17T17:27:28.643560", "total_time_seconds": 0.04703068733215332, "status_code": 200, "message": "Request completed in 0.0470s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.522548", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.593433", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.593433", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.593433", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.593433", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:09.593433", "file_processing_time": 0.06488728523254395, "message": "Custom metric: file_processing_time=0.06488728523254395"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "b38c056a-bdac-4052-96be-a8f6b18e2e05", "endpoint": "/section", "timestamp": "2025-06-17T17:28:22.689380", "total_time_seconds": 13.166831493377686, "status_code": 200, "message": "Request completed in 13.1668s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.651620", "message": "Request started for endpoint: /section2"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.694145", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.694145", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.694145", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.694145", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:01.695144", "file_processing_time": 0.039525747299194336, "message": "Custom metric: file_processing_time=0.039525747299194336"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5bf13aa8-e086-4e74-976d-b47db15de360", "endpoint": "/section2", "timestamp": "2025-06-17T17:29:13.246322", "total_time_seconds": 11.594701051712036, "status_code": 200, "message": "Request completed in 11.5947s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.513154", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.573342", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.573342", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.573342", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.573342", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:21.573342", "file_processing_time": 0.03895068168640137, "message": "Custom metric: file_processing_time=0.03895068168640137"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "5eb9c453-1ca1-44be-8329-4dcfea7206d4", "endpoint": "/section", "timestamp": "2025-06-17T17:38:42.053902", "total_time_seconds": 20.540748357772827, "status_code": 200, "message": "Request completed in 20.5407s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.701061", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.734477", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.734477", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.734477", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.734477", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:30.734477", "file_processing_time": 0.028751373291015625, "message": "Custom metric: file_processing_time=0.028751373291015625"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "798eab4a-e9ac-4e17-857c-281b7ec26dfe", "endpoint": "/section", "timestamp": "2025-06-17T17:39:44.771160", "total_time_seconds": 14.070099830627441, "status_code": 200, "message": "Request completed in 14.0701s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.569893", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.829808", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.829808", "file_size_bytes": 386748, "message": "Custom metric: file_size_bytes=386748"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.830809", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.830809", "extracted_text_length": 17173, "message": "Custom metric: extracted_text_length=17173"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:40:54.830809", "file_processing_time": 0.2344956398010254, "message": "Custom metric: file_processing_time=0.2344956398010254"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "3cdb6336-d8bc-4bd3-9659-493f1b61db16", "endpoint": "/section", "timestamp": "2025-06-17T17:42:16.416804", "total_time_seconds": 81.84691143035889, "status_code": 200, "message": "Request completed in 81.8469s with status 200"}
{"event": "request_start", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.271207", "message": "Request started for endpoint: /section2"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.541812", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.541812", "file_size_bytes": 386748, "message": "Custom metric: file_size_bytes=386748"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.541812", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.541812", "extracted_text_length": 17173, "message": "Custom metric: extracted_text_length=17173"}
{"event": "custom_metric", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:07.543316", "file_processing_time": 0.2449784278869629, "message": "Custom metric: file_processing_time=0.2449784278869629"}
{"event": "request_complete", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "request_id": "cd9ccc19-e9c6-4684-9d1c-59c0e24c022e", "endpoint": "/section2", "timestamp": "2025-06-17T17:45:42.678576", "total_time_seconds": 35.407368898391724, "status_code": 200, "message": "Request completed in 35.4074s with status 200"}
{"event": "session_end", "session_id": "3bfc5420-818c-476e-8ce4-ddd4834ff6a0", "timestamp": "2025-06-17T20:51:46.366780", "message": "API session ended"}
