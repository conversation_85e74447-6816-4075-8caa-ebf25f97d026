#!/usr/bin/env python3
"""
Test all the new resumes that haven't been tested yet with the hybrid endpoint.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_resume(resume_path):
    """Test a single resume and analyze the results."""
    
    print(f"\n📄 Testing: {resume_path.name}")
    print("-" * 60)
    
    try:
        file_size = resume_path.stat().st_size
        print(f"📊 File size: {file_size:,} bytes")
        
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                return False, f"Error: {result['error']}"
            
            # Analyze the response
            analysis = analyze_resume_response(result)
            print_analysis(analysis)
            
            return True, analysis
            
        else:
            error_msg = f"HTTP {response.status_code}: {response.text}"
            print(f"❌ FAILED: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Exception: {str(e)}"
        print(f"❌ EXCEPTION: {error_msg}")
        return False, error_msg

def analyze_resume_response(result):
    """Analyze the resume response for quality and schema compliance."""
    
    analysis = {
        'name': result.get('name', 'Not found'),
        'email': result.get('email'),
        'phone': result.get('phone'),
        'education_count': len(result.get('education', [])),
        'experience_count': len(result.get('experience', [])),
        'skills_count': len(result.get('skills', [])),
        'projects_count': len(result.get('projects', [])),
        'certifications_count': len(result.get('certifications', [])),
        'achievements_count': len(result.get('achievements', [])),
        'summary_present': bool(result.get('summary')),
        'social_media_count': len(result.get('social_media', [])),
        'issues': []
    }
    
    # Check schema compliance
    certifications = result.get('certifications', [])
    if certifications and not all(isinstance(cert, str) for cert in certifications):
        analysis['issues'].append("Certifications should be strings, not objects")
    
    achievements = result.get('achievements', [])
    if achievements and not all(isinstance(ach, str) for ach in achievements):
        analysis['issues'].append("Achievements should be strings, not objects")
    
    skills = result.get('skills', [])
    if skills and not all(isinstance(skill, str) for skill in skills):
        analysis['issues'].append("Skills should be strings, not objects")
    
    # Check experience content for misclassification
    experience = result.get('experience', [])
    for exp in experience:
        responsibilities = exp.get('key_responsibilities', '').lower()
        if any(word in responsibilities for word in ['basketball', 'olympiad', 'mathematics competition', 'sports', 'extra-curricular']):
            analysis['issues'].append("Experience contains extra-curricular activities (should be in achievements)")
    
    # Check for empty professional experience with null values
    for exp in experience:
        if not exp.get('company_name') and not exp.get('role') and not exp.get('duration'):
            if exp.get('key_responsibilities'):
                analysis['issues'].append("Experience entry has no company/role/duration but has responsibilities (likely misclassified)")
    
    return analysis

def print_analysis(analysis):
    """Print the analysis results."""
    
    print(f"📊 Analysis Results:")
    print(f"   👤 Name: '{analysis['name']}'")
    print(f"   📧 Email: {analysis['email'] or 'None'}")
    print(f"   📞 Phone: {analysis['phone'] or 'None'}")
    print(f"   🎓 Education: {analysis['education_count']} entries")
    print(f"   💼 Experience: {analysis['experience_count']} entries")
    print(f"   🛠️ Skills: {analysis['skills_count']} items")
    print(f"   🚀 Projects: {analysis['projects_count']} items")
    print(f"   🏆 Certifications: {analysis['certifications_count']} items")
    print(f"   🏅 Achievements: {analysis['achievements_count']} items")
    print(f"   📝 Summary: {'✅ Present' if analysis['summary_present'] else '❌ Missing'}")
    print(f"   📱 Social Media: {analysis['social_media_count']} items")
    
    if analysis['issues']:
        print(f"   ⚠️ Issues Found:")
        for issue in analysis['issues']:
            print(f"      - {issue}")
    else:
        print(f"   ✅ No schema/content issues detected")

def main():
    """Test all new resumes."""
    
    print("🧪 Testing New Resumes with Hybrid Endpoint")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # List of new resumes to test (excluding ones we've already tested extensively)
    new_resumes = [
        "Resume-Anurag Pandey.pdf",
        "Resume-AKASH G.pdf",
        "Resume-AROHI JADHAV.pdf", 
        "Resume-Adithya kammati.pdf",
        "Resume-Agrapujya Lashkari.pdf",
        "Resume-Daniel Tayal.pdf",
        "Resume-Dibyajyoti Behera.pdf",
        "Resume-Gourav Nss.pdf",
        "Resume-Hardik Maheshwari.pdf",
        "Resume-Harender chhoker.pdf",
        "Resume-Jithendranath akula.pdf",
        "Resume-Yashi Gupta.pdf"
    ]
    
    resume_dir = Path("resumes for testing")
    results = {}
    
    print(f"📁 Found {len(new_resumes)} new resumes to test")
    
    for i, resume_name in enumerate(new_resumes, 1):
        resume_path = resume_dir / resume_name
        
        if not resume_path.exists():
            print(f"\n❌ {i}/{len(new_resumes)}: {resume_name} - File not found")
            continue
        
        print(f"\n{'='*70}")
        print(f"📄 TESTING {i}/{len(new_resumes)}: {resume_name}")
        print(f"{'='*70}")
        
        success, result = test_resume(resume_path)
        results[resume_name] = {'success': success, 'result': result}
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 TESTING SUMMARY")
    print(f"{'='*70}")
    
    successful = [name for name, data in results.items() if data['success']]
    failed = [name for name, data in results.items() if not data['success']]
    with_issues = []
    
    for name, data in results.items():
        if data['success'] and isinstance(data['result'], dict):
            if data['result'].get('issues'):
                with_issues.append(name)
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}/{len(results)}")
    print(f"⚠️ With issues: {len(with_issues)}/{len(results)}")
    
    if successful:
        print(f"\n✅ Successful Resumes:")
        for name in successful:
            issues_indicator = " ⚠️" if name in with_issues else ""
            print(f"   ✅ {name}{issues_indicator}")
    
    if failed:
        print(f"\n❌ Failed Resumes:")
        for name in failed:
            error = results[name]['result']
            print(f"   ❌ {name}: {error}")
    
    if with_issues:
        print(f"\n⚠️ Resumes with Issues (but successful):")
        for name in with_issues:
            issues = results[name]['result']['issues']
            print(f"   ⚠️ {name}:")
            for issue in issues:
                print(f"      - {issue}")
    
    # Recommendations
    print(f"\n🎯 Recommendations:")
    if failed:
        print("1. Check prompt logs for failed resumes")
        print("2. Review error messages for patterns")
        print("3. Consider additional error handling")
    
    if with_issues:
        print("1. Review prompt to improve content classification")
        print("2. Add more specific schema enforcement")
        print("3. Improve experience vs achievements distinction")
    
    if len(successful) == len(results) and not with_issues:
        print("✅ All resumes processed perfectly!")
        print("✅ No improvements needed")
    
    print(f"\n📝 Next Steps:")
    print("1. Check the latest prompt logs for detailed analysis")
    print("2. Look for patterns in the issues found")
    print("3. Consider prompt improvements based on findings")

if __name__ == "__main__":
    main()
