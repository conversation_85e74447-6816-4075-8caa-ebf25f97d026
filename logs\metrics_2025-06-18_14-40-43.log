{"event": "session_start", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "timestamp": "2025-06-18T14:40:43.774007", "message": "New API session started"}
{"event": "request_start", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.953547", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.974951", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.974951", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.974951", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.974951", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:05.974951", "file_processing_time": 0.019403696060180664, "message": "Custom metric: file_processing_time=0.019403696060180664"}
{"event": "request_complete", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "43bee212-15e5-4251-bfd1-bf92c2911851", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:41:31.195264", "total_time_seconds": 25.242716789245605, "status_code": 200, "message": "Request completed in 25.2427s with status 200"}
{"event": "request_start", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "c17a7f24-89cf-4095-820d-ba7f4bb66dda", "endpoint": "/", "timestamp": "2025-06-18T14:43:38.528756", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "request_id": "c17a7f24-89cf-4095-820d-ba7f4bb66dda", "endpoint": "/", "timestamp": "2025-06-18T14:43:38.529536", "total_time_seconds": 0.0007803440093994141, "status_code": 200, "message": "Request completed in 0.0008s with status 200"}
{"event": "session_end", "session_id": "4e6f213d-ed06-4973-9269-9128fb244bc1", "timestamp": "2025-06-18T14:43:38.711738", "message": "API session ended"}
