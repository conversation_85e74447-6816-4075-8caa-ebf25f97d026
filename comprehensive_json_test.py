#!/usr/bin/env python3
"""
Comprehensive test of all problematic resumes with detailed analysis.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_comprehensive_resumes():
    """Test all the problematic resumes mentioned by the user."""
    
    print("🧪 COMPREHENSIVE JSON ISSUE TESTING")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test resumes from 15:41 onwards that had JSON issues
    test_resumes = [
        "Resume-Anurag Pandey.pdf",
        "Resume-Agrapujya Lashkari.pdf", 
        "Resume-Jithendranath akula.pdf",
        "Resume-MEESALA SREE SAI NATH.pdf",
        "Resume-Ya<PERSON> Gupta.pdf",
        "Resume-Harender chhoker.pdf"
    ]
    
    results = {}
    
    for i, resume_file in enumerate(test_resumes, 1):
        print(f"\n{'='*70}")
        print(f"📄 TESTING {i}/{len(test_resumes)}: {resume_file}")
        print(f"{'='*70}")
        
        resume_path = Path("resumes for testing") / resume_file
        
        if not resume_path.exists():
            print(f"❌ Resume not found: {resume_path}")
            results[resume_file] = {'success': False, 'error': 'File not found'}
            continue
        
        print(f"📊 File size: {resume_path.stat().st_size:,} bytes")
        
        try:
            # Test the hybrid endpoint
            start_time = time.time()
            
            with open(resume_path, 'rb') as f:
                files = {'file': (resume_path.name, f, 'application/pdf')}
                response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
            
            processing_time = time.time() - start_time
            
            print(f"📊 Response Status: {response.status_code}")
            print(f"⏱️ Processing Time: {processing_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                
                # Check if there's an error in the response
                if result.get('error'):
                    print(f"⚠️ Response contains error: {result['error']}")
                    results[resume_file] = {'success': False, 'error': result['error'], 'time': processing_time}
                    continue
                
                # Analyze the results
                name = result.get('name', 'Unknown')
                education_count = len(result.get('education', []))
                experience_count = len(result.get('experience', []))
                skills_count = len(result.get('skills', []))
                projects_count = len(result.get('projects', []))
                certifications_count = len(result.get('certifications', []))
                achievements_count = len(result.get('achievements', []))
                
                print(f"📊 Results:")
                print(f"   👤 Name: '{name}'")
                print(f"   🎓 Education: {education_count} entries")
                print(f"   💼 Experience: {experience_count} entries")
                print(f"   🛠️ Skills: {skills_count} items")
                print(f"   🚀 Projects: {projects_count} items")
                print(f"   🏆 Certifications: {certifications_count} items")
                print(f"   🏅 Achievements: {achievements_count} items")
                
                # Check if we got meaningful data
                total_data_points = education_count + experience_count + skills_count + projects_count + certifications_count + achievements_count
                
                if name == 'Unknown' and total_data_points == 0:
                    print(f"❌ FAILED: Got fallback response (no meaningful data)")
                    results[resume_file] = {
                        'success': False, 
                        'error': 'Fallback response', 
                        'time': processing_time,
                        'name': name,
                        'data_points': total_data_points
                    }
                else:
                    print(f"✅ SUCCESS: Meaningful data extracted!")
                    print(f"   📊 Total data points: {total_data_points}")
                    results[resume_file] = {
                        'success': True, 
                        'time': processing_time,
                        'name': name,
                        'data_points': total_data_points,
                        'counts': {
                            'education': education_count,
                            'experience': experience_count,
                            'skills': skills_count,
                            'projects': projects_count,
                            'certifications': certifications_count,
                            'achievements': achievements_count
                        }
                    }
                
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ FAILED: {error_msg}")
                results[resume_file] = {'success': False, 'error': error_msg, 'time': processing_time}
                
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            print(f"❌ EXCEPTION: {error_msg}")
            results[resume_file] = {'success': False, 'error': error_msg, 'time': 0}
        
        # Small delay between tests
        time.sleep(2)
    
    # Comprehensive Summary
    print(f"\n{'='*70}")
    print("📊 COMPREHENSIVE SUMMARY")
    print(f"{'='*70}")
    
    successful = [name for name, data in results.items() if data['success']]
    failed = [name for name, data in results.items() if not data['success']]
    
    print(f"✅ Successful: {len(successful)}/{len(test_resumes)} ({len(successful)/len(test_resumes)*100:.1f}%)")
    print(f"❌ Failed: {len(failed)}/{len(test_resumes)} ({len(failed)/len(test_resumes)*100:.1f}%)")
    
    if successful:
        print(f"\n✅ SUCCESSFUL RESUMES:")
        for name in successful:
            result_data = results[name]
            extracted_name = result_data.get('name', 'Unknown')
            processing_time = result_data.get('time', 0)
            data_points = result_data.get('data_points', 0)
            print(f"   ✅ {name}")
            print(f"      👤 Name: '{extracted_name}'")
            print(f"      📊 Data points: {data_points}")
            print(f"      ⏱️ Time: {processing_time:.1f}s")
    
    if failed:
        print(f"\n❌ FAILED RESUMES:")
        for name in failed:
            result_data = results[name]
            error = result_data.get('error', 'Unknown error')
            processing_time = result_data.get('time', 0)
            print(f"   ❌ {name}")
            print(f"      🚫 Error: {error}")
            print(f"      ⏱️ Time: {processing_time:.1f}s")
    
    # Analysis
    print(f"\n🔍 ANALYSIS:")
    
    if len(successful) == len(test_resumes):
        print("🎉 PERFECT SUCCESS: All resumes working!")
        print("✅ JSON self-healing system is working correctly")
        print("✅ Character replacement is handling all special characters")
        print("✅ ```json issue has been completely resolved")
    elif len(successful) > len(failed):
        print(f"⚠️ PARTIAL SUCCESS: {len(successful)}/{len(test_resumes)} working")
        print("✅ JSON self-healing is working for some resumes")
        print("❌ Some resumes still have issues - need further investigation")
        print("📝 Check the latest prompt logs for failed resumes")
    elif len(successful) > 0:
        print(f"⚠️ MIXED RESULTS: {len(successful)}/{len(test_resumes)} working")
        print("✅ Some improvement from JSON self-healing")
        print("❌ Majority still failing - may need alternative approach")
    else:
        print("❌ COMPLETE FAILURE: No resumes working")
        print("❌ JSON self-healing system not working as expected")
        print("❌ Need to investigate fundamental issues")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if len(failed) > 0:
        print("1. Check the latest prompt logs for failed resumes")
        print("2. Look for patterns in the failures")
        print("3. Consider additional character replacements")
        print("4. Verify self-healing prompt effectiveness")
        print("5. Test individual components in isolation")
    else:
        print("✅ All resumes working - ready for production!")
        print("✅ JSON self-healing system is robust")
        print("✅ Character replacement is comprehensive")
    
    return results

if __name__ == "__main__":
    results = test_comprehensive_resumes()
    
    print(f"\n📝 NEXT STEPS:")
    print("1. Review the detailed logs above")
    print("2. Check prompt_logs for self-healing attempts")
    print("3. Verify character replacement effectiveness")
    print("4. Consider additional improvements if needed")
