#!/usr/bin/env python3
"""
Demonstration of how the regex method handles different section orders.
"""

import sys
import os
import re

# Add the current directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import extract_sections_regex

def demo_different_orders():
    """Demonstrate regex extraction with different section orders."""
    
    print("🔍 Regex Method: Order Flexibility Demonstration")
    print("=" * 70)
    print("Testing how regex handles different section arrangements")
    print()
    
    # Format A: Traditional order (Summary first)
    resume_format_a = """John Doe
Email: <EMAIL>
Phone: (*************

SUMMARY
Experienced full-stack developer with 5+ years of expertise in web development.
Passionate about creating scalable solutions and leading development teams.

EDUCATION
Bachelor of Science in Computer Science
Tech University, 2018-2022
GPA: 3.8/4.0

EXPERIENCE
Senior Software Engineer
TechCorp Inc., 2022-Present
• Led development of microservices architecture
• Mentored junior developers

Software Engineer
StartupXYZ, 2020-2022
• Built REST APIs using Python and Django
• Implemented CI/CD pipelines

SKILLS
Programming: Python, JavaScript, Java
Frameworks: Django, React, Spring Boot
Databases: PostgreSQL, MongoDB

PROJECTS
E-commerce Platform
• Full-stack application using React and Django
• Integrated payment processing with Stripe
"""

    # Format B: Different order (Education first, Projects before Summary)
    resume_format_b = """Jane Smith
Email: <EMAIL>
Phone: (*************

EDUCATION
Master of Computer Science
Elite University, 2020-2022
GPA: 3.9/4.0
Specialization: Machine Learning

Bachelor of Engineering
State College, 2016-2020
GPA: 3.7/4.0

PROJECTS
Machine Learning Recommendation System
• Developed collaborative filtering algorithm
• Achieved 25% improvement in user engagement
• Technologies: Python, TensorFlow, scikit-learn

Mobile App Development
• Created cross-platform mobile application
• Published on both iOS and Android stores
• Technologies: React Native, Firebase

SUMMARY
Results-driven software engineer with expertise in machine learning and mobile development.
Strong background in data science and full-stack development.

EXPERIENCE
ML Engineer
DataTech Solutions, 2022-Present
• Developed predictive models for customer analytics
• Implemented real-time recommendation systems

Junior Developer
Innovation Labs, 2020-2022
• Built mobile applications using React Native
• Collaborated with UX team on user interface design

SKILLS
Programming: Python, JavaScript, Swift, Kotlin
ML Libraries: TensorFlow, PyTorch, scikit-learn
Mobile: React Native, Flutter
"""

    # Test both formats
    formats = [
        ("Format A (Traditional Order)", resume_format_a),
        ("Format B (Education First)", resume_format_b)
    ]
    
    for format_name, resume_text in formats:
        print(f"\n📄 Testing {format_name}")
        print("-" * 50)
        print(f"📝 Text length: {len(resume_text)} characters")
        
        # Show the section order in this resume
        print("📋 Section order in this resume:")
        lines = resume_text.split('\n')
        section_order = []
        for i, line in enumerate(lines):
            line_upper = line.strip().upper()
            if line_upper in ['SUMMARY', 'EDUCATION', 'EXPERIENCE', 'SKILLS', 'PROJECTS']:
                section_order.append(f"{len(section_order)+1}. {line_upper}")
        
        for order in section_order:
            print(f"   {order}")
        
        # Extract sections using regex
        try:
            sections, confidence_scores = extract_sections_regex(resume_text, f"{format_name.lower().replace(' ', '_')}.txt", "")
            
            print(f"\n✅ Extraction successful!")
            print(f"📊 Sections found: {len([s for s in sections.values() if s and s.strip()])}")
            
            # Show extracted sections with their content length
            print("\n📋 Extracted sections:")
            for section_name, content in sections.items():
                if content and content.strip():
                    confidence = confidence_scores.get(section_name, 0.0)
                    content_length = len(content.strip())
                    preview = content.strip()[:60].replace('\n', ' ')
                    print(f"   ✅ {section_name.upper()}: {content_length} chars, conf: {confidence:.2f}")
                    print(f"      Preview: {preview}...")
                else:
                    print(f"   ❌ {section_name.upper()}: Not found")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()

def explain_algorithm_steps():
    """Explain the step-by-step algorithm."""
    
    print("🔧 Algorithm Explanation: Step-by-Step Process")
    print("=" * 70)
    
    # Sample text to demonstrate
    sample_text = """Contact Info Here

EDUCATION
Bachelor's Degree...

PROJECTS  
Project 1 details...
Project 2 details...

SUMMARY
Professional summary...

SKILLS
Python, JavaScript..."""

    print("📝 Sample Resume Text:")
    print(sample_text)
    print()
    
    # Show the regex patterns
    section_patterns = {
        'summary': r'(?i)(?:^|\n)\s*(?:summary|objective|profile)\s*:?\s*\n',
        'education': r'(?i)(?:^|\n)\s*(?:education|academic)\s*:?\s*\n',
        'skills': r'(?i)(?:^|\n)\s*(?:skills|technical\s+skills)\s*:?\s*\n',
        'projects': r'(?i)(?:^|\n)\s*(?:projects|personal\s+projects)\s*:?\s*\n',
    }
    
    print("🔍 Step 1: Pattern Matching")
    print("-" * 30)
    print("Scanning entire text for section headers...")
    
    # Simulate the detection process
    section_positions = []
    for section_name, pattern in section_patterns.items():
        matches = list(re.finditer(pattern, sample_text))
        for match in matches:
            position = {
                'name': section_name,
                'start': match.end(),
                'header_start': match.start(),
                'header_text': match.group().strip()
            }
            section_positions.append(position)
            print(f"   Found '{section_name}' at position {match.start()}-{match.end()}")
    
    print(f"\n📊 Step 2: Sorting by Position")
    print("-" * 30)
    section_positions.sort(key=lambda x: x['start'])
    print("Sections sorted by their actual position in text:")
    for i, section in enumerate(section_positions):
        print(f"   {i+1}. {section['name'].upper()} (starts at position {section['start']})")
    
    print(f"\n✂️ Step 3: Content Extraction")
    print("-" * 30)
    print("Extracting content between consecutive section boundaries:")
    
    for i, section in enumerate(section_positions):
        section_name = section['name']
        content_start = section['start']
        
        # Find the end position
        if i + 1 < len(section_positions):
            content_end = section_positions[i + 1]['header_start']
            end_desc = f"next section at {content_end}"
        else:
            content_end = len(sample_text)
            end_desc = "end of document"
        
        content = sample_text[content_start:content_end].strip()
        print(f"   {section_name.upper()}: chars {content_start} to {content_end} ({end_desc})")
        print(f"      Content: '{content[:40]}...'")

def show_flexibility_benefits():
    """Show the benefits of this approach."""
    
    print("\n🎯 Benefits of Position-Based Extraction")
    print("=" * 70)
    
    benefits = [
        {
            "benefit": "Order Independence",
            "description": "Works regardless of section order in resume",
            "example": "EDUCATION first or SUMMARY first - both work perfectly"
        },
        {
            "benefit": "Automatic Adaptation", 
            "description": "Automatically adapts to the document's structure",
            "example": "No need to predefine expected section order"
        },
        {
            "benefit": "Complete Coverage",
            "description": "Extracts all content between sections",
            "example": "Nothing gets missed between section boundaries"
        },
        {
            "benefit": "Last Section Handling",
            "description": "Properly handles the final section",
            "example": "Extracts from last header to end of document"
        },
        {
            "benefit": "Multiple Formats",
            "description": "Handles various resume formats and styles",
            "example": "Traditional, modern, academic, technical resumes"
        }
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"{i}. ✅ {benefit['benefit']}")
        print(f"   📝 {benefit['description']}")
        print(f"   💡 Example: {benefit['example']}")
        print()

if __name__ == "__main__":
    demo_different_orders()
    explain_algorithm_steps()
    show_flexibility_benefits()
    
    print("🎉 Demonstration Complete!")
    print("\n📚 Key Takeaways:")
    print("✅ Regex method is completely order-independent")
    print("✅ Sections are detected by content, not position")
    print("✅ Algorithm adapts to any resume structure")
    print("✅ Works with traditional and non-traditional formats")
    print("✅ Maintains high accuracy regardless of section order")
