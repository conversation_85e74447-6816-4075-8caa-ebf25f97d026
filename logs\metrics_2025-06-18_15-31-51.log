{"event": "session_start", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "timestamp": "2025-06-18T15:31:51.965347", "message": "New API session started"}
{"event": "request_start", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "27bfb041-f463-44eb-951b-c8a7bfefa7be", "endpoint": "/", "timestamp": "2025-06-18T15:32:17.414693", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "27bfb041-f463-44eb-951b-c8a7bfefa7be", "endpoint": "/", "timestamp": "2025-06-18T15:32:17.415693", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "25dda91c-358f-4df7-a888-0c095134762b", "endpoint": "/", "timestamp": "2025-06-18T15:32:57.535167", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "25dda91c-358f-4df7-a888-0c095134762b", "endpoint": "/", "timestamp": "2025-06-18T15:32:57.537168", "total_time_seconds": 0.001003265380859375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.576645", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.594644", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.594644", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.594644", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.594644", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:32:59.594644", "file_processing_time": 0.014999866485595703, "message": "Custom metric: file_processing_time=0.014999866485595703"}
{"event": "request_complete", "session_id": "e12ffae2-74fc-494d-a4c2-392ce26141e0", "request_id": "222406f1-fac9-469a-ab4d-ed5f54f0e494", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:33:31.219632", "total_time_seconds": 31.642986297607422, "status_code": 200, "message": "Request completed in 31.6430s with status 200"}
