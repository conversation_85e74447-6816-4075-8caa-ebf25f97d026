#!/usr/bin/env python3
"""
STEP 4: Test the EXACT JSON being processed in the live flow by adding debug logging.
"""

import json

def test_exact_live_json():
    """Test the exact JSON from the live flow with markdown."""
    
    print("🔍 STEP 4: Testing Exact Live JSON")
    print("=" * 60)
    
    # This is the exact JSON from the self-healing log WITH markdown (lines 178-264)
    exact_live_json = '''```json
{
    "name": "Meesala Sree Sai Nat<PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Sri Chaitanya Junior College",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Kendriya Vidyalaya No-1, Air Force Academy",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "Mongo DB",
        "Django",
        "Express JS",
        "NodeJS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "Hadoop HDFS",
        "Data Structure"
    ],
    "experience": [
        {
            "company_name": "Spectacom Global, Gurugram, Haryana",
            "role": "SDE INTERN",
            "duration": "January 2025 - Present",
            "key_responsibilities": "Developed a leaderboard system in Django, supporting regional and individual rankings.\\nIntegrated detailed participant timing records with numerical insights for performance tracking.\\nOptimized Django ORM queries for ecient leaderboard updates and retrieval."
        }
    ],
    "projects": [
        {
            "name": "Laundry Pro",
            "description": "Laundry Pro simplies laundry services with an ecient platform for students to place and track orders\\nTech Stack: Built with Node.js, Express, Prisma, SQL.\\nFeatures:\\n{Secure login for students and admins.\\n{Admin tools for order status updates and record management."
        },
        {
            "name": "Fabrix",
            "description": "Developed a dynamic, simulated e-commerce platform with secure user authentication and product\\nbrowsing using dummy data for a modern, responsive shopping experience.\\nTech Stack: Built with React ,Tailwind CSS , and integrated Razorpay for secure\\ncheckout."
        },
        {
            "name": "Crypto - manager",
            "description": "Created a web application for managing and tracking cryptocurrency investments with real-time data and\\na responsive, interactive user interface.\\nTech Stack: Built with HTML ,CSS and JavaScript .\\nFeatures:\\n{Real-time API integration to fetch live cryptocurrency data and market trends.\\n{Simulated trading for buying, selling, and holding cryptocurrencies in a risk-free environment."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Rajya Puraskar"
    ],
    "domain_of_interest": [
        "Data Structure"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Ranked in the top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology.",
        "Qualied for the Semi-Finals inBadminton at the University Sports Fest."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Full Stack Developer skilled in React, Tailwind CSS, Node.js, Express, Django, and MySQL, with experience in scalable APIs\\nand dynamic UIs. Passionate about learning and building high-performing web applications.",
    "personal_projects": [],
    "social_media": [
        "LinkedIn",
        "Github",
        "Leetcode"
    ]
}
```'''

    print(f"📄 Testing exact live JSON with markdown:")
    print(f"Length: {len(exact_live_json)} characters")
    print(f"Contains ```json: {'```json' in exact_live_json}")
    print(f"Contains ```: {'```' in exact_live_json}")
    
    # Test our EXACT nuclear cleanup logic from main.py
    print(f"\n🔧 Testing EXACT Nuclear Cleanup Logic:")
    
    # NUCLEAR MARKDOWN REMOVAL - LLM keeps ignoring instructions
    print("Applying NUCLEAR markdown removal to self-healing response")
    
    # Step 1: Strip whitespace
    healed_json = exact_live_json.strip()
    print(f"After strip: {len(healed_json)} chars")
    
    # Step 2: Remove ALL possible markdown variations
    healed_json = healed_json.replace('```json', '').replace('```', '').strip()
    print(f"After markdown removal: {len(healed_json)} chars")
    
    # Step 3: Force extract between first { and last }
    start_brace = healed_json.find('{')
    end_brace = healed_json.rfind('}')
    if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
        healed_json = healed_json[start_brace:end_brace+1]
        print(f"NUCLEAR: Extracted JSON between braces, length: {len(healed_json)}")
    
    # Step 4: Additional cleanup
    healed_json = healed_json.replace('`', '').strip()
    print(f"After final cleanup: {len(healed_json)} chars")
    
    print(f"NUCLEAR cleanup result: {len(healed_json)} chars, starts with: {repr(healed_json[:30])}")
    
    # Test JSON parsing
    print(f"\n🧪 Testing JSON Parsing:")
    try:
        parsed_data = json.loads(healed_json)
        print("✅ SUCCESS: NUCLEAR JSON self-healing worked!")
        print(f"Name: {parsed_data.get('name')}")
        print(f"Education: {len(parsed_data.get('education', []))} entries")
        print(f"Skills: {len(parsed_data.get('skills', []))} items")
        print(f"Experience: {len(parsed_data.get('experience', []))} entries")
        print(f"Projects: {len(parsed_data.get('projects', []))} items")
        return True, parsed_data
    except json.JSONDecodeError as heal_error:
        print(f"❌ FAILED: NUCLEAR JSON self-healing still failed: {heal_error}")
        print(f"Problematic JSON preview: {repr(healed_json[:100])}")
        print(f"Error at position: {heal_error.pos}")
        if heal_error.pos < len(healed_json):
            char_at_error = healed_json[heal_error.pos]
            print(f"Character at error: '{char_at_error}' (ord: {ord(char_at_error)})")
            context = healed_json[max(0, heal_error.pos-30):heal_error.pos+30]
            print(f"Context: {repr(context)}")
        return False, None

if __name__ == "__main__":
    print("🐛 SYSTEMATIC DEBUGGING - STEP 4")
    print()
    
    success, data = test_exact_live_json()
    
    print(f"\n{'='*60}")
    print("📊 STEP 4 RESULTS")
    print(f"{'='*60}")
    
    if success:
        print("🎉 SUCCESS: The exact live JSON parses correctly with nuclear cleanup!")
        print("✅ Our nuclear cleanup logic is working perfectly")
        print("✅ The issue must be in the code flow or logging")
        print("\n💡 This means:")
        print("- The nuclear cleanup logic is correct")
        print("- The JSON should be parsing successfully")
        print("- There might be a bug in the error handling or flow")
        print("- We need to check why the success isn't being returned")
    else:
        print("❌ FAILED: The exact live JSON has issues even with nuclear cleanup")
        print("🔧 The nuclear cleanup logic needs further improvement")
        print("📝 Check the specific error above")
        print("\n💡 Next steps:")
        print("- Fix the specific character or syntax issue")
        print("- Enhance the nuclear cleanup logic")
        print("- Test again until it works")
