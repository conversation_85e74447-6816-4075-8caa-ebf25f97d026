{"event": "session_start", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "timestamp": "2025-06-18T14:48:32.924318", "message": "New API session started"}
{"event": "request_start", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.387495", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.404493", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.423392", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.423392", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.423392", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:48:42.423392", "file_processing_time": 0.015000104904174805, "message": "Custom metric: file_processing_time=0.015000104904174805"}
{"event": "request_complete", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "60e8cfee-8eb7-4d77-a5f5-e4bdc38cf5a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:49:08.035364", "total_time_seconds": 25.647869348526, "status_code": 200, "message": "Request completed in 25.6479s with status 200"}
{"event": "request_start", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "a12c8735-f216-4abf-8b40-96b9bec76816", "endpoint": "/", "timestamp": "2025-06-18T14:52:02.339541", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "request_id": "a12c8735-f216-4abf-8b40-96b9bec76816", "endpoint": "/", "timestamp": "2025-06-18T14:52:02.341822", "total_time_seconds": 0.0022814273834228516, "status_code": 200, "message": "Request completed in 0.0023s with status 200"}
{"event": "session_end", "session_id": "975d4258-978a-4b53-a870-de39c5bce04d", "timestamp": "2025-06-18T14:52:02.476242", "message": "API session ended"}
