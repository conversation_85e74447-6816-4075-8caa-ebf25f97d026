{"event": "session_start", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "timestamp": "2025-06-18T14:29:45.142281", "message": "New API session started"}
{"event": "request_start", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "2187fd62-e0fb-4acb-9d54-0440a555420f", "endpoint": "/", "timestamp": "2025-06-18T14:30:28.626533", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "2187fd62-e0fb-4acb-9d54-0440a555420f", "endpoint": "/", "timestamp": "2025-06-18T14:30:28.627534", "total_time_seconds": 0.001001119613647461, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.705603", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.739692", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.739692", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.739692", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.740691", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:30.740691", "file_processing_time": 0.031086444854736328, "message": "Custom metric: file_processing_time=0.031086444854736328"}
{"event": "request_complete", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "987ac380-0024-485d-afec-aab4a42f42b2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:30:55.305582", "total_time_seconds": 24.599978923797607, "status_code": 200, "message": "Request completed in 24.6000s with status 200"}
{"event": "request_start", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "77d4f94a-1335-4390-be71-c7b9c61786b9", "endpoint": "/", "timestamp": "2025-06-18T14:37:23.814313", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "request_id": "77d4f94a-1335-4390-be71-c7b9c61786b9", "endpoint": "/", "timestamp": "2025-06-18T14:37:23.815313", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "session_end", "session_id": "8668a940-79bb-4168-8bca-b8c925b23109", "timestamp": "2025-06-18T14:37:23.950923", "message": "API session ended"}
