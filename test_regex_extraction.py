#!/usr/bin/env python3
"""
Quick test script for the regex-based section extraction function.
"""

import sys
import os

# Add the current directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import extract_sections_regex, extract_skills_from_entire_text

def test_regex_extraction():
    """Test the regex extraction function with sample resume text."""
    
    # Read the sample resume text
    sample_file = "resume_extracted_text/Resume-Raman_Luhach.txt"
    
    if not os.path.exists(sample_file):
        print(f"❌ Sample file not found: {sample_file}")
        return
    
    with open(sample_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract the actual resume text (skip the debug header)
    lines = content.split('\n')
    resume_text = ""
    start_extracting = False
    
    for line in lines:
        if line.startswith("# ================================================"):
            start_extracting = True
            continue
        if start_extracting:
            resume_text += line + "\n"
    
    print("🧪 Testing Regex-Based Section Extraction")
    print("=" * 60)
    print(f"📄 Sample file: {sample_file}")
    print(f"📝 Text length: {len(resume_text)} characters")
    print()
    
    # Test the regex extraction
    try:
        sections, confidence_scores = extract_sections_regex(resume_text, "Resume-Raman_Luhach.txt", "")
        
        print("✅ Regex extraction successful!")
        print(f"📊 Sections found: {len(sections)}")
        print(f"📈 Average confidence: {sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0:.2f}")
        print()
        
        print("📋 Extracted Sections:")
        print("-" * 40)
        
        for section_name, content in sections.items():
            confidence = confidence_scores.get(section_name, 0.0)
            status = "✅" if content and content.strip() else "❌"
            
            print(f"\n{status} {section_name.upper()} (Confidence: {confidence:.2f})")
            print("-" * 30)
            
            if content and content.strip():
                # Show first 200 characters
                preview = content.strip()[:200]
                if len(content.strip()) > 200:
                    preview += "..."
                print(preview)
            else:
                print("(No content found)")
        
        print("\n" + "=" * 60)
        print("🎯 Analysis:")
        
        # Analyze the results
        found_sections = [s for s, c in sections.items() if c and c.strip()]
        missing_sections = [s for s, c in sections.items() if not c or not c.strip()]
        
        print(f"✅ Found sections ({len(found_sections)}): {', '.join(found_sections)}")
        if missing_sections:
            print(f"❌ Missing sections ({len(missing_sections)}): {', '.join(missing_sections)}")
        
        # Test skills extraction specifically
        print("\n🔧 Testing Skills Extraction from Entire Text:")
        skills_content = extract_skills_from_entire_text(resume_text)
        if skills_content:
            print(f"✅ Skills found: {skills_content[:100]}...")
        else:
            print("❌ No skills found using pattern matching")
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()

def test_section_patterns():
    """Test the regex patterns with sample section headers."""
    
    print("\n🔍 Testing Section Header Patterns")
    print("=" * 60)
    
    # Sample section headers to test
    test_headers = [
        "PROFESSIONAL SUMMARY",
        "EDUCATION", 
        "PROJECTS",
        "CERTIFICATIONS",
        "SKILLS",
        "EXTRA-CURRICULAR ACTIVITIES",
        "Experience:",
        "Work Experience",
        "Technical Skills:",
        "Summary:",
        "Objective",
        "Languages",
        "Achievements"
    ]
    
    # Import the patterns from the function
    import re
    
    section_patterns = {
        'summary': r'(?i)(?:^|\n)\s*(?:summary|objective|profile|professional\s+summary|career\s+objective|about\s+me)\s*:?\s*\n',
        'education': r'(?i)(?:^|\n)\s*(?:education|academic|qualifications|educational\s+background)\s*:?\s*\n',
        'experience': r'(?i)(?:^|\n)\s*(?:experience|work\s+experience|employment|professional\s+experience|career\s+history)\s*:?\s*\n',
        'skills': r'(?i)(?:^|\n)\s*(?:skills|technical\s+skills|core\s+competencies|technologies|expertise)\s*:?\s*\n',
        'projects': r'(?i)(?:^|\n)\s*(?:projects|personal\s+projects|key\s+projects|notable\s+projects)\s*:?\s*\n',
        'certifications': r'(?i)(?:^|\n)\s*(?:certifications|certificates|professional\s+certifications|licenses)\s*:?\s*\n',
        'achievements': r'(?i)(?:^|\n)\s*(?:achievements|accomplishments|awards|honors|recognition)\s*:?\s*\n',
        'languages': r'(?i)(?:^|\n)\s*(?:languages|language\s+skills|linguistic\s+skills)\s*:?\s*\n'
    }
    
    print("Testing header patterns:")
    for header in test_headers:
        test_text = f"\n{header}\n"
        matches = []
        
        for section_name, pattern in section_patterns.items():
            if re.search(pattern, test_text):
                matches.append(section_name)
        
        status = "✅" if matches else "❌"
        match_str = f" -> {', '.join(matches)}" if matches else ""
        print(f"{status} '{header}'{match_str}")

if __name__ == "__main__":
    test_regex_extraction()
    test_section_patterns()
    
    print("\n🎉 Testing completed!")
    print("\n📝 Next steps:")
    print("1. Start the API server: python main.py")
    print("2. Test the /section3 endpoint with: python testing/test_section3_regex.py")
    print("3. Compare with other methods using the test scripts")
