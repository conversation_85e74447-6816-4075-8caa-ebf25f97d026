# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:52:49
# Text Length: 2144 characters
# ================================================

<PERSON><PERSON>
LinkedIn Github Codeforces Leetcode Personal Portfolio
PROFESSIONAL SUMMARY
Frontend developer aspiring to be a full-stack web developer, with a keen interest in AI and emerging technologies. Solved
100+ questions and 1400+ rating on leetcode.
EDUCATION
Bachelor of Technology (Articial intelligence) 2024 - 2028
Newton School of Technology , Rishihood University Grade: 7.0/10.0
Intermediate (Class XII) 2022 - 2023
JKG SCHOOL GHAZIABAD Grade: 68.0%
Matriculation (Class X) 2020 - 2021
JKG SCHOOL GHAZIABAD Grade: 73.0%
PROJECTS
Edusphere - College Finder , ( Github ) ( Demo ) April 2025
Tech Stack: HTML, CSS, JavaScript, React
Description: Developed an interactive and responsive web application to help students search and explore colleges based
on their preferences.
Features: Implemented real-time search and lter functionality using React, designed a clean and accessible UI with
HTML and CSS, and ensured smooth navigation and performance across devices.
Portfolio , ( Github ) ( Demo ) February 2025
Tech Stack: HTML, CSS
Description: Designed and developed a user-friendly personal portfolio webpage.
Features: Implemented an intuitive UI/UX with smooth animations and transitions, ensuring enhanced accessibility and
seamless navigation.
Delicacy - Fruits and Vegetables , ( Github ) ( Demo ) February 2025
Tech Stack: HTML, CSS, JavaScript
Description: Developed a user-friendly website for seamless online fruit and vegetable shopping.
Features : Designed an intuitive and responsive UI for seamless navigation.
CERTIFICATIONS
Visual Vortex 1.0 , Google Developers Group ( Link ) November 2024
We participated in a UI/UX Figma Hackathon, where we designed an intuitive and user-friendly digital wallet UI, focusing on
seamless navigation and modern aesthetics.
SKILLS
Computer Languages: SQL, C++, Python, JavaScript, CSS, HTML, Java
Software Packages: Pandas
EXTRA-CURRICULAR ACTIVITIES
Participated in Smart India Hackathon (SIH) at Bennett University and Ranked Under 50.
Project - Build Full Stack Website on Fitness Tracker theme named as (Pranay Shakti).
Tech Stacks - HTML , CSS , JAVASCRIPT.
