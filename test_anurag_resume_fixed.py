#!/usr/bin/env python3
"""
Test <PERSON>urag's resume with the improved hybrid endpoint.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_anurag_resume():
    """Test Anurag's resume with the improved prompt."""

    print("🧪 Testing Anurag's Resume with Improved Hybrid Endpoint")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Try to find <PERSON><PERSON><PERSON>'s resume, fallback to another resume for testing
    possible_paths = [
        Path("resumes for testing/Resume-Anurag Pandey.pdf"),
        Path("resumes for testing/Anurag Pandey.pdf"),
        Path("resumes for testing/Resume-AKASH G.pdf"),  # Fallback for testing
    ]

    resume_path = None
    for path in possible_paths:
        if path.exists():
            resume_path = path
            break

    if not resume_path:
        print(f"❌ No test resume found. Checked: {[str(p) for p in possible_paths]}")
        return

    print(f"📄 Testing: {resume_path.name}")
    print(f"📊 File size: {resume_path.stat().st_size:,} bytes")
    
    try:
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                if result.get('error_details'):
                    print(f"   Details: {result['error_details']}")
                return False
            
            # Detailed analysis
            print(f"\n📊 Detailed Analysis:")
            print(f"   👤 Name: '{result.get('name', 'Not found')}'")
            print(f"   📧 Email: '{result.get('email', 'Not found')}'")
            print(f"   📞 Phone: '{result.get('phone', 'Not found')}'")
            
            # Education analysis
            education = result.get('education', [])
            print(f"   🎓 Education: {len(education)} entries")
            for i, edu in enumerate(education):
                print(f"      {i+1}. {edu.get('degree', 'N/A')} at {edu.get('institution', 'N/A')} ({edu.get('year', 'N/A')})")
            
            # Skills analysis
            skills = result.get('skills', [])
            print(f"   🛠️ Skills: {len(skills)} items")
            if skills:
                print(f"      Sample: {skills[:5]}")
            
            # Experience analysis
            experience = result.get('experience', [])
            print(f"   💼 Experience: {len(experience)} entries")
            for i, exp in enumerate(experience):
                company = exp.get('company_name', 'N/A')
                role = exp.get('role', 'N/A')
                duration = exp.get('duration', 'N/A')
                print(f"      {i+1}. {role} at {company} ({duration})")
                
                # Check if this looks like real work experience
                responsibilities = exp.get('key_responsibilities', '')
                if 'basketball' in responsibilities.lower() or 'mathematics' in responsibilities.lower():
                    print(f"         ⚠️ WARNING: This looks like extra-curricular activity, not work experience!")
            
            # Projects analysis
            projects = result.get('projects', [])
            print(f"   🚀 Projects: {len(projects)} items")
            for i, proj in enumerate(projects):
                print(f"      {i+1}. {proj.get('name', 'N/A')}")
            
            # Certifications analysis
            certifications = result.get('certifications', [])
            print(f"   🏆 Certifications: {len(certifications)} items")
            for i, cert in enumerate(certifications):
                if isinstance(cert, dict):
                    print(f"      {i+1}. ❌ OBJECT: {cert} (should be string!)")
                else:
                    print(f"      {i+1}. ✅ STRING: {cert}")
            
            # Achievements analysis
            achievements = result.get('achievements', [])
            print(f"   🏅 Achievements: {len(achievements)} items")
            for i, achievement in enumerate(achievements):
                print(f"      {i+1}. {achievement}")
            
            # Social media analysis
            social_media = result.get('social_media', [])
            print(f"   📱 Social Media: {len(social_media)} items")
            if social_media:
                print(f"      {social_media}")
            
            # Summary analysis
            summary = result.get('summary')
            print(f"   📝 Summary: {'✅ Present' if summary else '❌ Missing'}")
            if summary:
                print(f"      Preview: {summary[:100]}...")
            
            # Check for schema compliance
            print(f"\n🔍 Schema Compliance Check:")
            
            # Check certifications format
            cert_format_ok = all(isinstance(cert, str) for cert in certifications)
            print(f"   🏆 Certifications format: {'✅ All strings' if cert_format_ok else '❌ Contains objects'}")
            
            # Check achievements format
            ach_format_ok = all(isinstance(ach, str) for ach in achievements)
            print(f"   🏅 Achievements format: {'✅ All strings' if ach_format_ok else '❌ Contains objects'}")
            
            # Check skills format
            skills_format_ok = all(isinstance(skill, str) for skill in skills)
            print(f"   🛠️ Skills format: {'✅ All strings' if skills_format_ok else '❌ Contains objects'}")
            
            # Check experience content
            exp_content_ok = True
            for exp in experience:
                responsibilities = exp.get('key_responsibilities', '').lower()
                if any(word in responsibilities for word in ['basketball', 'olympiad', 'mathematics competition']):
                    exp_content_ok = False
                    break
            
            print(f"   💼 Experience content: {'✅ Professional work' if exp_content_ok else '❌ Contains extra-curricular'}")
            
            # Overall assessment
            overall_ok = cert_format_ok and ach_format_ok and skills_format_ok and exp_content_ok
            print(f"\n🎯 Overall Assessment: {'✅ GOOD' if overall_ok else '❌ NEEDS IMPROVEMENT'}")
            
            return overall_ok
            
        else:
            print("❌ FAILED!")
            print(f"Error: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"Error detail: {error_data.get('detail', 'No detail provided')}")
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    
    success = test_anurag_resume()
    
    print(f"\n{'='*70}")
    print("📊 TEST SUMMARY")
    print(f"{'='*70}")
    
    if success:
        print("🎉 SUCCESS: Anurag's resume processed correctly!")
        print("✅ Schema compliance achieved")
        print("✅ Content properly classified")
        print("✅ JSON format correct")
    else:
        print("❌ ISSUES DETECTED:")
        print("- Check schema compliance above")
        print("- Review content classification")
        print("- Verify JSON format")
        print("\n🔧 Next steps:")
        print("1. Check the latest prompt logs")
        print("2. Review the LLM response format")
        print("3. Consider further prompt improvements")

if __name__ == "__main__":
    main()
