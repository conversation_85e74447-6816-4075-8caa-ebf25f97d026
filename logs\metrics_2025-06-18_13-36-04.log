{"event": "session_start", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "timestamp": "2025-06-18T13:36:04.489897", "message": "New API session started"}
{"event": "request_start", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "fc5d3751-b117-4bf7-8e83-2598189e4f79", "endpoint": "/", "timestamp": "2025-06-18T13:36:06.432859", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "fc5d3751-b117-4bf7-8e83-2598189e4f79", "endpoint": "/", "timestamp": "2025-06-18T13:36:06.434860", "total_time_seconds": 0.002001047134399414, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "9cbdddea-e3c2-4204-aac4-27726b402d74", "endpoint": "/docs", "timestamp": "2025-06-18T13:36:09.471319", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "9cbdddea-e3c2-4204-aac4-27726b402d74", "endpoint": "/docs", "timestamp": "2025-06-18T13:36:09.471319", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "34dc7b96-56b4-44a3-b690-6230da04a243", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:36:09.564894", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "34dc7b96-56b4-44a3-b690-6230da04a243", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:36:09.587412", "total_time_seconds": 0.022518157958984375, "status_code": 200, "message": "Request completed in 0.0225s with status 200"}
{"event": "request_start", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.017770", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.052771", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.052771", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.053771", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.053771", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:25.053771", "file_processing_time": 0.0319979190826416, "message": "Custom metric: file_processing_time=0.0319979190826416"}
{"event": "request_complete", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "request_id": "20494fa1-3c33-4bb2-8c7a-fe60a041973f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:36:39.859175", "total_time_seconds": 14.841404438018799, "status_code": 500, "message": "Request completed in 14.8414s with status 500"}
{"event": "session_end", "session_id": "423432fe-8a70-432f-9dd6-98f62fbfe845", "timestamp": "2025-06-18T13:37:02.116885", "message": "API session ended"}
