# Extracted Text Debug File
# Source File: Abilash_. Sr. Java Full Stack Developer_9 years of Exp..docx
# Context: resume
# Extraction Method: docx_text
# Timestamp: 2025-06-09 14:21:53
# Text Length: 23979 characters
# ================================================

Abilash Paruchuri
Sr. Java Full Stack Developer
Email:                                                                                               Phone: (*************
Professional Summary:
Over 09 years of professional experience in analysis of requirements, software design, development, testing and maintenance of web applications using JAVA/J2EE technologies. 
Worked in challenging environments by following different methodologies like Agile/Scrum, Test Driven Development, Waterfall model and used JIRA as a tracking tool for the sprints. 
Experience of the design patterns and best practices with <PERSON>lang (and more) to start with design and get to deployable production systems including scale monitoring and instrumentation platform.
Hands on experience in rendering large data sets on the application view using ReactJS and Front-End technologies like Angular 12, Angular 10, Angular 9, Angular 8, ReactJS, NodeJS, JavaScript, Type Script, HTML5, CSS3/4, Bootstrap, jQuery, Tag Libraries, AJAX, Micro-Front End. 
Developed UI modules using Angular9, Node JS, React JS, Bootstrap, JavaScript, Ajax, jQuery, CSS3 and HTML5. Developed the web application to serve the JavaScript application using the web socket solution
Experience in building Responsive web pages using multiple technologies. 
Expertise in back-end development using Core Java, Collections, Spring, Hibernate, JDBC, Spring JDBC, Spring JPA, Struts, Web Services, REST(JAX-RS), SOAP(JAX-WS), SQL. 
Extensive experience in Spring frameworks such as Spring AOP, Spring transaction Management, Spring Data, Spring Batch, Spring Boot and Spring Cloud, Spring Data Rest. 
Hands on experience JBOSS DROOLS, Guvnor, writing Business rules, Decision Tables, Agenda Filter, Knowledge Base, and JBPM.
Implemented the Kotlin with Android studio for smooth functioning of the location based services GPS, Bluetooth on Android.
Expertise in integrating Terraform with Ansible, Packer to create and Version the AWS Infrastructure, designing, automating, implementing and sustainment of Amazon machine images (AMI) across the AWS Cloud environment.
Implemented SOAP and REST Services utilizing Spring MVC and JAX-RS and created unit test cases for REST endpoints utilizing Junit, Mockito and Spring Test. Used Postman to test web services. Experience in Deployment Automation and Containerization (Docker, Kubernetes). 
Designed and developed the detail page using Java 1.5 JSP 1.2 Struts 2.0 EJB 3.0 
Experience in Micro services development using spring boot, deployment in Pivotal Cloud Foundry, AWS and Azure. 
Have knowledge on Kotlin Android Extensions framework.
Implemented the application using Spring IOC, Spring MVC Framework, Spring Batch, Vert.x and also handled the security using Spring Security.
Tracked GCP (Google Cloud Platform) traffic for error-handling and troubleshooting scenarios.
Good experience in developing customized policies in APIGEEE and developing API's in APIGEEB. 
Have good experience working among databases like MySQL, MongoDB, PostgreSQL Configured Cloud message brokers like RabbitMQ, Kafka between microservices. 
Hands on exposure with multiple Application Servers like BEA Web Logic Application Server, Apache Tomcat, WebSphere and JBoss. 
Experience with configuration management tools like Ansible and Packer and automated the clo deployments using terraform.
Experience with CICD-Ant, Maven, Gradle, Jenkins, Concourse, CVS, Git, Git Hub. 
Designed and implemented AWS EC2 Server setup and deployment, build, maintenance, and configuration of various AWS resources like, EC2, S3, Lambda, ECS, EBS, Elastic Load Balancers, VPC, and IAM Security Groups that are utilized for different environments like dev, testing, Production. 
Expertise in using Apache Kafka in cluster as messaging system between the APIs and microservices. 
Extensively used the repositories and version control tools like Git, Subversion, CVS and SVN. 
Experience in using build/deploy tools such as Jenkins, Docker, and AWS for Continuous Integration & Deployment for Microservices. 
Experience in messaging handling services like Apache Kafka, JMS and RabbitMQ. 
Has very strong exposure on Performance using LoadRunner and JMeter.
Technical Skills:
Professional Experience:
Cable One, Phoenix, AZ	                                                                          			 Aug2022 - Present 
Role: Sr. Java Backend Developer
Responsibilities: 
Developed powerful Single Page Application and dynamic responsive Web Application with Angular 10. 
Developed many new functionalities to our application using Java script, Spring Boot, Angular. 
Worked with Angular 10 Controllers, Directives, Services, Pipes, Guards, Angular Routing. 
Used MYSQL for applications which were hosted in the cloud using Amazon RDS services. 
Using Kotlin for implementing new modules in the application.
Changing the AWS infrastructure Elastic Beanstalk to Docker with Kubernetes. 
Created JSON request and validated the response using postman Interceptor and Swagger UI. Built REST based microservice with Spring Boot 2.0 and deployed using cloud formation into the cluster running in AWS. 
Developed Cloud formation templates to create S3 buckets, AWS API gateway, Dynamo DB. 
Worked on writing unit test using Xunit, Moq, BDD 
Migrated some modules to KOTLIN from Java.
Automated the sap applications deployment using Ansible and terraform.
Created POC for SSl authorization using vert.x Framework.
Use BDD (Behavior-Driven Development) to develop HTML and jQuery code (Qunit, jQUnit). 
Converted application from Angular 6 to Angular 8 and guided the team in setting it up in their developer environments. 
Extensively worked on Drools Rules Engine and Expectation Engine for writing Business rules Validation.
Developed dynamic, component-based UIs and SPAs using Vue.js, optimizing performance with Vue Router and lazy loading
Created service accounts using Terraform with the respective roles to support the services deployed for managing the GCP TechStack
Managed complex application state using Vuex, ensuring efficient data flow across components
Extensive involvement with Collections using Array and Hashes in the Ruby environment.
As part of cloud technologies, I worked on various clouds like AWS, PCF and GCP where our micro services were frequently deployed in Dev, STG and Prod environments.
Integrated the Drools and JBPM to application framework which involved dynamic creation of knowledgebase and Knowledge session.
Leveraged cutting edge technology like Kotlin, Android JetPack, Retrofit, Navigation, View Mode Room, Actions.
Created different Terraform modules as part of foundation (MIG, Cloud SQL, GLB, ILB, Instanc Template)
Created POC for Automatic update for the deployment status update using Vert.x Framework (push Notification), HTML5, CSS3, JavaScript, Junit & JAVA.
Golang and Java were used as a backend REST service while AngularJS was used for the user interface.
Developed REST API's using Spring MVC and Spring boot, hosted all micro services on Kubernetes. 
Developed various screens for the front end using Angular8 and used various predefined components from NPM (Node Package Manager). Used Node Cron package to schedule periodically executable jobs. 
Developed responsive web product features using React JS, SASS, on GoLang with REST.
Implemented multiple functionalities using Spring Boot with Thyme leaf and Hibernate ORM. 
Setting up Angular 7 framework for UI development. Developed html views with HTML 5, CSS, jQuery, JSON, and Java Script. 
Setup edge environment using Rails 3.0 Beta, Ruby Version Manager and migrated the code for compliance with rails 3.0
Defined a message store architecture necessary to support drools rule engine input processing, scalability and recovery.
Implemented the Application using Vert.x.
Closely worked with the GCP API team to understand the REST contract between FES and API team and architectural design patterns being followed (E.g. Strangler, Split-Traffic).
Used Go Programming Language (GoLang) and Scala in the development of the application.
As part of agile methodology, performed Continuous Integration and Continuous Deployment of projects using tools Like Bamboo, GIT and AWS CloudFormation. 
Developed the terraform script to get the certificates from the bucket and create an SSL cert in t respective project on the fly without downloading the certs on the local machine.
Expert in writing SQL queries and using Hibernate framework with Spring ORM in interaction with the RDBMS and familiar with Non-Relational Database like Mongo DB. 
Design and developed the REST based Microservices using the Spring Boot, Spring Data with JPA. 
Implemented Hibernate annotations for the classes by replacing hibernate mapping xml files and Hibernate/JPA. 
Developed the web application to serve the JavaScript application using the web socket solution.
Developed backup and recovery engine for VM backup/recovery using VMware vSphere APIs, GoLang programming language and RabbitMQ Message bus (communication interface).
Built RESTful API to save and retrieve agent information in Java using Spring MVC, Mongo DB, NoSQL. 
Created a RESTful Web API service using ASP.NET Web API to generate data for the EMS which was then consumed in the front-end by Angular 7. 
Used RSpec to create test driven development. Experience with all of the GEMS and libraries for this version of the ruby/rails.
Wrote various types of Business rules in the. drl (drools le) and ece (expectation les) using Drools rule engine for business decision making.
As part of GCP implemented a spring boot app using IntelliJ IDE and deployed it to Google APP Engine
Implemented Kafka producer and consumer applications on Kafka cluster setup with help of Zookeeper. 
Used Spring Kafka API calls to process the messages smoothly on Kafka Cluster setup. 
Used the all the features of Spring Core layer (IOC), Spring AOP, Spring ORM layer and Spring DAO support layer for developing the application. 
Design and develop web pages HTML5, CSS, Bootstrap and Client-side scripting using JavaScript, Ajax, jQuery and Angular 7. 
Deployed the application on Apache Tomcat Application Server. 
Resolving spring framework artefacts and code build & deployment with Maven. 
Environment: Java8/J2EE, Web Services - RESTful (Microservices), Servlets, Spring, Spring boot, HTML5, CSS4, JavaScript, jQuery, AJAX, Eclipse, Groovy, NodeJS, Docker, Ruby, Kotlin, Micro-Front End, Terraform, Vert.x, Golang, GCP, Jenkins, Gradle, Log4J 2, JBOSS, Kubernetes, Spring data, Vue.JS,  JUnit, UML, Drools, Design Patterns, Git, JMS, Cucumber, MYSQL, JMeter, PL/SQL Developer, Typescript, Thyme leaf, HATEOAS, RabbitMQ, REST, MONGO DB, Postman, EJB.
Ford, Dearborn, MI								September 2021 to August 2022
Role: Java Backend Developer
Responsibilities 
Involved in analysis and testing phases of Software Development Life Cycle (SDLC) and used agile methodology for developing applications. 
Followed Agile methodology and prepared technical reports & documentation manuals during the program development. 
Having good experience with Ruby on Rails and Python with Django
Provide hands on training to application developers in secure coding techniques and best practices and helping to integrate security into the software development life cycle (DevSecOps).
Developed new RESTful API services that work as a middleware between our application and third-party APIs that we will use using Golang.
Analyze process-based DRL, design JBPM work ow process using BPMN 2.0, implement business rules processing using Drools.
Accessed various data stores from application using Vert.x.
Integrated RESTful APIs with Vue.js to deliver real-time, data-driven applications
Created interactive 2D graphics and animations using HTML5 Canvas for enhanced user engagement.
Skilled in implementing Linux features on windows through VM’s, UI’s and cloud shell in GCP/AWS.
Implementing RoR upgrades, patches and publishing Ruby Gems using Ruby Gems
Implemented Multithreading, Concurrency, Exception Handling and Collections whenever necessary. 
Designed and developed Microservices business components using Spring Boot. 
Skilled in Ruby on Rails implementation in building web applications from scratch.
Used the API Gateway Group which can do the routing, security control in the cloud Microservices. 
Used Spring MVC Design Pattern to hold the UI data and serve the request and to send the response. 
Used Spring as the Framework do the spring core configuration and integration to perform the dependency injection. 
Used Vert.x to provide simple APIs for authentication in application.
Hands on experience on automation framework, Agile, DevOps, DevSecOps, CHEF client/server, integration of CHEF with Jenkins for continuous deployment and creating recipes/cookbooks using Ruby programming.
Experience writing data APIs and multi-server applications to meet product needs using Golang.
Connected to Data sources and performed operations using Spring Data Framework features like Spring JDBC and Spring ORM. 
Used Vert.x to provide Java Connector Architecture (JCA) adaptor to interoperate with Java J2EE application server.
Integrate the Drools and JBPM to the application framework, dynamic creation of knowledgebase and Knowledge session, and JPA persistence using Hibernate to maintain drools state.
Developed presentation layer using HTML5, CSS3, JavaScript, JSON, AJAX, React js, Bootstrap, NodeJS and XML. 
Developed Swagger UI documentation for the REST API's. 
Secured REST services using Spring security OAuth 2 using JWT tokens. 
Deployed, scaled, configured, and wrote manifest file for various microservices in PCF. 
Develop various screens for the front end using React JS and used various predefined components from NPM (Node Package Manager) and Redux libraries. 
Actively involved in developing applications on emerging technologies- DevOps, DevSecOps, Ansible, GitHub, Bitbucket, Spark, on Linux environment.
Developed Microservice to provide Restful API utilizing Spring Boot with various data persistence frameworks such Hibernate, JPA and messaging engines. 
Integration with Spring Security in web application. 
Designed and developed a project that reads messages from Kafka topic and applied conversation from JSON to pipe delimited String data format to be saved in Oracle and NoSQL. 
Made the integration of data in several applications in favor of JSON documents with dynamic schemas using MongoDB (NoSQL) database. 
Implemented Angular 9 custom services to implement dependency injection and used pipes to transformations. 
Created various MongoDB collections and wrote services to store and retrieve user data from for the application on devices and used Mongoose API to access the MongoDB from NodeJS. 
Used REST Client and POSTMAN to test the REST based services and used Junit and Mockito to test the middleware services. 
Configured WebSphere resources like JDBC providers, JDBC data sources, connection pooling. 
Migrated all the dependencies from Java 8 to the latest stable versions Java 11+. 
Used Java 11 features such as Lambdas, Streams, Observables, and Completable Futures. 
Created and configured the continuous delivery pipelines for deploying micro services and lambda functions using CI/CD Jenkins server. 
Developed API for using AWS Lambda to manage the servers and run the code in AWS. 
Worked on AWS services to deploy static websites and dynamic Web Apps on EC2 using Elastic Beanstalk and Elastic Container Service-Docker. 
Wrote some Python scripts to manage AWS cloud formation templates to automate installation of auto scaling, AWS cloud (EC2, S3, Lambda). 
Wrote JUnit test cases for unit, integration, and functional tests, run automatically by Jenkins. 
Used SonarQube to check the code review for the whole project. 
Used Maven as a build and configuration tool and Jenkins for continuous integration tool. 
Used GIT as a version control system, to keep track of the whole work and changes, collaborating with several developers. 
Implemented Test Driven Environment (TDD) Used JUnit and Mockito. 
Used JIRA tool for Issue/bug tracking, monitoring of work assignment in the system. 
Involved in configuration and usage of Apache Log4J for logging and debugging purposes. 
Environment: Java, Agile, Microservices, HTML, CSS, JavaScript, Bootstrap, Spring boot, Spring cloud, DevSecOps, Hibernate, REST, Postman, NoSQL, GCP, Ruby, Vue.JS, MongoDB, Drools, Kafka, AWS, EC2, Chef, Vert.x Golang, Jenkins, Git, SonarQube, Maven, Docker, Kubernetes.
CSC, Hyderabad, Telangana, India							January 2020 to July 2021
Role: Sr. Java Backend Developer
Responsibilities: 
Actively involving in Analysis, Design, Development, System Testing and User Acceptance Testing. 
Developing Rest APIs to support React JS for creating new UI portal-based development. 
Designing and developing the End Points (Controllers), Business Layer, DAO Layer using Hibernate/JDBC 
templates, using Spring IOC (Dependency Injection). 
Azure Resource Manager (ARM) provides security, auditing, and tagging features to help you manage your resources after deployment. 
Implemented Spring MVC architecture and increased modularity by allowing the separation of cross-cutting concerns using Spring AOP. 
Using Express Framework to set up middleware to respond to the HTTP requests with the help of Node JS. 
Designed and developed client's website screens and front-end widgets using React.js. 
Used Apache Kafka for streaming real-time data pipelines and streaming of application data to achieve asynchronous messaging. 
Built custom drawing tools and data visualizations with Canvas API, including real-time rendering and user interaction features
Used Spring Core for dependency injection/Inversion of control (IOC). 
Deployed Hazelcast as an in-memory data grid to improve application responsiveness and scalability
Wrote Kafka producers to stream the data from external Rest APIs to Kafka topics. 
Implemented ReactJS code to handle cross browser compatibility issues. 
Optimized Canvas performance through efficient rendering techniques and memory management for smoother animations.
Designed responsive and scalable Canvas elements compatible with various screen sizes and devices.
Designed, built and deployed application using the AWS stack (Including EC2, Mongo DB, Docker, 
Extensively worked on writing complex PL/SQL Queries using joins, stored procedures, Functions, 
Test automation for web application using Cucumber. 
Experience in Developing User Interface (UI) Rich Web Applications and Web Service Applications using HTML 4, XHTML, CSS 2, XML, AJAX, Object Oriented Java Script, ANGULARJS, REACTJS, BOOTSTRAP Framework, RESTful services, JAVA, JSP. 
Environment: Java8, JEE, JMS, Hibernate, Spring, Web services, REST, UML, HTML, Bootstrap, JavaScript, jQuery, Maven, Make, Node JS, PL/SQL, Bootstrap, Spring Security, React JS, Azure, Microservices, Kafka, Rational Application Developer (RAD), Hibernate, XML, XSD, Log4j, Oracle12c, PL/SQL, JUnit, AJAX, Jenkins, CSS, JSP, JNDI, JIRA, Cucumber.
All State Insurance, Bengaluru, Karnataka, India         				January 2018 to December 2019
Role: Java Developer
Responsibilities: 
Worked in Agile work environment for workflow management and content versioning. 
Used Spring framework MVC in the development of new modules based on requirements. 
Utilized various utilities like JSP, JSTL, JavaScript, HTML, & CSS, jQuery. 
A robust understanding of front-end technologies, Object-Oriented JavaScript, AJAX, XHTML, HTML, XML/DOM, Node.js, Angular 6, ReactJS, JS-Based framework. 
Refactored existing project to make it more RESTful and thread safe. 
Implemented the Microservices based on RESTful API utilizing Spring Boot with Spring MVC. 
Utilized Angular 6 framework to bind HTML template (views) to JavaScript object (models). 
Apache Mesos and Kubernetes were used as the best ecosystem to deploy and manage Micro-services. 
Developed the Spring Features like Spring MVC, Spring Boot, Spring Batch, Spring Security, and Spring Integration. 
Implemented Hazelcast for distributed computing and data storage in a clustered environment.
Developed views using Bootstrap components, Angular-UI and throughout the project involved in configuring routing for various modules using angular UI router. 
Used AJAX to make asynchronous calls to the server to fetch data and update the DOM. 
Experienced in DOM manipulation, Event Handling, Event Bubbling, and integration with RESTful services. 
Experience in cross browser compatibility check and thoroughly performed unit testing and integration testing. 
Created Custom Directives in Angular 6, NodeJS making use of Modules and Filters objects according to the requirement. 
Deployed Spring Boot based Microservices Docker containers Using AWS EC2 container services and using AWS admin console. Used EJB3 and DTO and DAO design pattern to persist data in DB2 9.1 database. 
Unit Tested PL/SQL Modules. Prepared Unit Test Cases for the designed modules. 
Implemented the logging mechanism using log4j framework. 
Environment: Core Java, J2EE, HTML, CSS, JSP, JDBC, JSP, Spring Boot, Angular 6, JAXB, JavaScript, jQuery, XML, JSON, Servlets, Spring Framework, PL/SQL, JAXB, RESTful, Log4j, Eclipse.
QUALCOMM, Hyderabad, Telangana, India						May 2016 to December 2017
Role: Java Developer
Responsibilities: 
Involved in gathering and analyzing system requirements. 
Front-end is designed by using HTML, CSS, JSP, Servlets, JSTL, Ajax and Struts. Used JavaScript for web page validation. 
Developed the Training and Appraisal modules using Java, JSP, Servlets and JavaScript. 
Developed the UI using Java Servlets, Java script, HTML, CSS3, Ajax. 
Developed the Action classes for handling the requests from the JSP pages. 
Apache Tomcat Serve is used for deploying the projects. 
Helped in designing and creating the database structure and its tables to minimize the storage of data. 
Developed Java Bean components to communicate with Data Base. Used Junit for unit testing. 
Developed the Ant scripts for preparing WAR files, EAR, EJB used to deploy J2EE components. deployed the application on Jetty. 
Use WebLogic fuse for remote console login, JMX management and web server console. 
Responsible for coding SQL Statements and Stored procedures for back-end communication using JDBC. 
Used PL/SQL to code packages, store procedures, functions, and loaders. 
Created Unit, Acceptance and Integration Test Documents and Code Review Documents. 
Environment: Java, J2EE, JSP, ANT, Servlets, WebLogic, SV, JDBC, HTML, CSS3, JavaScript, Junit, Ajax, RSA, Junit, MVC, Apache Tomcat and MY SQL, EJB.
Languages | Java 1.8, J2EE, Ruby
Web Technologies | SERVLETS 3.1, JSP 3.0, JavaBeans 3.0, Java Servlet API 3.0
Frameworks | Spring 4.3/5, Hibernate 4.3, Angular 6/7/8/9/10, Drools, Vert.x, Spring Security 4.0
Application/Web servers | Apache Tomcat 8/9, IBM WebSphere 8.0/9.0, Jetty, Jenkins 2.50, WebSphere MQ 7.5
Relational Databases | Oracle 12c, 10g/11g, SQL server, MySQL 5.7, DB2 11.1
NoSQL Databases | MongoDB, Cassandra, CouchDB
Internet Technologies | HTML 5, JavaScript 1.8, XML 2.0, CSS 3 and CSS 4, jQuery 2.11, Angular, BackBone.JS 1.3.1, Polymer.JS, Node JS 6.0.
Cloud Environments | Azure, AWS, Netflix Eureka, GCP, Mesos, Kubernetes
IDE | Eclipse, NetBeans 8.0.2, IntelliJ 2017.1, Spring Tool Suite (STS) 3.8.3, WebStorm
Operating system | Windows 10, Linux Mint 18.1, Unix
Bug tracking Tools | JUNIT4.12, JIRA 7.0, Bugzilla 4.4.12, Curl 7.36
Reporting Tools | Jasper Reports 6.0, Crystal Reports XI, SSRS 5.0
Methodologies | Agile, waterfall, TDD (Test-Driven-Development), Scrum
Developer IDE Platforms | Eclipse, Edit plus, Notepad ++, TEXT PAD, JBuilder, Net Beans 8.0.2, IntelliJ 2017.1, Spring Tool Suite (STS) 3.8.3, GITHUB 2.12.0, MS Office
Build Tools | Ant 1.10, Maven 3.3.9, Gradle 3.4.1
Web Services | SOAP 1.2, REST 2.0, JAX-WS, JAX-RPC, JAX-RS