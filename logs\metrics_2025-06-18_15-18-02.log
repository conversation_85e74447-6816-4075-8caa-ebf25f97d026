{"event": "session_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "timestamp": "2025-06-18T15:18:02.839924", "message": "New API session started"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5c116fb1-c379-43a8-91e0-501293964211", "endpoint": "/", "timestamp": "2025-06-18T15:18:29.282980", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5c116fb1-c379-43a8-91e0-501293964211", "endpoint": "/", "timestamp": "2025-06-18T15:18:29.283981", "total_time_seconds": 0.0010006427764892578, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "63a2d50c-5a6c-4483-9798-74f9a88b2e37", "endpoint": "/", "timestamp": "2025-06-18T15:18:40.058950", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "63a2d50c-5a6c-4483-9798-74f9a88b2e37", "endpoint": "/", "timestamp": "2025-06-18T15:18:40.060454", "total_time_seconds": 0.0015044212341308594, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.103056", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.119056", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.119056", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.119056", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.119056", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:18:42.119056", "file_processing_time": 0.01300048828125, "message": "Custom metric: file_processing_time=0.01300048828125"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "d4ed97f1-0e2f-4c50-8fe1-e5fef6cab9d7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:14.397533", "total_time_seconds": 32.294477462768555, "status_code": 200, "message": "Request completed in 32.2945s with status 200"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.474627", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.488630", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.489633", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.489633", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.489633", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:18.489633", "file_processing_time": 0.011003494262695312, "message": "Custom metric: file_processing_time=0.011003494262695312"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "4ea0f817-f2fd-407b-8a99-e8019989c3d9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:47.925544", "total_time_seconds": 29.450916528701782, "status_code": 200, "message": "Request completed in 29.4509s with status 200"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:51.986534", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:52.002735", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:52.002735", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:52.002735", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:52.002735", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:19:52.002735", "file_processing_time": 0.014203071594238281, "message": "Custom metric: file_processing_time=0.014203071594238281"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "706fbfee-fa86-477f-bc19-7570911f184c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:23.620483", "total_time_seconds": 31.633948802947998, "status_code": 200, "message": "Request completed in 31.6339s with status 200"}
{"event": "request_start", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.682828", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.715228", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.715228", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.715228", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.715228", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:27.715228", "file_processing_time": 0.011999368667602539, "message": "Custom metric: file_processing_time=0.011999368667602539"}
{"event": "request_complete", "session_id": "a7285b17-94a8-4c18-827d-0c78fbe28fd2", "request_id": "5400cbee-6391-4a18-9ee7-c4f0e35cb98a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:20:55.836607", "total_time_seconds": 28.153779983520508, "status_code": 200, "message": "Request completed in 28.1538s with status 200"}
