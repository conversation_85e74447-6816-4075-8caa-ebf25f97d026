{"event": "session_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "timestamp": "2025-06-18T15:34:30.421482", "message": "New API session started"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2b064897-dc9e-4116-a2b7-0991f03ae9aa", "endpoint": "/", "timestamp": "2025-06-18T15:34:57.865093", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2b064897-dc9e-4116-a2b7-0991f03ae9aa", "endpoint": "/", "timestamp": "2025-06-18T15:34:57.866093", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ea861b05-f673-4b6d-8ae7-031a904242b7", "endpoint": "/", "timestamp": "2025-06-18T15:35:07.347931", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ea861b05-f673-4b6d-8ae7-031a904242b7", "endpoint": "/", "timestamp": "2025-06-18T15:35:07.349930", "total_time_seconds": 0.0019991397857666016, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.374507", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.392902", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.392902", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.392902", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.392902", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:09.392902", "file_processing_time": 0.016223907470703125, "message": "Custom metric: file_processing_time=0.016223907470703125"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b4353057-a1c8-4287-908a-7c65026f1f87", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:23.769644", "total_time_seconds": 14.39513635635376, "status_code": 200, "message": "Request completed in 14.3951s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "860af4f4-a974-43f6-b41a-9524b60b2940", "endpoint": "/", "timestamp": "2025-06-18T15:35:40.576997", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "860af4f4-a974-43f6-b41a-9524b60b2940", "endpoint": "/", "timestamp": "2025-06-18T15:35:40.578001", "total_time_seconds": 0.0010039806365966797, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.664033", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.677542", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.677542", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.677542", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.677542", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:42.677542", "file_processing_time": 0.01051020622253418, "message": "Custom metric: file_processing_time=0.01051020622253418"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "fda0311a-2cb8-4643-aae8-1a1706101dbb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:35:56.924417", "total_time_seconds": 14.260384559631348, "status_code": 200, "message": "Request completed in 14.2604s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:00.981021", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:01.013395", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:01.013395", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:01.014395", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:01.014395", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:01.014395", "file_processing_time": 0.012097835540771484, "message": "Custom metric: file_processing_time=0.012097835540771484"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ef5e30fe-9c3c-4303-a49b-f59aef94523f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:29.523943", "total_time_seconds": 28.542922496795654, "status_code": 200, "message": "Request completed in 28.5429s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.562111", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.578110", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.578110", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.578110", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.578110", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:36:33.578110", "file_processing_time": 0.013997793197631836, "message": "Custom metric: file_processing_time=0.013997793197631836"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3ecc5eaf-3ace-477b-85c2-5d79ebbdf0df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:02.764655", "total_time_seconds": 29.20254373550415, "status_code": 200, "message": "Request completed in 29.2025s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.824079", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.837579", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.837579", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.838582", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.838582", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:06.838582", "file_processing_time": 0.011501312255859375, "message": "Custom metric: file_processing_time=0.011501312255859375"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b302f415-da61-480e-bfd9-afc199234df5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:37:34.566275", "total_time_seconds": 27.742196083068848, "status_code": 200, "message": "Request completed in 27.7422s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.946074", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.961581", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.961581", "file_size_bytes": 81582, "message": "Custom metric: file_size_bytes=81582"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.961581", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.961581", "extracted_text_length": 3080, "message": "Custom metric: extracted_text_length=3080"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:08.961581", "file_processing_time": 0.013506650924682617, "message": "Custom metric: file_processing_time=0.013506650924682617"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "3a67cb1f-c1ce-46f2-9c8b-51acc252d562", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:24.340964", "total_time_seconds": 15.394889831542969, "status_code": 200, "message": "Request completed in 15.3949s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.405836", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.423408", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.423408", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.423408", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.423408", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:26.423408", "file_processing_time": 0.014571189880371094, "message": "Custom metric: file_processing_time=0.014571189880371094"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "4df1b6f1-0f93-42b8-ad99-1bad26fd7e9d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:52.863020", "total_time_seconds": 26.457184553146362, "status_code": 200, "message": "Request completed in 26.4572s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.920222", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.936226", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.936226", "file_size_bytes": 71498, "message": "Custom metric: file_size_bytes=71498"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.936226", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.936226", "extracted_text_length": 2507, "message": "Custom metric: extracted_text_length=2507"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:40:54.936226", "file_processing_time": 0.013006448745727539, "message": "Custom metric: file_processing_time=0.013006448745727539"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "05993b6d-09c6-4828-9c0d-df1843efb6ab", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:09.526727", "total_time_seconds": 14.606505155563354, "status_code": 200, "message": "Request completed in 14.6065s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.573024", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.617024", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.618022", "file_size_bytes": 72971, "message": "Custom metric: file_size_bytes=72971"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.618022", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.618022", "extracted_text_length": 3176, "message": "Custom metric: extracted_text_length=3176"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:11.618022", "file_processing_time": 0.04200148582458496, "message": "Custom metric: file_processing_time=0.04200148582458496"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d37e3610-cc89-4ce0-a044-cabb3aec52e2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:39.099490", "total_time_seconds": 27.526466131210327, "status_code": 200, "message": "Request completed in 27.5265s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.167642", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.183650", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.184649", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.184649", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.184649", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:41:41.185155", "file_processing_time": 0.014008283615112305, "message": "Custom metric: file_processing_time=0.014008283615112305"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "bbb75de3-f0c8-4734-8ad0-f6ab6c186feb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:08.074285", "total_time_seconds": 26.906643390655518, "status_code": 200, "message": "Request completed in 26.9066s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.135003", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.149002", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.149002", "file_size_bytes": 72943, "message": "Custom metric: file_size_bytes=72943"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.150007", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.150007", "extracted_text_length": 3091, "message": "Custom metric: extracted_text_length=3091"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:10.150007", "file_processing_time": 0.011999845504760742, "message": "Custom metric: file_processing_time=0.011999845504760742"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2709bd71-fbfd-46e5-8ac2-930fdd4db9da", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:38.660417", "total_time_seconds": 28.52541470527649, "status_code": 200, "message": "Request completed in 28.5254s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.740756", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.759550", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.760554", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.760554", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.760554", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:42:40.760554", "file_processing_time": 0.015590190887451172, "message": "Custom metric: file_processing_time=0.015590190887451172"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a6a2da09-acee-4e0c-9cd4-8ac73ef6b92c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:09.805999", "total_time_seconds": 29.065242767333984, "status_code": 200, "message": "Request completed in 29.0652s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.847690", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.878461", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.878461", "file_size_bytes": 84438, "message": "Custom metric: file_size_bytes=84438"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.879456", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.879456", "extracted_text_length": 2855, "message": "Custom metric: extracted_text_length=2855"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:11.879456", "file_processing_time": 0.02076411247253418, "message": "Custom metric: file_processing_time=0.02076411247253418"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "09a2dee5-e967-45c0-8915-cffe1f4c0e2c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:27.377940", "total_time_seconds": 15.53024935722351, "status_code": 200, "message": "Request completed in 15.5302s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.425120", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.449118", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.449118", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.449118", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.449118", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:29.450119", "file_processing_time": 0.01999950408935547, "message": "Custom metric: file_processing_time=0.01999950408935547"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2d407081-5f00-4535-8330-9deae7247ff4", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:43.036742", "total_time_seconds": 13.611622333526611, "status_code": 200, "message": "Request completed in 13.6116s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.076601", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.098115", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.098115", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.098115", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.098115", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:43:45.098115", "file_processing_time": 0.019516706466674805, "message": "Custom metric: file_processing_time=0.019516706466674805"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "36ce3868-8aa5-4699-a353-730944d0bc7b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:44:11.285532", "total_time_seconds": 26.20893144607544, "status_code": 200, "message": "Request completed in 26.2089s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.547502", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.563500", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.564501", "file_size_bytes": 72401, "message": "Custom metric: file_size_bytes=72401"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.564501", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.564501", "extracted_text_length": 3544, "message": "Custom metric: extracted_text_length=3544"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:06.564501", "file_processing_time": 0.014000415802001953, "message": "Custom metric: file_processing_time=0.014000415802001953"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "ad47fbce-3205-4170-b2a8-5866312452ec", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:35.320275", "total_time_seconds": 28.772773265838623, "status_code": 200, "message": "Request completed in 28.7728s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.394747", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.414750", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.414750", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.414750", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.414750", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:45:37.414750", "file_processing_time": 0.016002416610717773, "message": "Custom metric: file_processing_time=0.016002416610717773"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "6d15eaf3-a6c9-47b5-ab6b-aab55d25225b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:03.250739", "total_time_seconds": 25.85599184036255, "status_code": 200, "message": "Request completed in 25.8560s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.289923", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.308255", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.308255", "file_size_bytes": 64332, "message": "Custom metric: file_size_bytes=64332"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.308255", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.308255", "extracted_text_length": 3518, "message": "Custom metric: extracted_text_length=3518"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:05.308255", "file_processing_time": 0.015999317169189453, "message": "Custom metric: file_processing_time=0.015999317169189453"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "68ad4e84-f9cb-4ac9-ba3f-b082e65afcac", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:36.343372", "total_time_seconds": 31.053448915481567, "status_code": 200, "message": "Request completed in 31.0534s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.384027", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.406534", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.406534", "file_size_bytes": 71509, "message": "Custom metric: file_size_bytes=71509"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.406534", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.407534", "extracted_text_length": 3591, "message": "Custom metric: extracted_text_length=3591"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:46:38.407534", "file_processing_time": 0.019507884979248047, "message": "Custom metric: file_processing_time=0.019507884979248047"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "26075651-5316-4f4d-8979-d0d255a8f27b", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:06.495621", "total_time_seconds": 28.11159372329712, "status_code": 200, "message": "Request completed in 28.1116s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.585717", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.605726", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.605726", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.605726", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.605726", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:28.605726", "file_processing_time": 0.018006563186645508, "message": "Custom metric: file_processing_time=0.018006563186645508"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "b2f45a1a-4783-4aef-859c-ced3dd45e47c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:55.205844", "total_time_seconds": 26.620126485824585, "status_code": 200, "message": "Request completed in 26.6201s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.256932", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.288334", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.288334", "file_size_bytes": 96268, "message": "Custom metric: file_size_bytes=96268"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.288334", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.288334", "extracted_text_length": 4893, "message": "Custom metric: extracted_text_length=4893"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:47:57.289318", "file_processing_time": 0.02840566635131836, "message": "Custom metric: file_processing_time=0.02840566635131836"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c7b45d4-d6eb-4e1e-a13d-7f254fe03408", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:28.351270", "total_time_seconds": 31.094338417053223, "status_code": 200, "message": "Request completed in 31.0943s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.426762", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.444784", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.444784", "file_size_bytes": 70963, "message": "Custom metric: file_size_bytes=70963"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.444784", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.444784", "extracted_text_length": 1872, "message": "Custom metric: extracted_text_length=1872"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:30.444784", "file_processing_time": 0.015023231506347656, "message": "Custom metric: file_processing_time=0.015023231506347656"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "a921f73f-5061-45b2-b2d9-ebbb9f1555fc", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:53.188739", "total_time_seconds": 22.761977434158325, "status_code": 200, "message": "Request completed in 22.7620s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.258583", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.287305", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.287305", "file_size_bytes": 76118, "message": "Custom metric: file_size_bytes=76118"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.287305", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.287305", "extracted_text_length": 3345, "message": "Custom metric: extracted_text_length=3345"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:48:55.287305", "file_processing_time": 0.022675275802612305, "message": "Custom metric: file_processing_time=0.022675275802612305"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "82bf6c2a-71b7-43eb-bdd7-c9b5dbad1265", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:24.302921", "total_time_seconds": 29.0443377494812, "status_code": 200, "message": "Request completed in 29.0443s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.382294", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.403013", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.403013", "file_size_bytes": 81408, "message": "Custom metric: file_size_bytes=81408"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.403013", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.403013", "extracted_text_length": 2452, "message": "Custom metric: extracted_text_length=2452"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:26.404012", "file_processing_time": 0.0176239013671875, "message": "Custom metric: file_processing_time=0.0176239013671875"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "c5e1c3f9-28eb-43c4-8bd2-b7ad1250bdf5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:53.379510", "total_time_seconds": 26.99721598625183, "status_code": 200, "message": "Request completed in 26.9972s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.453904", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.473561", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.473561", "file_size_bytes": 70825, "message": "Custom metric: file_size_bytes=70825"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.473561", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.473561", "extracted_text_length": 2308, "message": "Custom metric: extracted_text_length=2308"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:49:55.473561", "file_processing_time": 0.016651153564453125, "message": "Custom metric: file_processing_time=0.016651153564453125"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "d3efc583-2ad7-4fad-a9c7-bb055c200968", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:21.504887", "total_time_seconds": 26.05098295211792, "status_code": 200, "message": "Request completed in 26.0510s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.586560", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.605588", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.605588", "file_size_bytes": 71487, "message": "Custom metric: file_size_bytes=71487"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.606592", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.606592", "extracted_text_length": 2104, "message": "Custom metric: extracted_text_length=2104"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:23.606592", "file_processing_time": 0.016026973724365234, "message": "Custom metric: file_processing_time=0.016026973724365234"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "88a50f0f-d7ef-4720-9a61-e8379560d6bf", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:35.339615", "total_time_seconds": 11.753054857254028, "status_code": 200, "message": "Request completed in 11.7531s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.394297", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.416047", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.417048", "file_size_bytes": 63647, "message": "Custom metric: file_size_bytes=63647"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.417048", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.417048", "extracted_text_length": 3421, "message": "Custom metric: extracted_text_length=3421"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:37.417048", "file_processing_time": 0.019748449325561523, "message": "Custom metric: file_processing_time=0.019748449325561523"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "35421344-fb08-485b-a7df-324561b25cb5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:52.747470", "total_time_seconds": 15.353172779083252, "status_code": 200, "message": "Request completed in 15.3532s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.819202", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.837150", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.838151", "file_size_bytes": 71397, "message": "Custom metric: file_size_bytes=71397"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.838151", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.838151", "extracted_text_length": 2462, "message": "Custom metric: extracted_text_length=2462"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:50:54.838151", "file_processing_time": 0.015510082244873047, "message": "Custom metric: file_processing_time=0.015510082244873047"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "1c1cd3a5-02ce-438f-86f5-21145709e3f2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:18.724296", "total_time_seconds": 23.90509343147278, "status_code": 200, "message": "Request completed in 23.9051s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.806917", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.833971", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.833971", "file_size_bytes": 72604, "message": "Custom metric: file_size_bytes=72604"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.833971", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.833971", "extracted_text_length": 4038, "message": "Custom metric: extracted_text_length=4038"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:20.833971", "file_processing_time": 0.024054527282714844, "message": "Custom metric: file_processing_time=0.024054527282714844"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f1cd4b25-8efc-497c-a6a7-ecc63bb132e7", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:49.418537", "total_time_seconds": 28.61161971092224, "status_code": 200, "message": "Request completed in 28.6116s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.492424", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.509437", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.509437", "file_size_bytes": 71689, "message": "Custom metric: file_size_bytes=71689"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.509437", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.509437", "extracted_text_length": 1680, "message": "Custom metric: extracted_text_length=1680"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:51:51.509437", "file_processing_time": 0.01401662826538086, "message": "Custom metric: file_processing_time=0.01401662826538086"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "dd3a9c4c-bb72-49a0-baac-f6ef9a6ed635", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:11.710067", "total_time_seconds": 20.21764302253723, "status_code": 200, "message": "Request completed in 20.2176s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.767727", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.787755", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.787755", "file_size_bytes": 78742, "message": "Custom metric: file_size_bytes=78742"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.787755", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.789261", "extracted_text_length": 2223, "message": "Custom metric: extracted_text_length=2223"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:13.789261", "file_processing_time": 0.016514062881469727, "message": "Custom metric: file_processing_time=0.016514062881469727"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "e5fff16d-54a0-42d7-855c-1ff42d3816df", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:26.915561", "total_time_seconds": 13.147833108901978, "status_code": 200, "message": "Request completed in 13.1478s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:28.992923", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:29.026427", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:29.026427", "file_size_bytes": 69371, "message": "Custom metric: file_size_bytes=69371"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:29.026427", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:29.026427", "extracted_text_length": 1231, "message": "Custom metric: extracted_text_length=1231"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:29.026427", "file_processing_time": 0.01251077651977539, "message": "Custom metric: file_processing_time=0.01251077651977539"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "2acb1271-a7d3-4e76-86ac-731f481238e3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:47.238340", "total_time_seconds": 18.24541664123535, "status_code": 200, "message": "Request completed in 18.2454s with status 200"}
{"event": "request_start", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.320450", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.336961", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.336961", "file_size_bytes": 72117, "message": "Custom metric: file_size_bytes=72117"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.336961", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.336961", "extracted_text_length": 2144, "message": "Custom metric: extracted_text_length=2144"}
{"event": "custom_metric", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:52:49.338467", "file_processing_time": 0.01351308822631836, "message": "Custom metric: file_processing_time=0.01351308822631836"}
{"event": "request_complete", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "request_id": "f6c37c2a-d200-470a-940c-66dae88a56a9", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:53:00.922649", "total_time_seconds": 11.60219955444336, "status_code": 200, "message": "Request completed in 11.6022s with status 200"}
{"event": "session_end", "session_id": "b4aba668-ca4e-4139-8b6e-8549bfbf8443", "timestamp": "2025-06-18T16:04:01.026533", "message": "API session ended"}
