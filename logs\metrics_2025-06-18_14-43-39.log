{"event": "session_start", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "timestamp": "2025-06-18T14:43:39.267464", "message": "New API session started"}
{"event": "request_start", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.150001", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.165001", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.183309", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.183309", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.183309", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:43:50.183309", "file_processing_time": 0.012001276016235352, "message": "Custom metric: file_processing_time=0.012001276016235352"}
{"event": "request_complete", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "3b303fd4-c9e1-4a18-8b31-6710d7291c04", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:44:15.487149", "total_time_seconds": 25.3371479511261, "status_code": 200, "message": "Request completed in 25.3371s with status 200"}
{"event": "request_start", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "6b3f6aea-7898-4f92-8362-c9293eeab409", "endpoint": "/", "timestamp": "2025-06-18T14:48:32.180792", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "request_id": "6b3f6aea-7898-4f92-8362-c9293eeab409", "endpoint": "/", "timestamp": "2025-06-18T14:48:32.181790", "total_time_seconds": 0.0009982585906982422, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "session_end", "session_id": "40c35042-9958-4614-85a5-34592dcab31d", "timestamp": "2025-06-18T14:48:32.363207", "message": "API session ended"}
