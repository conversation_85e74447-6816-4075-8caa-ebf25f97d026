{"event": "session_start", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "timestamp": "2025-06-18T14:37:24.500630", "message": "New API session started"}
{"event": "request_start", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "820a95dc-0d87-48aa-8326-22913ad8e7b1", "endpoint": "/", "timestamp": "2025-06-18T14:37:33.861783", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "820a95dc-0d87-48aa-8326-22913ad8e7b1", "endpoint": "/", "timestamp": "2025-06-18T14:37:33.862782", "total_time_seconds": 0.00099945068359375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.892710", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.908468", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.908468", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.908468", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.908468", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:37:35.908468", "file_processing_time": 0.013365983963012695, "message": "Custom metric: file_processing_time=0.013365983963012695"}
{"event": "request_complete", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "3106f8ad-999e-4a88-95a4-070588decb2a", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:38:04.495851", "total_time_seconds": 28.60314106941223, "status_code": 200, "message": "Request completed in 28.6031s with status 200"}
{"event": "request_start", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "b45a8ce9-9395-4628-a852-1f85aa11fa7a", "endpoint": "/", "timestamp": "2025-06-18T14:40:43.060526", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "request_id": "b45a8ce9-9395-4628-a852-1f85aa11fa7a", "endpoint": "/", "timestamp": "2025-06-18T14:40:43.061525", "total_time_seconds": 0.0009992122650146484, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "session_end", "session_id": "8c1f9de9-5de0-4a56-8acc-d631b458ebc2", "timestamp": "2025-06-18T14:40:43.196882", "message": "API session ended"}
