# Extracted Text Debug File
# Source File: Aashish Resume..docx
# Context: resume
# Extraction Method: docx_text
# Timestamp: 2025-06-09 14:21:17
# Text Length: 15869 characters
# ================================================

Name: Aashish Mishra
Email: <EMAIL>
Phone: ************ ||  
Professional Summary:
Around 7+ years of experience as a Senior Software Developer with expertise in developing and maintaining complex applications in healthcare, insurance, and banking domains.
Designed and implemented microservices architecture using Spring Boot and Docker to enhance scalability, maintainability, and performance across diverse platforms.
Experienced in Developing Java/J2EE Applications using JDK 1.7/1.8, Core Java, JSP, JSF, Spring, Web Services, Struts, XML, and XSL.
Developed RESTful APIs and SOAP web services for seamless integration between core systems, ensuring efficient data exchange in healthcare, insurance, and banking applications.
Utilized Kafka for real-time data streaming and event-driven processing, significantly improving data handling and responsiveness in healthcare and insurance systems.
Implemented OAuth2 and JWT with Spring Security to ensure robust security protocols for authentication and authorization in sensitive financial and healthcare applications.
Developed responsive and dynamic user interfaces using Angular 6/12, ReactJS 17, HTML5, CSS3, and Bootstrap 4/5, enhancing user experience across banking and insurance platforms.
Managed databases using Oracle 11g/19c and MongoDB 4.x, optimizing queries and ensuring efficient data storage and retrieval for large-scale applications.
Deployed and managed applications on cloud platforms like AWS utilizing Kubernetes for efficient container orchestration and scalability.
Led Agile development teams, effectively coordinating sprints, stand-ups, and retrospectives to deliver high-quality software within tight deadlines.
Monitored system performance using JMX with Java Management Extensions (JMX) 1.4, allowing real-time application management.
Enhanced system performance and caching with Redis and Cassandra, significantly reducing latency in data-intensive healthcare and financial applications.
Conducted rigorous unit and integration testing using JUnit 5 and Selenium, ensuring high code quality and reducing defects in production environments.
Deployed and managed applications on WebLogic and WebSphere Application Servers, ensuring high availability and robust performance in banking and financial systems.
Managed CI/CD pipelines using Jenkins 2.303 and Docker 20.10, automating deployment processes and maintaining consistency across development, testing, and production environments.
Collaborated with cross-functional teams, including UX/UI designers and DevOps, to deliver user-friendly and secure applications that meet industry standards and client needs.
Experienced in the Development, testing, and deployment of enterprise applications on Windows & UNIX platforms using IDEs such as Eclipse, NetBeans, and IntelliJ 14.x/15.x, Web Storm, Visual Studio Code.
Experience:

Client: First Republic Bank								         Dec 2022- Present
Senior Software Developer
Responsibilities:
Built and maintained backend services using Java 8 and Spring Boot 2.x, ensuring scalable, secure banking applications.
Skilled in Java 21 features, including Record Patterns, Virtual Threads, and Sequenced Collections.
Implemented client-side logic in ReactJS, manipulating DOM nodes and enhancing UI performance across application modules.
Experienced in Retail, Banking, and Payment domains, working on transaction processing and financial application systems.
Managed issue tracking and workflow automation using GitHub Issues and Project Boards for efficient task management.
Developed RESTful APIs with Java 11, Spring Boot 2.4, and Spring MVC 5.2, improving response times by 35%.
Utilized Helm charts to simplify Kubernetes deployments, enhancing container orchestration and version control.
.
Skilled in writing optimized JDBC queries for efficient database interactions.
Developed dynamic web pages using JSP and Servlets.
Built user-friendly web applications using JSF and PrimeFaces components.
Experienced in refactoring monolithic architectures for improved scalability and maintainability.
Hands-on experience in managing repositories using Bitbucket for version control and collaboration.
Proficient in using JUnit, Mockito, and TestNG for automated testing.
Leveraged Java 8 Lambda expressions to streamline functional programming and reduce redundant code in backend applications.
Built reusable React.js components and optimized templating for improved application scalability and performance.
Designed and optimized PostgreSQL database schemas, queries, and indexing strategies to improve application efficiency.
Worked closely with backend developers to fetch and display dynamic JSON data through services.
Enhanced JSP-based applications for improved server-side rendering.
Customized JSF components to enhance application responsiveness.
Developed complex SQL queries for relational databases, improving performance and data retrieval accuracy.
Integrated PayPal, Stripe, and Authorize.net for secure online transactions, ensuring seamless payment processing.
Integrated Java applications with relational databases like MySQL and PostgreSQL using JDBC.
Implemented HTML5, CSS3, and JavaScript to build dynamic, responsive front-end user interfaces with tableless layouts.
Utilized third-party libraries such as ngx-bootstrap, ng-select, and PrimeNG to expand UI capabilities.
Integrated Bitbucket with CI/CD pipelines for automated deployment processes.
Built scalable and high-performance applications utilizing Java 21 enhancements.
Designed RESTful web services, exposing APIs in JSON format for seamless integration with Django-based admin panels.
Secured enterprise applications by implementing OAuth 2.0 authentication, ensuring compliance with industry security standards.
Configured and managed Artifactory repositories to optimize artifact storage, retrieval, and dependency resolution in Java projects.
Deployed and managed Java applications using Tomcat 9.0+, JBoss, and Kubernetes for scalable microservices architecture.
Implemented GitHub Actions to automate build, test, and deployment processes, improving CI/CD efficiency.
Designed and deployed containerized microservices using Docker, integrated with Jenkins for automated deployments.
Engaged in daily stand-ups and sprint planning to align development with backend Java deliverables.
Experienced in executing software projects using Agile and Waterfall methodologies.
Extensive experience in writing unit tests before implementation to develop robust applications.
Integrated Apache Kafka with Spring Boot microservices to enable real-time, event-driven communication between distributed systems.
Developed and maintained CI/CD pipelines using Jenkins, automating deployment workflows for Java applications.
Used Bitbucket Pipelines to automate testing and deployment tasks.
Integrated Java 21 with microservices architectures to improve application responsiveness.
Utilized JWT-based authentication to secure REST APIs and enhance access control mechanisms.
Managed MongoDB 4.4 for flexible, document-based storage, optimizing unstructured data handling for scalable applications.
Leveraged PostgreSQL features like PostGIS and JSONB for advanced geospatial and structured data processing.
Automated application deployments using Helm templates, Kubernetes, and Ansible for efficient cloud-native infrastructure management.
Implemented Kafka Producer API to publish event streams, ensuring high-throughput, distributed messaging for microservices.
Client: HMS, Irving, Tx                                                                                    		 March 2019 – July 2021
Software Developer
Responsibilities:
Developed responsive web interfaces using HTML5, CSS3, Bootstrap 4, ensuring cross-browser compatibility and enhanced user experience.
Implemented Dependency Injection (DI) and managed the Spring Beans lifecycle, optimizing resource handling and application performance.
Integrated Jenkins with Git, automating CI/CD pipelines to trigger builds on every push for seamless deployments.
Designed responsive user interfaces with React and connected them to RESTful APIs for dynamic data rendering.
Developed and maintained large-scale monolithic applications using Java and Spring.
Utilized GitHub for version control, maintaining source code integrity and enabling collaborative software development.
Configured Artifactory for managing Java libraries, enhancing dependency management across distributed development teams.
Deployed Java applications using Artifactory, ensuring rollback capabilities and streamlined release pipeline management.
Built reusable React components to optimize performance and minimize repetitive code across the application.
Integrated JSF with backend services to create interactive UI elements.
Developed applications with a strict TDD approach, minimizing bugs and enhancing maintainability.
Worked with MySQL, PostgreSQL, and DynamoDB, optimizing queries for low-latency data retrieval and transaction processing.
Connected JSP with backend databases for seamless data interactions.
Implemented JIRA workflows, aligning with Agile methodologies for improved task tracking and issue resolution.
Implemented connection pooling to enhance performance in database-heavy applications.
Enhanced concurrency in Java applications using Java 8+ Parallel Streams and Lambda expressions.
Built dynamic and reusable React.js components with Redux, improving frontend maintainability and state management.
Used React Router to implement dynamic routing and enhance user experience in single-page applications.
Worked closely with cross-functional teams to ensure smooth SDLC execution.
Designed and managed Jenkins pipelines, automating builds, Docker image pushes, and Kubernetes deployments.
Managed global state efficiently in large applications using React and Redux for consistent data flow.
Deployed and scaled microservices using Kubernetes, Docker, Helm, and AWS CloudFormation for high availability.
Created custom JSP tags to simplify complex UI elements.
Integrated Spring Kafka for asynchronous messaging, ensuring efficient communication between microservices.
Implemented Spring Boot microservices with Eureka, enhancing scalability, fault tolerance, and distributed service discovery.
Developed CRUD operations with prepared statements to prevent SQL injection risks.
Utilized Spring Security to enforce authentication and authorization, securing RESTful web applications.
Optimized SQL queries and PostgreSQL partitioning, improving performance for large datasets in Java applications.
Planned and executed migration strategies to transition from monolithic to microservices architecture.
Developed and executed Component & Integration Test suites in Python, ensuring application reliability and correctness.
Integrated React frontend with Spring Boot microservices via REST APIs to enable full-stack communication.
Conducted code reviews and implemented best practices to improve software quality.
Managed dependencies using Maven and Artifactory, streamlining Java project builds and version control.
Built RESTful APIs and implemented Spring Data JPA with Hibernate 5.3, simplifying database interactions.
Leveraged MongoDB for handling unstructured data, improving flexibility in data storage and retrieval.
Optimized shell scripts for automation, scheduling, and process management across development and production environments.
Improved UI responsiveness and load speed by optimizing rendering logic within React components.
Applied the MVC pattern using JSP, Servlets, and JavaBeans for structured development.
Utilized Facelets for reusable UI templates and improved maintainability.
Configured Oracle 19c and MongoDB 4.4, enhancing database performance for Java applications.
Deployed microservices on AWS (EC2, S3, RDS), ensuring scalability, security, and fault tolerance.
Utilized Kafka for event-driven architecture, improving real-time communication between distributed services.
Client:  - Fuse machines, Ave, New York		                                                             July 2017 -Feb 2019
Software Developer
Responsibilities: 
Developed and maintained backend services using Java and Spring 3.x for processing financial transactions and customer data.
Built interactive applications using HTML5, CSS3, JavaScript, jQuery, AJAX, JSON, ReactJS, Backbone.js, and Bootstrap.
Implemented multithreading in Java to optimize backend processes, improving transaction speed and system efficiency.
Collaborated with backend developers to define RESTful API contracts compatible with React front-end needs.
Utilized PostgreSQL stored procedures and functions for efficient data manipulation within Java applications.
Worked with PostgreSQL on cloud platforms like AWS RDS, Google Cloud SQL, and Azure Database for PostgreSQL.
Ensured timely project delivery by following SDLC best practices.
Boosted testability by writing unit tests for React components using Jest and React Testing Library.
Optimized JSF-based applications for better performance and cross-browser compatibility.
Implemented log analysis and error handling to enhance system reliability during job execution.
Integrated Hibernate 3 for ORM, ensuring seamless data persistence and optimized database interactions.
Developed interactive web applications with React , Ajax, ensuring real-time data updates and improved UI responsiveness.
Migrated old UI codebases to React to enhance maintainability, performance, and user interaction.
Applied custom validation controls in JavaScript and React JS for robust form validation in web applications.
Debugged JavaScript issues using browser DevTools and logging for enhanced frontend performance.
Developed and maintained CI/CD pipelines using GitHub Actions to automate testing, builds, and deployments.
Built and deployed microservices using Docker containers for consistency across development, testing, and production.
Orchestrated Kubernetes-managed Docker containers to enable scalable deployment of Spring Boot microservices.
Implemented performance optimizations to handle high-traffic monolithic applications.
Collaborated with teams to adopt TDD best practices for high-quality software development.
Managed project dependencies using Maven, ensuring streamlined Java development workflows and version consistency.
Created real-time data dashboards using React, Chart.js, and REST APIs for dynamic visualization.
In-depth knowledge of all SDLC phases, from requirement gathering to deployment.
Optimized database performance by tuning JDBC configurations and indexing strategies.
Integrated JavaScript with Java-based backend services via RESTful APIs for enhanced interactivity.
Optimized Java code to improve application performance and reduce latency in microservices architecture.
Customized third-party React components to align with specific project requirements and design standards.
Automated build, testing, and deployment using Jenkins and Maven for a continuous integration workflow.
Implemented transaction management and connection pooling for PostgreSQL within Java-based applications.
Integrated Apache Kafka with Spring Boot microservices for efficient message-driven architecture.
Leveraged ES6+ JavaScript features like arrow functions, promises, and async/await for cleaner, efficient code.
Used React Hooks to manage state and side effects efficiently within functional components.
Developed automated tests using Selenium and SoapUI to ensure software quality before deployment.
Ensured seamless database interactions within monolithic structures using JDBC and Hibernate.
Education:
Bachelors in information technology		 					              May 2019 
University of Bedfordshire
Masters in Artificial Intelligence and Data Science						 Dec 2023
Eastern Kentucky University