{"event": "session_start", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "timestamp": "2025-06-18T16:05:59.448130", "message": "New API session started"}
{"event": "request_start", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4594c5f5-ce6d-4b3f-af53-1d2d5e99ddd0", "endpoint": "/", "timestamp": "2025-06-18T16:06:27.711765", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4594c5f5-ce6d-4b3f-af53-1d2d5e99ddd0", "endpoint": "/", "timestamp": "2025-06-18T16:06:27.712763", "total_time_seconds": 0.0009975433349609375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "75fca681-538e-4f6d-ac4a-781d93d3778e", "endpoint": "/", "timestamp": "2025-06-18T16:06:39.791627", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "75fca681-538e-4f6d-ac4a-781d93d3778e", "endpoint": "/", "timestamp": "2025-06-18T16:06:39.792626", "total_time_seconds": 0.0009982585906982422, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.821454", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.848022", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.848022", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.848022", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.848022", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:06:41.849021", "file_processing_time": 0.023055315017700195, "message": "Custom metric: file_processing_time=0.023055315017700195"}
{"event": "request_complete", "session_id": "bc12e9be-a46c-47fe-b351-2477574d6315", "request_id": "4e086c5b-a07d-45d5-ade7-0f87a7caf54f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T16:07:28.300570", "total_time_seconds": 46.47911548614502, "status_code": 200, "message": "Request completed in 46.4791s with status 200"}
