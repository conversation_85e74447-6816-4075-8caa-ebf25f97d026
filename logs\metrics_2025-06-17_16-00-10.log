{"event": "session_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "timestamp": "2025-06-17T16:00:10.798370", "message": "New API session started"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "d9ed3bbe-177e-4c73-b22c-d44de81f3348", "endpoint": "/", "timestamp": "2025-06-17T16:00:12.180546", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "d9ed3bbe-177e-4c73-b22c-d44de81f3348", "endpoint": "/", "timestamp": "2025-06-17T16:00:12.182599", "total_time_seconds": 0.0020525455474853516, "status_code": 200, "message": "Request completed in 0.0021s with status 200"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "30179175-3f4f-4969-8788-0d64b2b1f30b", "endpoint": "/favicon.ico", "timestamp": "2025-06-17T16:00:12.310720", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "30179175-3f4f-4969-8788-0d64b2b1f30b", "endpoint": "/favicon.ico", "timestamp": "2025-06-17T16:00:12.312022", "total_time_seconds": 0.0013017654418945312, "status_code": 404, "message": "Request completed in 0.0013s with status 404"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "2bd9e841-2c48-4936-b07f-45cb1a30743e", "endpoint": "/docs", "timestamp": "2025-06-17T16:00:14.994592", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "2bd9e841-2c48-4936-b07f-45cb1a30743e", "endpoint": "/docs", "timestamp": "2025-06-17T16:00:14.994592", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "ab7e1e59-d674-4533-9153-fcb890d5fca9", "endpoint": "/openapi.json", "timestamp": "2025-06-17T16:00:15.172974", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "ab7e1e59-d674-4533-9153-fcb890d5fca9", "endpoint": "/openapi.json", "timestamp": "2025-06-17T16:00:15.186615", "total_time_seconds": 0.013640642166137695, "status_code": 200, "message": "Request completed in 0.0136s with status 200"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:33.998504", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:34.040152", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:34.040152", "file_size_bytes": 64332, "message": "Custom metric: file_size_bytes=64332"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:34.040152", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:34.040152", "extracted_text_length": 3518, "message": "Custom metric: extracted_text_length=3518"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:00:34.041152", "file_processing_time": 0.037134408950805664, "message": "Custom metric: file_processing_time=0.037134408950805664"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "64e4ca25-3686-458a-8cfa-487e21ba5c55", "endpoint": "/section", "timestamp": "2025-06-17T16:01:02.912109", "total_time_seconds": 28.91360592842102, "status_code": 200, "message": "Request completed in 28.9136s with status 200"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.084745", "message": "Request started for endpoint: /section"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.112632", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.112632", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.112632", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.112632", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:01:55.113633", "file_processing_time": 0.02377033233642578, "message": "Custom metric: file_processing_time=0.02377033233642578"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "37bcaebe-69e8-4e3c-b63b-276bdf2e4256", "endpoint": "/section", "timestamp": "2025-06-17T16:02:06.391420", "total_time_seconds": 11.306674718856812, "status_code": 200, "message": "Request completed in 11.3067s with status 200"}
{"event": "request_start", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.309656", "message": "Request started for endpoint: /section2"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.333018", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.333018", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.334018", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.334018", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:02:48.334018", "file_processing_time": 0.0213625431060791, "message": "Custom metric: file_processing_time=0.0213625431060791"}
{"event": "request_complete", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "request_id": "274a4426-ce64-4a31-9e71-22804b249802", "endpoint": "/section2", "timestamp": "2025-06-17T16:03:03.358509", "total_time_seconds": 15.04885220527649, "status_code": 200, "message": "Request completed in 15.0489s with status 200"}
{"event": "session_end", "session_id": "b1eabcd3-fb91-4545-9826-9686060024c1", "timestamp": "2025-06-17T16:04:43.333680", "message": "API session ended"}
