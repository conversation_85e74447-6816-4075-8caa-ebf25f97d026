================================================================================
LLM CALL LOG - 2025-06-17 15:58:25
================================================================================

[CALL INFORMATION]
Endpoint: /section
Context: test.txt_experience
Call Type: section_extraction
Model: gemma3:4b
Timestamp: 2025-06-17T15:58:25.912282
Metadata: {
  "timeout_seconds": 45,
  "max_tokens": 800,
  "processing_time": 6.750318288803101,
  "has_image": false,
  "prompt_length": 527,
  "response_length": 225,
  "eval_count": 49,
  "prompt_eval_count": 119,
  "model_total_duration": 6693275100
}

[PROMPT]
Length: 527 characters
----------------------------------------
Extract ONLY the work experience, professional experience, or employment history section from this resume text. Include all job titles, companies, dates, and responsibilities.
Return only the experience content without section headers. If no experience is found, return 'NOT_FOUND'.

Resume Text:
John Doe\nSoftware Engineer\nExperience: 5 years at TechCorp

Important: Return ONLY the experience content. Do not include section headers, explanations, or any other text. If the section is not found, return exactly 'NOT_FOUND'.
----------------------------------------

[RESPONSE]
Length: 225 characters
----------------------------------------
TechCorp 2019 – Present
* Developed and maintained software applications using Java and Python.
* Collaborated with cross-functional teams to design and implement new features.
* Troubleshooted and resolved software defects.

----------------------------------------

================================================================================