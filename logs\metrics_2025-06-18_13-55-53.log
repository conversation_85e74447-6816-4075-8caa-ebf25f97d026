{"event": "session_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "timestamp": "2025-06-18T13:55:53.748693", "message": "New API session started"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "7de276a7-a48d-45aa-83aa-0abea64ad92b", "endpoint": "/", "timestamp": "2025-06-18T13:55:55.193691", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "7de276a7-a48d-45aa-83aa-0abea64ad92b", "endpoint": "/", "timestamp": "2025-06-18T13:55:55.196689", "total_time_seconds": 0.00299835205078125, "status_code": 200, "message": "Request completed in 0.0030s with status 200"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "6cdb5b71-cec1-4f9c-b02c-0f93fc33d1a4", "endpoint": "/docs", "timestamp": "2025-06-18T13:55:57.300976", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "6cdb5b71-cec1-4f9c-b02c-0f93fc33d1a4", "endpoint": "/docs", "timestamp": "2025-06-18T13:55:57.301979", "total_time_seconds": 0.0010023117065429688, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "6dab89c8-32fc-4ffa-b9b3-d9e6247dfcb9", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:55:57.392931", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "6dab89c8-32fc-4ffa-b9b3-d9e6247dfcb9", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:55:57.421440", "total_time_seconds": 0.028509140014648438, "status_code": 200, "message": "Request completed in 0.0285s with status 200"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.828204", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.907636", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.907636", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.907636", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.908638", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:01.908638", "file_processing_time": 0.015223264694213867, "message": "Custom metric: file_processing_time=0.015223264694213867"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "ddc4eaa0-288d-4c5a-b554-3a450e48c64e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:57:15.840582", "total_time_seconds": 14.012377262115479, "status_code": 200, "message": "Request completed in 14.0124s with status 200"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.831556", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.855556", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.855556", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.856557", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.856557", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:27.856557", "file_processing_time": 0.014999866485595703, "message": "Custom metric: file_processing_time=0.014999866485595703"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "2b6673ca-4488-459b-a539-ba680a48e1fb", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:58:41.572500", "total_time_seconds": 13.740943908691406, "status_code": 200, "message": "Request completed in 13.7409s with status 200"}
{"event": "request_start", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.547451", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.568456", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.568456", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.568456", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.568456", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T13:59:59.568456", "file_processing_time": 0.018001794815063477, "message": "Custom metric: file_processing_time=0.018001794815063477"}
{"event": "request_complete", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "request_id": "aa50ce5f-b218-454e-89cf-6794904b452e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:00:26.692807", "total_time_seconds": 27.145355939865112, "status_code": 200, "message": "Request completed in 27.1454s with status 200"}
{"event": "session_end", "session_id": "5b6187cd-8427-477a-a0a9-78a9496d082f", "timestamp": "2025-06-18T14:05:53.291679", "message": "API session ended"}
