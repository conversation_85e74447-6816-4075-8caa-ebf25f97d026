{"event": "session_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "timestamp": "2025-06-18T13:05:19.495738", "message": "New API session started"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f688a087-dd3c-4486-a390-9f53128ef3af", "endpoint": "/", "timestamp": "2025-06-18T13:05:21.386302", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f688a087-dd3c-4486-a390-9f53128ef3af", "endpoint": "/", "timestamp": "2025-06-18T13:05:21.387304", "total_time_seconds": 0.001001596450805664, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "3c59c750-b54c-409b-8a2c-116613c437f2", "endpoint": "/favicon.ico", "timestamp": "2025-06-18T13:05:21.455743", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "3c59c750-b54c-409b-8a2c-116613c437f2", "endpoint": "/favicon.ico", "timestamp": "2025-06-18T13:05:21.456746", "total_time_seconds": 0.0010035037994384766, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "6e8daca3-cd72-4c42-a1b0-dc2dcc7e8a31", "endpoint": "/docs", "timestamp": "2025-06-18T13:05:23.831798", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "6e8daca3-cd72-4c42-a1b0-dc2dcc7e8a31", "endpoint": "/docs", "timestamp": "2025-06-18T13:05:23.831798", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "3331daaa-4a3a-4206-86ee-53fa488c5ff5", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:05:23.928677", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "3331daaa-4a3a-4206-86ee-53fa488c5ff5", "endpoint": "/openapi.json", "timestamp": "2025-06-18T13:05:23.961747", "total_time_seconds": 0.03306984901428223, "status_code": 200, "message": "Request completed in 0.0331s with status 200"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:51.961595", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:51.999628", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:51.999628", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:51.999628", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:51.999628", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:52.000628", "file_processing_time": 0.031028270721435547, "message": "Custom metric: file_processing_time=0.031028270721435547"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "95de23b7-b636-4c56-ade3-d137e063a7af", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:52.008638", "total_time_seconds": 0.0470423698425293, "status_code": 200, "message": "Request completed in 0.0470s with status 200"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.418093", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.442094", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.442094", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.442094", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.442094", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.442094", "file_processing_time": 0.019994497299194336, "message": "Custom metric: file_processing_time=0.019994497299194336"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "d5cd1a33-e497-4644-b050-4bd00b5746a0", "endpoint": "/section3", "timestamp": "2025-06-18T13:05:53.447094", "total_time_seconds": 0.029000520706176758, "status_code": 200, "message": "Request completed in 0.0290s with status 200"}
{"event": "request_start", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.172024", "message": "Request started for endpoint: /section3"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.201035", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.201035", "file_size_bytes": 33611, "message": "Custom metric: file_size_bytes=33611"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.201035", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.201035", "extracted_text_length": 23903, "message": "Custom metric: extracted_text_length=23903"}
{"event": "custom_metric", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.201035", "file_processing_time": 0.028012514114379883, "message": "Custom metric: file_processing_time=0.028012514114379883"}
{"event": "request_complete", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "request_id": "f075c174-4d96-412a-ae30-7340e72918fd", "endpoint": "/section3", "timestamp": "2025-06-18T13:06:21.208037", "total_time_seconds": 0.03601384162902832, "status_code": 200, "message": "Request completed in 0.0360s with status 200"}
{"event": "session_end", "session_id": "5e0f9472-9ebb-4c3f-96ad-45ecde86d8db", "timestamp": "2025-06-18T13:15:19.065778", "message": "API session ended"}
