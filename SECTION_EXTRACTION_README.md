# Resume Section Extraction

This document describes the resume section extraction functionality designed to test the Gemma model's capabilities for isolating and extracting specific sections from resume documents.

## Overview

The section extraction system provides two different approaches to extract resume sections:

1. **Multiple Calls Method** (`/section`) - Makes individual LLM calls for each section
2. **Single Call Method** (`/section2`) - Extracts all sections in one comprehensive call

Both methods extract the following sections:
- **Summary/Objective** - Professional summary or career objective
- **Education** - Educational background and qualifications
- **Experience** - Work experience and employment history
- **Skills** - Technical and soft skills
- **Projects** - Personal or professional projects
- **Certifications** - Professional certifications and licenses
- **Achievements** - Awards, honors, and accomplishments
- **Languages** - Language skills and proficiency levels

## API Endpoints

### POST /section (Multiple Calls Method)

Extracts resume sections using individual LLM calls for each section.

**Request:**
```http
POST /section
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "filename": "john_doe_resume.pdf",
  "extraction_method": "multiple_calls",
  "sections_extracted": {
    "summary": "Experienced software engineer with 5+ years...",
    "education": "Bachelor of Science in Computer Science...",
    "experience": "Senior Software Engineer at TechCorp...",
    "skills": "Python, JavaScript, React, AWS...",
    "projects": "E-commerce Platform - Built a full-stack...",
    "certifications": "AWS Certified Solutions Architect...",
    "achievements": "Winner of TechCorp Hackathon 2023...",
    "languages": "English: Native, Spanish: Conversational..."
  },
  "extraction_stats": {
    "processing_time": 45.67,
    "total_calls": 8,
    "overall_confidence": 0.85,
    "text_length": 2345,
    "sections_found": 7
  },
  "confidence_scores": {
    "summary": 0.9,
    "education": 0.85,
    "experience": 0.92,
    "skills": 0.88,
    "projects": 0.75,
    "certifications": 0.8,
    "achievements": 0.7,
    "languages": 0.6
  },
  "overall_confidence": 0.85,
  "processing_time": 45.67,
  "errors": []
}
```

### POST /section2 (Single Call Method)

Extracts all resume sections using a single comprehensive LLM call.

**Request:**
```http
POST /section2
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "filename": "john_doe_resume.pdf",
  "extraction_method": "single_call",
  "sections_extracted": {
    "summary": "Experienced software engineer with 5+ years...",
    "education": "Bachelor of Science in Computer Science...",
    "experience": "Senior Software Engineer at TechCorp...",
    "skills": "Python, JavaScript, React, AWS...",
    "projects": "E-commerce Platform - Built a full-stack...",
    "certifications": "AWS Certified Solutions Architect...",
    "achievements": "Winner of TechCorp Hackathon 2023...",
    "languages": "English: Native, Spanish: Conversational..."
  },
  "extraction_stats": {
    "processing_time": 15.23,
    "total_calls": 1,
    "overall_confidence": 0.78,
    "text_length": 2345,
    "sections_found": 6
  },
  "confidence_scores": {
    "summary": 0.8,
    "education": 0.75,
    "experience": 0.85,
    "skills": 0.8,
    "projects": 0.7,
    "certifications": 0.75,
    "achievements": 0.65,
    "languages": 0.5
  },
  "overall_confidence": 0.78,
  "processing_time": 15.23,
  "errors": []
}
```

## Output Files

All extraction results are automatically saved to the `resume sections extracted` directory with the following naming convention:

```
{timestamp}_{filename}_{method}_sections.txt
```

Example: `20241217_john_doe_resume_multiple_calls_sections.txt`

### File Format

```
================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: john_doe_resume.pdf
Extraction Method: multiple_calls
Timestamp: 2024-12-17T14:30:52.123456
Total Sections Extracted: 7
Processing Time: 45.67 seconds
Total LLM Calls: 8
Overall Confidence: 0.85
================================================================================

[SUMMARY]
----------------------------------------
Experienced software engineer with 5+ years of expertise in full-stack 
development, machine learning, and cloud technologies. Proven track record 
of delivering scalable solutions and leading cross-functional teams.

========================================

[EDUCATION]
----------------------------------------
Bachelor of Science in Computer Science
University of California, Berkeley
2016-2020
GPA: 3.8/4.0
Relevant Coursework: Data Structures, Algorithms, Machine Learning

========================================

[EXPERIENCE]
----------------------------------------
Senior Software Engineer
TechCorp Inc., San Francisco, CA
January 2022 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented machine learning models for recommendation systems

========================================

... (continues for all sections)
```

## Method Comparison

### Multiple Calls Method (/section)

**Advantages:**
- ✅ More focused prompts per section
- ✅ Individual confidence scores for each section
- ✅ Better error isolation and handling
- ✅ Potentially higher accuracy per section
- ✅ Detailed error reporting

**Disadvantages:**
- ❌ More API calls (higher cost)
- ❌ Longer processing time
- ❌ More complex error handling
- ❌ Higher token usage

**Best for:** High-accuracy requirements, detailed analysis, debugging

### Single Call Method (/section2)

**Advantages:**
- ✅ Faster processing (single call)
- ✅ Lower cost and token usage
- ✅ Simpler implementation
- ✅ Context awareness across sections
- ✅ Consistent formatting

**Disadvantages:**
- ❌ May miss some sections
- ❌ Less focused extraction
- ❌ Harder to debug failures
- ❌ Lower individual section accuracy

**Best for:** Quick analysis, cost efficiency, batch processing

## Testing

### Using the Test Script

```bash
cd testing
python test_section_extraction.py
```

This script will:
1. Test both endpoints with available resume files
2. Compare processing times and accuracy
3. Show detailed section extraction results
4. Analyze confidence scores

### Using the Demo Script

```bash
python demo_section_extraction.py
```

This script demonstrates the functionality with sample resume text.

## Confidence Scoring

The system provides confidence scores for each extracted section:

- **0.9-1.0**: High confidence - Section clearly identified and extracted
- **0.7-0.8**: Good confidence - Section found with minor uncertainties
- **0.5-0.6**: Medium confidence - Section partially identified
- **0.0-0.4**: Low confidence - Section unclear or not found

Factors affecting confidence:
- Content length and structure
- Presence of section keywords
- Text quality and formatting
- Model response consistency

## Error Handling

The system handles various error scenarios:

- **File format errors**: Invalid PDF/DOCX files
- **Text extraction failures**: Corrupted or image-only files
- **LLM timeout errors**: Long processing times
- **Section parsing errors**: Malformed responses
- **Individual section failures**: Isolated extraction errors

## Best Practices

1. **File Quality**: Use high-quality, text-based PDF/DOCX files
2. **File Size**: Keep files under 10MB for optimal performance
3. **Method Selection**: 
   - Use `/section` for accuracy-critical applications
   - Use `/section2` for speed and cost efficiency
4. **Error Monitoring**: Check confidence scores and error arrays
5. **Result Validation**: Review extracted sections for accuracy

## Troubleshooting

### Common Issues

1. **Empty sections**: Check if the resume actually contains those sections
2. **Low confidence**: May indicate poor text quality or unusual formatting
3. **Timeout errors**: Large files or complex layouts may need longer processing
4. **Missing sections**: Some resumes may not have all standard sections

### Performance Optimization

- Use appropriate timeout values based on file size
- Monitor token usage for cost optimization
- Implement caching for repeated extractions
- Consider preprocessing for better text quality

## Integration Examples

### Python Client

```python
import requests

# Test multiple calls method
with open('resume.pdf', 'rb') as f:
    files = {'file': ('resume.pdf', f, 'application/pdf')}
    response = requests.post('http://localhost:8000/section', files=files)
    result = response.json()

print(f"Sections found: {result['extraction_stats']['sections_found']}")
for section, content in result['sections_extracted'].items():
    if content and content.strip():
        print(f"{section}: {content[:100]}...")
```

### cURL Example

```bash
curl -X POST "http://localhost:8000/section" \
     -F "file=@resume.pdf" \
     -H "accept: application/json"
```

This section extraction system provides comprehensive testing capabilities for evaluating the Gemma model's performance on resume parsing tasks, with detailed logging and analysis features.
