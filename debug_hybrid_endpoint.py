#!/usr/bin/env python3
"""
Debug script for the /hybrid_resume endpoint.
Tests each resume individually to identify issues.
"""

import requests
import json
import time
import os
from pathlib import Path
import traceback

BASE_URL = "http://localhost:8000"

def test_individual_resume(file_path):
    """Test a single resume file with the hybrid endpoint."""
    
    print(f"\n🧪 Testing: {file_path.name}")
    print("-" * 60)
    
    try:
        # Check file size
        file_size = file_path.stat().st_size
        print(f"📄 File size: {file_size:,} bytes")
        
        # Determine content type
        if file_path.suffix.lower() == '.pdf':
            content_type = 'application/pdf'
        elif file_path.suffix.lower() == '.docx':
            content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            content_type = 'application/octet-stream'
        
        print(f"📋 Content type: {content_type}")
        
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, content_type)}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            
            # Analyze the response
            print(f"📊 Response Analysis:")
            print(f"   👤 Name: {result.get('name', 'Not found')}")
            print(f"   📧 Email: {result.get('email', 'Not found')}")
            print(f"   📞 Phone: {result.get('phone', 'Not found')}")
            print(f"   🎓 Education: {len(result.get('education', []))} entries")
            print(f"   💼 Experience: {len(result.get('experience', []))} entries")
            print(f"   🛠️ Skills: {len(result.get('skills', []))} items")
            print(f"   🚀 Projects: {len(result.get('projects', []))} items")
            print(f"   🏆 Certifications: {len(result.get('certifications', []))} items")
            
            # Check processing metadata
            print(f"   ⏱️ Processing time: {result.get('processing_time', 'N/A')}")
            print(f"   🔧 Method: {result.get('extraction_method', 'N/A')}")
            print(f"   📋 Sections extracted: {result.get('sections_extracted', 'N/A')}")
            print(f"   📈 Regex confidence: {result.get('regex_confidence', 'N/A')}")
            
            return True, result
            
        else:
            print("❌ FAILED!")
            print(f"Error: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"Error detail: {error_data.get('detail', 'No detail provided')}")
            except:
                pass
            
            return False, response.text
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        traceback.print_exc()
        return False, str(e)

def test_section3_for_comparison(file_path):
    """Test the same file with section3 to see if the issue is in regex extraction."""
    
    print(f"\n🔍 Testing {file_path.name} with /section3 for comparison...")
    
    try:
        if file_path.suffix.lower() == '.pdf':
            content_type = 'application/pdf'
        elif file_path.suffix.lower() == '.docx':
            content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            content_type = 'application/octet-stream'
        
        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, content_type)}
            response = requests.post(f"{BASE_URL}/section3", files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            sections = result.get('sections_extracted', {})
            sections_with_content = {k: v for k, v in sections.items() if v and v.strip()}
            
            print(f"✅ Section3 SUCCESS: {len(sections_with_content)} sections extracted")
            
            # Check for None values in sections
            none_sections = [k for k, v in sections.items() if v is None]
            if none_sections:
                print(f"⚠️ Sections with None values: {none_sections}")
            
            return True, sections
        else:
            print(f"❌ Section3 FAILED: {response.status_code}")
            return False, response.text
            
    except Exception as e:
        print(f"❌ Section3 EXCEPTION: {e}")
        return False, str(e)

def debug_local_processing(file_path):
    """Debug the local processing functions to identify the exact issue."""
    
    print(f"\n🔧 Local Debug for: {file_path.name}")
    print("-" * 40)
    
    try:
        # Import the functions we need
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from main import extract_text_from_file, extract_sections_regex, parse_sections_with_gemma
        
        # Step 1: Test text extraction
        print("📄 Step 1: Text extraction...")
        file_type = "pdf" if file_path.suffix.lower() == ".pdf" else "docx"
        
        extracted_text = extract_text_from_file(
            str(file_path),
            file_type,
            request_metrics=None,
            source_filename=file_path.name,
            context="debug"
        )
        
        if not extracted_text:
            print("❌ Text extraction failed - no text extracted")
            return False
        
        print(f"✅ Text extracted: {len(extracted_text)} characters")
        
        # Step 2: Test regex section extraction
        print("🔍 Step 2: Regex section extraction...")
        sections, confidence_scores = extract_sections_regex(extracted_text, file_path.name, "")
        
        print(f"✅ Sections extracted: {len(sections)}")
        
        # Check for None values
        none_sections = []
        for section_name, content in sections.items():
            if content is None:
                none_sections.append(section_name)
                print(f"⚠️ Section '{section_name}' is None")
            elif not isinstance(content, str):
                print(f"⚠️ Section '{section_name}' is not a string: {type(content)}")
        
        if none_sections:
            print(f"❌ Found {len(none_sections)} None sections: {none_sections}")
            return False
        
        # Step 3: Test LLM processing
        print("🤖 Step 3: LLM processing...")
        try:
            structured_data = parse_sections_with_gemma(sections, file_path.name)
            print(f"✅ LLM processing completed")
            
            if "error" in structured_data:
                print(f"❌ LLM processing error: {structured_data['error']}")
                return False
            
            return True
            
        except Exception as llm_error:
            print(f"❌ LLM processing failed: {llm_error}")
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Local debug failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debugging function."""
    
    print("🐛 Hybrid Resume Endpoint Debugging")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Start server with: python main.py")
        return
    
    # Get all resume files
    resume_dir = Path("resumes for testing")
    if not resume_dir.exists():
        print(f"❌ Resume directory not found: {resume_dir}")
        return
    
    resume_files = list(resume_dir.glob("*.pdf")) + list(resume_dir.glob("*.docx"))
    
    if not resume_files:
        print("❌ No resume files found")
        return
    
    print(f"📁 Found {len(resume_files)} resume files")
    
    # Test each resume
    results = {}
    
    for i, file_path in enumerate(resume_files, 1):
        print(f"\n{'='*70}")
        print(f"📄 TESTING {i}/{len(resume_files)}: {file_path.name}")
        print(f"{'='*70}")
        
        # Test with hybrid endpoint
        success, result = test_individual_resume(file_path)
        results[file_path.name] = {'hybrid_success': success, 'hybrid_result': result}
        
        if not success:
            print(f"\n🔍 DEBUGGING FAILED RESUME: {file_path.name}")
            
            # Test with section3 for comparison
            section3_success, section3_result = test_section3_for_comparison(file_path)
            results[file_path.name]['section3_success'] = section3_success
            results[file_path.name]['section3_result'] = section3_result
            
            # Debug local processing
            local_debug_success = debug_local_processing(file_path)
            results[file_path.name]['local_debug_success'] = local_debug_success
        
        # Add a small delay between tests
        time.sleep(1)
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 DEBUGGING SUMMARY")
    print(f"{'='*70}")
    
    successful = [name for name, result in results.items() if result['hybrid_success']]
    failed = [name for name, result in results.items() if not result['hybrid_success']]
    
    print(f"✅ Successful: {len(successful)}/{len(resume_files)}")
    for name in successful:
        print(f"   ✅ {name}")
    
    if failed:
        print(f"\n❌ Failed: {len(failed)}/{len(resume_files)}")
        for name in failed:
            print(f"   ❌ {name}")
            result = results[name]
            if 'section3_success' in result:
                section3_status = "✅" if result['section3_success'] else "❌"
                print(f"      Section3: {section3_status}")
            if 'local_debug_success' in result:
                local_status = "✅" if result['local_debug_success'] else "❌"
                print(f"      Local Debug: {local_status}")
    
    print(f"\n🎯 Next Steps:")
    if failed:
        print("1. Check the specific error messages above")
        print("2. Look at conversation logs for failed resumes")
        print("3. Check if the issue is in text extraction, regex extraction, or LLM processing")
    else:
        print("✅ All resumes processed successfully!")

if __name__ == "__main__":
    main()
