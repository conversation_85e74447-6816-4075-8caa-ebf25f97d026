================================================================================
LLM CALL LOG - 2025-06-17 17:26:14
================================================================================

[CALL INFORMATION]
Endpoint: /section
Context: test.txt_experience
Call Type: section_extraction
Model: gemma3:4b
Timestamp: 2025-06-17T17:26:14.813325
Metadata: {
  "timeout_seconds": 45,
  "max_tokens": 800,
  "processing_time": 0.6486308574676514,
  "has_image": false,
  "prompt_length": 1152,
  "response_length": 62,
  "eval_count": 18,
  "prompt_eval_count": 254,
  "model_total_duration": *********
}

[PROMPT]
Length: 1152 characters
----------------------------------------
Look for a section in the resume with headings like "EXPERIENCE", "WORK EXPERIENCE", "PROFESSIONAL EXPERIENCE", "EMPLOYMENT HISTORY", "CAREER HISTORY", or "EMPLOYMENT".
Extract ONLY the text that appears directly under that specific section heading. This should include job titles, company names, dates, and job responsibilities.
Do not include information from other sections like achievements, projects, skills, or summary. Return exactly what is written under the work experience section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
PROFESSIONAL SUMMARY
Experienced engineer with 5 years

WORK EXPERIENCE
Senior Engineer at TechCorp
2022-Present
Led development team

PROJECTS
E-commerce Platform
Built with React

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content
----------------------------------------

[RESPONSE]
Length: 62 characters
----------------------------------------
Senior Engineer at TechCorp
2022-Present
Led development team

----------------------------------------

================================================================================