#!/usr/bin/env python3
"""
Debug the JSON cleanup function to see why it's not working.
"""

import json

def test_json_cleanup():
    """Test the JSON cleanup logic."""
    
    # Sample JSON with markdown (like what we're getting from LLM)
    sample_json_with_markdown = '''```json
{
    "name": "Anurag Pandey",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artificial intelligence)",
            "institution": "Newton School of Technology , Rishihood University",
            "year": "2024 - 2028"
        }
    ],
    "skills": [
        "Python",
        "JavaScript",
        "CSS",
        "HTML"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Calculator Project",
            "description": "A web-based calculator with a user-friendly interface"
        }
    ],
    "certifications": [
        "Python for Beginners , Newton School Of Technology ( Link ) August 2024"
    ],
    "achievements": [
        "I am a basketball player and have played in inter school matches."
    ],
    "summary": "Aspiring developer and tech enthusiast"
}
```'''

    print("🧪 Testing JSON Cleanup Logic")
    print("=" * 60)
    
    print(f"📄 Original JSON with markdown:")
    print(f"Length: {len(sample_json_with_markdown)} characters")
    print(f"Starts with: {repr(sample_json_with_markdown[:50])}")
    print(f"Ends with: {repr(sample_json_with_markdown[-20:])}")
    
    # Apply our cleanup logic
    json_str = sample_json_with_markdown
    
    print(f"\n🔧 Step 1: Initial cleanup")
    json_str = json_str.strip()
    print(f"After strip: {repr(json_str[:50])}")
    
    print(f"\n🔧 Step 2: Markdown removal")
    if "```json" in json_str:
        print("Found ```json marker")
        start_marker = "```json"
        end_marker = "```"
        start_idx = json_str.find(start_marker)
        print(f"Start index: {start_idx}")
        if start_idx != -1:
            start_idx += len(start_marker)
            print(f"Content start index: {start_idx}")
            end_idx = json_str.find(end_marker, start_idx)
            print(f"End index: {end_idx}")
            if end_idx != -1:
                json_str = json_str[start_idx:end_idx].strip()
                print(f"Extracted content: {repr(json_str[:50])}")
    
    print(f"\n🔧 Step 3: Additional cleanup")
    if json_str.startswith('```json'):
        json_str = json_str[7:].strip()
        print("Removed leading ```json")
    if json_str.startswith('```'):
        json_str = json_str[3:].strip()
        print("Removed leading ```")
    if json_str.endswith('```'):
        json_str = json_str[:-3].strip()
        print("Removed trailing ```")
    
    json_str = json_str.replace('```json', '').replace('```', '').strip()
    print("Removed any remaining markdown")
    
    print(f"\n📊 Final result:")
    print(f"Length: {len(json_str)} characters")
    print(f"Starts with: {repr(json_str[:50])}")
    print(f"Ends with: {repr(json_str[-20:])}")
    
    # Test JSON parsing
    print(f"\n🧪 Testing JSON parsing:")
    try:
        parsed_data = json.loads(json_str)
        print("✅ JSON parsing successful!")
        print(f"Name: {parsed_data.get('name')}")
        print(f"Education count: {len(parsed_data.get('education', []))}")
        print(f"Skills count: {len(parsed_data.get('skills', []))}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
        print(f"Error at position: {e.pos}")
        if e.pos < len(json_str):
            print(f"Character at error: {repr(json_str[e.pos])}")
            print(f"Context: {repr(json_str[max(0, e.pos-10):e.pos+10])}")
        return False

def test_alternative_cleanup():
    """Test a simpler, more robust cleanup approach."""
    
    sample_json_with_markdown = '''```json
{
    "name": "Anurag Pandey",
    "email": null,
    "skills": ["Python", "JavaScript"]
}
```'''

    print(f"\n🔧 Testing Alternative Cleanup Approach")
    print("=" * 60)
    
    json_str = sample_json_with_markdown.strip()
    
    # Simple approach: find first { and last }
    start_brace = json_str.find('{')
    end_brace = json_str.rfind('}')
    
    if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
        clean_json = json_str[start_brace:end_brace+1]
        print(f"✅ Extracted JSON between braces:")
        print(f"Length: {len(clean_json)} characters")
        print(f"Starts with: {repr(clean_json[:30])}")
        print(f"Ends with: {repr(clean_json[-10:])}")
        
        try:
            parsed_data = json.loads(clean_json)
            print("✅ JSON parsing successful with brace method!")
            print(f"Name: {parsed_data.get('name')}")
            return True, clean_json
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed with brace method: {e}")
            return False, clean_json
    else:
        print("❌ Could not find JSON braces")
        return False, json_str

if __name__ == "__main__":
    print("🐛 JSON Cleanup Debugging")
    print()
    
    # Test current cleanup logic
    success1 = test_json_cleanup()
    
    # Test alternative approach
    success2, clean_json = test_alternative_cleanup()
    
    print(f"\n📊 Summary:")
    print(f"Current cleanup method: {'✅ Success' if success1 else '❌ Failed'}")
    print(f"Alternative brace method: {'✅ Success' if success2 else '❌ Failed'}")
    
    if success2 and not success1:
        print(f"\n💡 Recommendation: Use the brace extraction method")
        print("This is more robust and handles markdown better")
