"""
Demo script for literal section extraction.

This script demonstrates the improved literal extraction that copies content
exactly as it appears under specific section headings.
"""

import os
import time
from main import extract_single_section, extract_all_sections_single_call, save_section_extraction

def create_test_resume_with_mixed_content():
    """Create a test resume where content could be mixed between sections."""
    return """
<PERSON>
Senior Software Engineer
Email: <EMAIL>
Phone: (*************
Location: San Francisco, CA

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years of expertise in full-stack development, 
machine learning, and cloud technologies. Proven track record of delivering scalable 
solutions and leading cross-functional teams.

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley
2016-2020
GPA: 3.8/4.0
Relevant Coursework: Data Structures, Algorithms, Machine Learning

Master of Science in Software Engineering
Stanford University
2020-2022
Thesis: "Scalable Microservices Architecture for E-commerce Platforms"

WORK EXPERIENCE
Senior Software Engineer
TechCorp Inc., San Francisco, CA
January 2022 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented machine learning models for recommendation systems
• Mentored junior developers and conducted code reviews
• Technologies: Python, React, A<PERSON>, <PERSON><PERSON>, Kubernetes

Software Engineer
StartupXYZ, Palo Alto, CA
June 2020 - December 2021
• Developed full-stack web applications using React and Node.js
• Built RESTful APIs and integrated third-party services
• Optimized database queries resulting in 40% performance improvement
• Technologies: JavaScript, Node.js, PostgreSQL, Redis

TECHNICAL SKILLS
Programming Languages: Python, JavaScript, Java, C++, SQL
Frontend: React, Vue.js, HTML5, CSS3, TypeScript
Backend: Node.js, Django, Flask, Express.js, Spring Boot
Databases: PostgreSQL, MySQL, MongoDB, Redis, Elasticsearch
Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, Git, CI/CD
Machine Learning: TensorFlow, PyTorch, scikit-learn, pandas, NumPy

PROJECTS
E-commerce Platform
• Built a full-stack e-commerce platform with React frontend and Node.js backend
• Implemented payment processing, inventory management, and user authentication
• Deployed on AWS with auto-scaling capabilities and load balancing
• Technologies: React, Node.js, PostgreSQL, AWS, Stripe API

Movie Recommendation System
• Developed a machine learning-based movie recommendation system
• Used collaborative filtering and content-based filtering algorithms
• Achieved 85% accuracy in user preference prediction
• Technologies: Python, TensorFlow, pandas, Flask, Docker

Real-time Chat Application
• Created a real-time chat application with WebSocket support
• Implemented user authentication, message encryption, and file sharing
• Deployed using Docker containers on AWS ECS
• Technologies: Node.js, Socket.io, MongoDB, JWT, AWS

CERTIFICATIONS
AWS Certified Solutions Architect - Associate (2023)
Google Cloud Professional Data Engineer (2022)
Certified Kubernetes Administrator (CKA) (2021)
MongoDB Certified Developer Associate (2020)

ACHIEVEMENTS
• Winner of TechCorp Hackathon 2023 for AI-powered customer service bot
• Published research paper on "Scalable Machine Learning Systems" in IEEE Conference
• Speaker at PyData Conference 2022 on "Building ML Pipelines"
• Dean's List for 4 consecutive semesters at UC Berkeley
• Open source contributor with 500+ GitHub stars across projects

LANGUAGES
English: Native
Spanish: Conversational
Mandarin: Basic
French: Beginner
"""

def test_literal_extraction():
    """Test the literal extraction functionality."""
    print("🔍 Testing Literal Section Extraction")
    print("=" * 60)
    
    sample_text = create_test_resume_with_mixed_content()
    
    # Test individual section extraction
    print("1. Testing Individual Section Extraction (Multiple Calls Method)")
    print("-" * 60)
    
    sections = {}
    confidence_scores = {}
    section_names = [
        "summary", "education", "experience", "skills", 
        "projects", "certifications", "achievements", "languages"
    ]
    
    for section_name in section_names:
        print(f"\nExtracting {section_name}...")
        try:
            content, confidence = extract_single_section(sample_text, section_name, "demo_literal_test.txt")
            sections[section_name] = content
            confidence_scores[section_name] = confidence
            
            print(f"   Confidence: {confidence:.2f}")
            if content and content != "NOT_FOUND" and not content.startswith("ERROR:"):
                # Show first 150 characters
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"   Content: {preview}")
                
                # Check for literal extraction quality
                if section_name == "experience":
                    # Should NOT contain project information
                    if "E-commerce Platform" in content or "Movie Recommendation" in content:
                        print("   ⚠️  WARNING: Experience section contains project information!")
                    else:
                        print("   ✅ Good: Experience section contains only work experience")
                
                elif section_name == "projects":
                    # Should contain project information
                    if "E-commerce Platform" in content or "Movie Recommendation" in content:
                        print("   ✅ Good: Projects section contains project information")
                    else:
                        print("   ⚠️  WARNING: Projects section missing expected project information!")
                
                elif section_name == "achievements":
                    # Should NOT contain project information
                    if "E-commerce Platform" in content or "Movie Recommendation" in content:
                        print("   ⚠️  WARNING: Achievements section contains project information!")
                    else:
                        print("   ✅ Good: Achievements section contains only achievements")
            else:
                print(f"   Content: {content}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            sections[section_name] = f"ERROR: {str(e)}"
            confidence_scores[section_name] = 0.0
    
    # Test single call extraction
    print("\n\n2. Testing Single Call Extraction")
    print("-" * 60)
    
    try:
        single_sections, single_confidence = extract_all_sections_single_call(sample_text, "demo_literal_test.txt")
        
        print("Single call extraction results:")
        for section_name, content in single_sections.items():
            confidence = single_confidence.get(section_name, 0.0)
            print(f"\n{section_name.upper()}:")
            print(f"   Confidence: {confidence:.2f}")
            if content and content.strip():
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"   Content: {preview}")
            else:
                print("   Content: NOT_FOUND")
    
    except Exception as e:
        print(f"❌ Error in single call extraction: {e}")
        single_sections = {}
        single_confidence = {}
    
    # Save results for comparison
    print("\n\n3. Saving Results")
    print("-" * 60)
    
    # Save multiple calls results
    stats_multi = {
        "processing_time": 45.0,
        "total_calls": len(section_names),
        "overall_confidence": sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0.0,
        "text_length": len(sample_text),
        "sections_found": len([s for s in sections.values() if s and s != "NOT_FOUND" and not s.startswith("ERROR:")])
    }
    
    multi_path = save_section_extraction(sections, "demo_literal_multiple.txt", "multiple_calls_literal", stats_multi)
    print(f"✅ Multiple calls results saved to: {multi_path}")
    
    # Save single call results
    if single_sections:
        stats_single = {
            "processing_time": 15.0,
            "total_calls": 1,
            "overall_confidence": sum(single_confidence.values()) / len(single_confidence) if single_confidence else 0.0,
            "text_length": len(sample_text),
            "sections_found": len([s for s in single_sections.values() if s and s.strip()])
        }
        
        single_path = save_section_extraction(single_sections, "demo_literal_single.txt", "single_call_literal", stats_single)
        print(f"✅ Single call results saved to: {single_path}")
    
    # Analysis
    print("\n\n4. Literal Extraction Analysis")
    print("-" * 60)
    
    print("📊 Section Boundary Analysis:")
    
    # Check experience vs projects separation
    exp_content = sections.get("experience", "")
    proj_content = sections.get("projects", "")
    
    if exp_content and proj_content:
        exp_has_projects = any(keyword in exp_content for keyword in ["E-commerce Platform", "Movie Recommendation", "Real-time Chat"])
        proj_has_projects = any(keyword in proj_content for keyword in ["E-commerce Platform", "Movie Recommendation", "Real-time Chat"])
        
        print(f"   Experience section contains projects: {'❌ YES' if exp_has_projects else '✅ NO'}")
        print(f"   Projects section contains projects: {'✅ YES' if proj_has_projects else '❌ NO'}")
    
    # Check achievements vs projects separation
    ach_content = sections.get("achievements", "")
    if ach_content and proj_content:
        ach_has_projects = any(keyword in ach_content for keyword in ["E-commerce Platform", "Movie Recommendation", "Real-time Chat"])
        
        print(f"   Achievements section contains projects: {'❌ YES' if ach_has_projects else '✅ NO'}")
    
    # Check skills compilation
    skills_content = sections.get("skills", "")
    if skills_content:
        expected_skills = ["Python", "JavaScript", "React", "AWS", "TensorFlow", "PostgreSQL"]
        skills_found = sum(1 for skill in expected_skills if skill in skills_content)
        print(f"   Skills compilation: {skills_found}/{len(expected_skills)} expected skills found")
    
    print("\n💡 Literal Extraction Summary:")
    print("   - Content should stay within section boundaries")
    print("   - Projects should only appear in PROJECTS section")
    print("   - Achievements should only appear in ACHIEVEMENTS section")
    print("   - Skills should be compiled from entire resume")
    print("   - No cross-contamination between sections")

if __name__ == "__main__":
    print("🚀 Literal Section Extraction Demo")
    print("This demo tests the improved extraction that copies content exactly as it appears under section headings.")
    print()
    
    # Run the demo
    test_literal_extraction()
    
    print("\n✨ Demo completed!")
    print("Check the 'resume sections extracted' folder for detailed results.")
    print("The extraction should now maintain proper section boundaries.")
