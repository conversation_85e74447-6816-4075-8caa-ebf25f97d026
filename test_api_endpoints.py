#!/usr/bin/env python3
"""
Simple test to verify the API endpoints are working.
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_root_endpoint():
    """Test the root endpoint to see available endpoints."""
    print("🧪 Testing Root Endpoint")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print("✅ API is running!")
            print(f"📋 Available endpoints: {len(data['endpoints'])}")
            
            # Check if section3 is listed
            section3_found = False
            for endpoint in data['endpoints']:
                if endpoint['path'] == '/section3':
                    section3_found = True
                    print(f"✅ Found /section3 endpoint: {endpoint['description']}")
                    break
            
            if not section3_found:
                print("❌ /section3 endpoint not found in the list")
            
            # Show all section endpoints
            print("\n📋 Section extraction endpoints:")
            for endpoint in data['endpoints']:
                if 'section' in endpoint['path']:
                    print(f"   {endpoint['path']}: {endpoint['description']}")
                    
        else:
            print(f"❌ API request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error connecting to API: {e}")

def test_section3_without_file():
    """Test the section3 endpoint without a file to see the error response."""
    print("\n🧪 Testing /section3 Endpoint (without file)")
    print("=" * 40)
    
    try:
        response = requests.post(f"{BASE_URL}/section3")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📝 Response: {response.text[:200]}...")
        
        if response.status_code == 422:
            print("✅ Expected validation error (no file provided)")
        else:
            print("⚠️ Unexpected response")
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

def create_sample_text_file():
    """Create a sample text file that we can use for testing."""
    print("\n📝 Creating Sample Resume File")
    print("=" * 40)
    
    sample_resume = """John Doe
Email: <EMAIL>
Phone: (*************

SUMMARY
Experienced software engineer with 5+ years of expertise in full-stack development.

EDUCATION
Bachelor of Science in Computer Science
University of Technology, 2018-2022
GPA: 3.8/4.0

EXPERIENCE
Senior Software Engineer
Tech Corp Inc., 2022-Present
• Developed scalable web applications
• Led a team of 5 developers

Software Engineer
StartupXYZ, 2020-2022
• Built REST APIs using Python and Django
• Implemented CI/CD pipelines

SKILLS
Programming Languages: Python, JavaScript, Java
Frameworks: Django, React, Spring Boot
Databases: PostgreSQL, MongoDB
Tools: Docker, Kubernetes, Git

PROJECTS
E-commerce Platform
• Built a full-stack e-commerce application
• Technologies: React, Node.js, PostgreSQL

CERTIFICATIONS
AWS Certified Solutions Architect
Issued: January 2023

ACHIEVEMENTS
• Employee of the Month - March 2023
• Led successful migration to microservices architecture

LANGUAGES
English: Native
Spanish: Conversational
"""
    
    filename = "sample_resume.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sample_resume)
    
    print(f"✅ Created sample resume: {filename}")
    print(f"📝 Content length: {len(sample_resume)} characters")
    return filename

if __name__ == "__main__":
    print("🚀 API Endpoint Testing")
    print("Testing the new /section3 regex-based extraction endpoint")
    print()
    
    # Test 1: Check if API is running
    test_root_endpoint()
    
    # Test 2: Test section3 endpoint without file
    test_section3_without_file()
    
    # Test 3: Create sample file for future testing
    sample_file = create_sample_text_file()
    
    print("\n" + "=" * 60)
    print("🎉 Basic API testing completed!")
    print("\n📝 Summary:")
    print("✅ Verified API is running")
    print("✅ Confirmed /section3 endpoint exists")
    print("✅ Created sample resume file for testing")
    
    print("\n🎯 Next Steps:")
    print("1. Convert sample_resume.txt to PDF for proper testing")
    print("2. Test /section3 endpoint with actual file upload")
    print("3. Compare results with /section and /section2 endpoints")
    print("4. Verify regex extraction performance")
    
    print(f"\n📄 Sample file created: {sample_file}")
    print("You can convert this to PDF using online tools or Word for testing.")
