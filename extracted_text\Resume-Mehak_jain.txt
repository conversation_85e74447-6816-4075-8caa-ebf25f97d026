# Extracted Text Debug File
# Source File: Resume-Mehak jain.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:43:11
# Text Length: 2855 characters
# ================================================

<PERSON><PERSON>
LinkedIn Github HackerRank Leetcode
PROFESSIONAL SUMMARY
Dynamic and self-motivated individual with a strong foundation in HTML, CSS, JavaScript, React.js, Node.js, Express.js, and
MySQL. Eager to leverage skills and collaborate within a professional team environment to drive impactful results.
EDUCATION
Bachelor of Technology (Articial intelligence) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 7.48/10.0
Intermediate (Class XII) 2022 - 2023
Growell School (CBSE) Grade: 88.3%
Matriculation (Class X) 2021 - 2022
Growell School (CBSE) Grade: 94.83%
INTERNSHIPS
CONTRIBUTOR May 2024 - September 2024
Google (GSOC'24 at Jupyter Lab) Remote
Techstack : React.Js, Typescript
Key contributor to <PERSON><PERSON><PERSON> Toolkit in Google Summer of Code 2024.
GSOC COMPLETION CERTIFICATE
Merged three critical pull requests to standardize UI elements across JupyterLab:
{Replaced all search inputs with the toolkit's standardized search input.
{Applying a consistent tree view across table of contents, debugger, and running tabs.
{Integrated a consistent icon component into the toolkit.
Enhanced settings editor with advanced widgets (CustomCheckboxWidget, SelectWidget).
Strengthened debugging skills and codebase navigation
PROJECTS
3D Art Gallery , ( Github ) ( Demo ) September 2024
Tech Stack : HTML, CSS, JavaScript, React.js, Node.js, Express, and MySQL
Description: 3D Art Gallery is a full-stack e-commerce platform allowing users to explore and purchase artwork in a 3D
virtual space. Artists can easily upload and manage their art for direct sale.
Features:
{3D Gallery Exploration: Realistic, interactive 3D browsing for an immersive experience.
{E-commerce Functions: Add-to-cart, direct purchasing.
{Artist Management: Easy upload and management of artwork for artists.
MyPortfolio , ( Github ) ( Demo ) May 2024
TechStack: HTML,CSS and Javascript
Description: Explore my portfolio to uncover my skills, experiences, and projects that highlight my capabilities and
passion for innovation.
Features:
{Built responsive navigation with a hamburger menu, smooth scroll, and clickable social links.
{Enhanced UX with a modern design, clean layout, and optimised asset loading.
SKILLS
Computer Languages: TypeScript, Java, Python, JavaScript, CSS, HTML
Software Packages: React, MySQL, Express JS, Prisma ORM, Tailwind, Bootstrap, NodeJS
EXTRA-CURRICULAR ACTIVITIES
Won the StealthFire Hackathon with CampusLink, an innovative project connecting aspirants and college students.
Led the Fine Art Club, coordinating creative initiatives within the college community.
Participated in the Level 1 E-Commerce
Tech Quiz of the Flipkart GRiD 6.0 - Software Development Track organised by the Flipkart .
Participated in a university-organized 100km walkathon, showcasing endurance and commitment to campus activities.
