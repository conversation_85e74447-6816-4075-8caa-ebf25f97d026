"""
Demo script for section extraction functionality.

This script demonstrates how the section extraction works by creating
sample resume text and testing both extraction methods.
"""

import os
import time
from main import extract_single_section, extract_all_sections_single_call, save_section_extraction

def create_sample_resume_text():
    """Create a sample resume text for testing."""
    return """
<PERSON>
Software Engineer
Email: <EMAIL>
Phone: (*************
Location: San Francisco, CA

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years of expertise in full-stack development, 
machine learning, and cloud technologies. Proven track record of delivering scalable 
solutions and leading cross-functional teams.

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley
2016-2020
GPA: 3.8/4.0
Relevant Coursework: Data Structures, Algorithms, Machine Learning, Database Systems

WORK EXPERIENCE
Senior Software Engineer
TechCorp Inc., San Francisco, CA
January 2022 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented machine learning models for recommendation systems
• Mentored junior developers and conducted code reviews
• Technologies: Python, React, AWS, Docker, Kubernetes

Software Engineer
StartupXYZ, Palo Alto, CA
June 2020 - December 2021
• Developed full-stack web applications using React and Node.js
• Built RESTful APIs and integrated third-party services
• Optimized database queries resulting in 40% performance improvement
• Technologies: JavaScript, Node.js, PostgreSQL, Redis

TECHNICAL SKILLS
Programming Languages: Python, JavaScript, Java, C++, SQL
Frameworks: React, Node.js, Django, Flask, Express.js
Databases: PostgreSQL, MySQL, MongoDB, Redis
Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, Git
Machine Learning: TensorFlow, PyTorch, scikit-learn, pandas

PROJECTS
E-commerce Platform
• Built a full-stack e-commerce platform with React frontend and Node.js backend
• Implemented payment processing, inventory management, and user authentication
• Deployed on AWS with auto-scaling capabilities
• Technologies: React, Node.js, PostgreSQL, AWS, Stripe API

Movie Recommendation System
• Developed a machine learning-based movie recommendation system
• Used collaborative filtering and content-based filtering algorithms
• Achieved 85% accuracy in user preference prediction
• Technologies: Python, TensorFlow, pandas, Flask

CERTIFICATIONS
AWS Certified Solutions Architect - Associate (2023)
Google Cloud Professional Data Engineer (2022)
Certified Kubernetes Administrator (CKA) (2021)

ACHIEVEMENTS
• Winner of TechCorp Hackathon 2023 for AI-powered customer service bot
• Published research paper on "Scalable Machine Learning Systems" in IEEE Conference
• Speaker at PyData Conference 2022 on "Building ML Pipelines"
• Dean's List for 4 consecutive semesters at UC Berkeley

LANGUAGES
English: Native
Spanish: Conversational
Mandarin: Basic
"""

def demo_multiple_calls_extraction():
    """Demo the multiple calls extraction method."""
    print("🔍 Testing Multiple Calls Extraction Method")
    print("=" * 60)
    
    sample_text = create_sample_resume_text()
    sections = {}
    confidence_scores = {}
    total_calls = 0
    
    section_names = [
        "summary", "education", "experience", "skills", 
        "projects", "certifications", "achievements", "languages"
    ]
    
    start_time = time.time()
    
    for section_name in section_names:
        print(f"Extracting {section_name}...")
        try:
            content, confidence = extract_single_section(sample_text, section_name, "demo_resume.txt")
            sections[section_name] = content
            confidence_scores[section_name] = confidence
            total_calls += 1
            
            # Show preview
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"   ✅ {section_name}: {confidence:.2f} confidence - {preview}")
            
        except Exception as e:
            print(f"   ❌ {section_name}: Error - {str(e)}")
            sections[section_name] = f"ERROR: {str(e)}"
            confidence_scores[section_name] = 0.0
    
    processing_time = time.time() - start_time
    
    # Calculate overall confidence
    valid_confidences = [score for score in confidence_scores.values() if score > 0]
    overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0
    
    # Save results
    stats = {
        "processing_time": processing_time,
        "total_calls": total_calls,
        "overall_confidence": overall_confidence,
        "text_length": len(sample_text),
        "sections_found": len([s for s in sections.values() if s and not s.startswith("ERROR:") and s != "NOT_FOUND"])
    }
    
    save_path = save_section_extraction(sections, "demo_resume_multiple.txt", "multiple_calls", stats)
    
    print(f"\n📊 Results Summary:")
    print(f"   ⏱️  Processing time: {processing_time:.2f}s")
    print(f"   🔢 Total calls: {total_calls}")
    print(f"   📊 Overall confidence: {overall_confidence:.2f}")
    print(f"   📝 Sections found: {stats['sections_found']}")
    print(f"   💾 Saved to: {save_path}")
    
    return sections, confidence_scores, stats

def demo_single_call_extraction():
    """Demo the single call extraction method."""
    print("\n🔍 Testing Single Call Extraction Method")
    print("=" * 60)
    
    sample_text = create_sample_resume_text()
    
    start_time = time.time()
    
    try:
        sections, confidence_scores = extract_all_sections_single_call(sample_text, "demo_resume.txt")
        processing_time = time.time() - start_time
        
        # Show results
        for section_name, content in sections.items():
            confidence = confidence_scores.get(section_name, 0.0)
            preview = content[:100] + "..." if len(content) > 100 else content
            status = "✅" if content and content.strip() else "❌"
            print(f"   {status} {section_name}: {confidence:.2f} confidence - {preview}")
        
        # Calculate overall confidence
        valid_confidences = [score for score in confidence_scores.values() if score > 0]
        overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0
        
        # Save results
        stats = {
            "processing_time": processing_time,
            "total_calls": 1,
            "overall_confidence": overall_confidence,
            "text_length": len(sample_text),
            "sections_found": len([s for s in sections.values() if s and s.strip()])
        }
        
        save_path = save_section_extraction(sections, "demo_resume_single.txt", "single_call", stats)
        
        print(f"\n📊 Results Summary:")
        print(f"   ⏱️  Processing time: {processing_time:.2f}s")
        print(f"   🔢 Total calls: 1")
        print(f"   📊 Overall confidence: {overall_confidence:.2f}")
        print(f"   📝 Sections found: {stats['sections_found']}")
        print(f"   💾 Saved to: {save_path}")
        
        return sections, confidence_scores, stats
        
    except Exception as e:
        print(f"❌ Error in single call extraction: {e}")
        return {}, {}, {}

def compare_results(multiple_results, single_results):
    """Compare the results from both methods."""
    print("\n📊 Method Comparison")
    print("=" * 60)
    
    multiple_sections, multiple_confidence, multiple_stats = multiple_results
    single_sections, single_confidence, single_stats = single_results
    
    print("Performance Comparison:")
    print(f"   Multiple Calls: {multiple_stats.get('processing_time', 0):.2f}s ({multiple_stats.get('total_calls', 0)} calls)")
    print(f"   Single Call:    {single_stats.get('processing_time', 0):.2f}s ({single_stats.get('total_calls', 0)} calls)")
    
    print(f"\nConfidence Comparison:")
    print(f"   Multiple Calls: {multiple_stats.get('overall_confidence', 0):.2f}")
    print(f"   Single Call:    {single_stats.get('overall_confidence', 0):.2f}")
    
    print(f"\nSections Found:")
    print(f"   Multiple Calls: {multiple_stats.get('sections_found', 0)}")
    print(f"   Single Call:    {single_stats.get('sections_found', 0)}")
    
    # Section-by-section comparison
    print(f"\nSection-by-Section Confidence:")
    section_names = ["summary", "education", "experience", "skills", "projects", "certifications", "achievements", "languages"]
    
    for section in section_names:
        multi_conf = multiple_confidence.get(section, 0.0)
        single_conf = single_confidence.get(section, 0.0)
        print(f"   {section.capitalize():12}: Multiple={multi_conf:.2f}, Single={single_conf:.2f}")

if __name__ == "__main__":
    print("🚀 Section Extraction Demo")
    print("This demo tests both extraction methods with sample resume text.")
    print()
    
    # Test multiple calls method
    multiple_results = demo_multiple_calls_extraction()
    
    # Test single call method
    single_results = demo_single_call_extraction()
    
    # Compare results
    compare_results(multiple_results, single_results)
    
    print("\n✨ Demo completed!")
    print("Check the 'resume sections extracted' folder for detailed results.")
    print("You can now test with real resume files using the API endpoints:")
    print("   - POST /section (multiple calls)")
    print("   - POST /section2 (single call)")
