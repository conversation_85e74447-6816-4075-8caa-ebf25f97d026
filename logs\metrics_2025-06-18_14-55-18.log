{"event": "session_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "timestamp": "2025-06-18T14:55:18.699390", "message": "New API session started"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "34c1f8d9-22b1-4c6e-b1b6-44bd777687c4", "endpoint": "/", "timestamp": "2025-06-18T14:55:28.757291", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "34c1f8d9-22b1-4c6e-b1b6-44bd777687c4", "endpoint": "/", "timestamp": "2025-06-18T14:55:28.759734", "total_time_seconds": 0.0024421215057373047, "status_code": 200, "message": "Request completed in 0.0024s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fe2b123e-c694-4b43-b930-275272d11383", "endpoint": "/docs", "timestamp": "2025-06-18T14:55:57.618954", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fe2b123e-c694-4b43-b930-275272d11383", "endpoint": "/docs", "timestamp": "2025-06-18T14:55:57.619959", "total_time_seconds": 0.001004934310913086, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "34293e74-4b24-4b2e-befe-e28cfba3caf4", "endpoint": "/openapi.json", "timestamp": "2025-06-18T14:55:57.756625", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "34293e74-4b24-4b2e-befe-e28cfba3caf4", "endpoint": "/openapi.json", "timestamp": "2025-06-18T14:55:57.778153", "total_time_seconds": 0.021528244018554688, "status_code": 200, "message": "Request completed in 0.0215s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.448015", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.476012", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.476012", "file_size_bytes": 73336, "message": "Custom metric: file_size_bytes=73336"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.477012", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.477012", "extracted_text_length": 2822, "message": "Custom metric: extracted_text_length=2822"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:12.477012", "file_processing_time": 0.022997379302978516, "message": "Custom metric: file_processing_time=0.022997379302978516"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "a084968f-1757-4799-a45f-75bc5102836d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:56:36.680946", "total_time_seconds": 24.232930421829224, "status_code": 200, "message": "Request completed in 24.2329s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.317458", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.364741", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.365744", "file_size_bytes": 96268, "message": "Custom metric: file_size_bytes=96268"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.365744", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.365744", "extracted_text_length": 4893, "message": "Custom metric: extracted_text_length=4893"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:57:46.365744", "file_processing_time": 0.026996850967407227, "message": "Custom metric: file_processing_time=0.026996850967407227"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dc4a9999-e4ce-4880-8f67-407bd4fc7079", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T14:58:18.684807", "total_time_seconds": 32.36734938621521, "status_code": 200, "message": "Request completed in 32.3673s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.691559", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.711775", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.712780", "file_size_bytes": 81582, "message": "Custom metric: file_size_bytes=81582"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.712780", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.712780", "extracted_text_length": 3080, "message": "Custom metric: extracted_text_length=3080"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:02:56.712780", "file_processing_time": 0.01743793487548828, "message": "Custom metric: file_processing_time=0.01743793487548828"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "4bdd676f-29b1-444b-be91-321408811114", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:11.223636", "total_time_seconds": 14.532076835632324, "status_code": 200, "message": "Request completed in 14.5321s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.266124", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.280996", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.280996", "file_size_bytes": 81007, "message": "Custom metric: file_size_bytes=81007"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.280996", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.280996", "extracted_text_length": 2526, "message": "Custom metric: extracted_text_length=2526"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:13.280996", "file_processing_time": 0.011875391006469727, "message": "Custom metric: file_processing_time=0.011875391006469727"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "fdc3a793-b0b4-4b2a-9cf2-7e4658cc588f", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:40.253541", "total_time_seconds": 26.987416982650757, "status_code": 200, "message": "Request completed in 26.9874s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.309702", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.327703", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.327703", "file_size_bytes": 71498, "message": "Custom metric: file_size_bytes=71498"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.327703", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.327703", "extracted_text_length": 2507, "message": "Custom metric: extracted_text_length=2507"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:42.328704", "file_processing_time": 0.016000986099243164, "message": "Custom metric: file_processing_time=0.016000986099243164"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "930474b9-933f-4500-98f4-672cd01149ba", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:56.838236", "total_time_seconds": 14.528533220291138, "status_code": 200, "message": "Request completed in 14.5285s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.902500", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.923499", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.923499", "file_size_bytes": 72971, "message": "Custom metric: file_size_bytes=72971"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.923499", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.923499", "extracted_text_length": 3176, "message": "Custom metric: extracted_text_length=3176"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:03:58.924499", "file_processing_time": 0.01900005340576172, "message": "Custom metric: file_processing_time=0.01900005340576172"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "f33bfdd9-d991-4ebc-b1d8-9afeeb4762fe", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:12.992807", "total_time_seconds": 14.090306997299194, "status_code": 200, "message": "Request completed in 14.0903s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.091543", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.142055", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.142055", "file_size_bytes": 75014, "message": "Custom metric: file_size_bytes=75014"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.142055", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.143054", "extracted_text_length": 2738, "message": "Custom metric: extracted_text_length=2738"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:15.143054", "file_processing_time": 0.04851388931274414, "message": "Custom metric: file_processing_time=0.04851388931274414"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "90c1c825-b0a8-43c7-90d6-433ef3ee53a0", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:41.907467", "total_time_seconds": 26.815923929214478, "status_code": 200, "message": "Request completed in 26.8159s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.970740", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.992741", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.992741", "file_size_bytes": 72943, "message": "Custom metric: file_size_bytes=72943"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.992741", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.992741", "extracted_text_length": 3091, "message": "Custom metric: extracted_text_length=3091"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:43.992741", "file_processing_time": 0.01999950408935547, "message": "Custom metric: file_processing_time=0.01999950408935547"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "442d3479-59a3-45b3-a2c9-1bd287d72aa2", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:04:58.802463", "total_time_seconds": 14.83172345161438, "status_code": 200, "message": "Request completed in 14.8317s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.875999", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.896001", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.896001", "file_size_bytes": 85006, "message": "Custom metric: file_size_bytes=85006"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.896001", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.896001", "extracted_text_length": 2973, "message": "Custom metric: extracted_text_length=2973"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:00.896001", "file_processing_time": 0.016992807388305664, "message": "Custom metric: file_processing_time=0.016992807388305664"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "14c128b0-fc6d-4aa5-9c72-c80ad28b1af5", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:32.293550", "total_time_seconds": 31.417551279067993, "status_code": 200, "message": "Request completed in 31.4176s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.363680", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.389205", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.389205", "file_size_bytes": 84438, "message": "Custom metric: file_size_bytes=84438"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.389205", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.389205", "extracted_text_length": 2855, "message": "Custom metric: extracted_text_length=2855"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:34.389205", "file_processing_time": 0.0215146541595459, "message": "Custom metric: file_processing_time=0.0215146541595459"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "588f9ef5-33d9-45e4-89b9-4b289dfc9e3d", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:49.795893", "total_time_seconds": 15.432212114334106, "status_code": 200, "message": "Request completed in 15.4322s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.870882", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.888888", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.889889", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.889889", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.889889", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:05:51.889889", "file_processing_time": 0.015004873275756836, "message": "Custom metric: file_processing_time=0.015004873275756836"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "dfc7224b-7751-48f0-8d6b-075757963766", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:05.466512", "total_time_seconds": 13.595629930496216, "status_code": 200, "message": "Request completed in 13.5956s with status 200"}
{"event": "request_start", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.538075", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.561074", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.561074", "file_size_bytes": 80649, "message": "Custom metric: file_size_bytes=80649"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.561074", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.561074", "extracted_text_length": 2535, "message": "Custom metric: extracted_text_length=2535"}
{"event": "custom_metric", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:07.562074", "file_processing_time": 0.02000141143798828, "message": "Custom metric: file_processing_time=0.02000141143798828"}
{"event": "request_complete", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "request_id": "bb159d15-1f55-425c-93ad-e56feaed9d00", "endpoint": "/hybrid_resume", "timestamp": "2025-06-18T15:06:33.499832", "total_time_seconds": 25.961756944656372, "status_code": 200, "message": "Request completed in 25.9618s with status 200"}
{"event": "session_end", "session_id": "dc7054e5-807d-476d-a2a7-d6c936579781", "timestamp": "2025-06-18T15:13:40.978056", "message": "API session ended"}
