# Extracted Text Debug File
# Source File: Resume-<PERSON><PERSON>.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 15:04:43
# Text Length: 3091 characters
# ================================================

<PERSON>hu <PERSON>ni
LinkedIn Github HackerRank CodeChef Codeforces Leetcode Personal Portfolio
PROFESSIONAL SUMMARY
Enthusiastic and innovative Frontend Developer with a passion for clean and ecient code, eager to contribute to dynamic
teams and make a meaningful impact in the eld of web development.
EDUCATION
Bachelor of Technology (Articial intelligence) 2023 - 2027
Newton school of Technology, Rishihood University Grade: 8.3/10.0
Intermediate (Class XII) 2021 - 2022
DELHI PUBLIC SCHOOL, HISAR Grade: 77.0%
Matriculation (Class X) 2019 - 2020
DELHI PUBLIC SCHOOL, HISAR Grade: 82.0%
PROJECTS
Theta: The Learning App , ( Github ) ( Demo ) October 2024
Tech Staks: MongoDB, React, Node, express, JavaScript, css
Description: Theta is a cutting-edge E-learning platform designed to provide personalized, engaging educational content.
Tailored for diverse learners, Theta makes learning accessible and interactive anytime, anywhere.
Features:
As an admin user can perform CRUD operations to create Courses, Lectures and manage costs of it.
As a buyer user can buy course to watch lectures and learn.
As a superadmin user can manage roles and permissions of admin and user.
Razorpay APIs also have been integrated.
Used react-router-dom for routing and pagination for interactive and immersive user experience and can create and log
into their respective accounts securely.
The Souled Store Website Clone , ( Github ) ( Demo ) July 2024
Tech Stack: React, CSS, JavaScript, Tailwind, HTML, Bootstrap, Figma.
Description: Developed a replica of The Souled Store using React, demonstrating front-end prociency and ensuring a
seamless user experience across various devices.
Features:
Implemented modular React components for ecient UI management.
Managed application state eectively across components.
Set up routing for seamless navigation between pages.
Utilized Context API for global state management.
Ensured a fully responsive, mobile-rst design.
Art Gallery Website , ( Github ) ( Demo ) January 2024
Tech Stack: CSS, JavaScript, Tailwind, HTML, Bootstrap.
Description: Developed a dynamic online Art Gallery platform connecting art enthusiasts, collectors, and creators
worldwide, oering an engaging and user-friendly experience.
Features:
Curated a diverse collection of artworks from both established and emerging artists across various mediums.
Enabled users to discover, buy, and sell art in a seamless marketplace.
Fostered a vibrant community for expanding collections and showcasing unique creations.
SKILLS
Computer Languages: C++, Python, JavaScript, CSS, HTML, Java
Software Packages: React, Express JS, Prisma ORM, NodeJS
Additional Courses: Data Structure
EXTRA-CURRICULAR ACTIVITIES
Contributed to shaping an innovative and impactful event as a core team member of Neutron Fest, India's pioneering
new-age AI Techno Cultural Fest.
Serving as the Secretary of Google Developer Group at Rishihood University.
Held the position of Chief of the Maratha Clan within the Rishihood Community, managing responsibilities with honor
and leadership.
