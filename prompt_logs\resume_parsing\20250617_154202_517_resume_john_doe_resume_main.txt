================================================================================
LLM CALL LOG - 2025-06-17 15:42:02
================================================================================

[CALL INFORMATION]
Endpoint: /resume
Context: john_doe_resume.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2025-06-17T15:42:02.517583
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 15.67,
  "has_image": false,
  "prompt_length": 361,
  "response_length": 426,
  "eval_count": 456,
  "prompt_eval_count": 123
}

[PROMPT]
Length: 361 characters
----------------------------------------
You are an expert resume parser. Extract the following information from this resume:

Name: John Doe
Email: <EMAIL>
Phone: ******-123-4567
Experience: Software Engineer at Tech Corp (2020-2023)
Skills: Python, JavaScript, React, Node.js
Education: B.S. Computer Science, University of Tech (2016-2020)

Please return the information in JSON format.
----------------------------------------

[RESPONSE]
Length: 426 characters
----------------------------------------
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "experience": [
    {
      "company_name": "Tech Corp",
      "role": "Software Engineer",
      "duration": "2020-2023"
    }
  ],
  "skills": ["Python", "JavaScript", "React", "Node.js"],
  "education": [
    {
      "degree": "B.S. Computer Science",
      "institution": "University of Tech",
      "year": "2016-2020"
    }
  ]
}
----------------------------------------

================================================================================