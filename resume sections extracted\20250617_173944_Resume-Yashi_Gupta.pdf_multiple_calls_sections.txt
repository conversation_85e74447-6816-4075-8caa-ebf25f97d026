================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: Resume-Yashi Gupta.pdf
Extraction Method: multiple_calls
Timestamp: 2025-06-17T17:39:44.767460
Total Sections Extracted: 8
Processing Time: 14.063202381134033 seconds
Total LLM Calls: 8
Overall Confidence: 0.9571428571428572
================================================================================

[SUMMARY]
----------------------------------------
Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ready to enhance user experiences in a team environment.

========================================

[EDUCATION]
----------------------------------------
Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 8.57/10.0

========================================

[EXPERIENCE]
----------------------------------------
WORK EXPERIENCE
Frontend Intern June 2024 - August 2024
IIT Roorkee with Social Studies Foundation Remote
Tech Stack: NextJS, Tailwind CSS
Description: Developed in collaboration between IIT Roorkee and the Social Studies Foundation, this project curates and
displays government schemes, job openings, and scholarships for the SC/ST community.
Contributions: Designed lter tabs with state-wise and department-wise options, integrated APIs for categorized schemes
(scholarships, job openings, general schemes), and built responsive components like detailed scheme cards and homepage
elements for improved user experience.

========================================

[SKILLS]
----------------------------------------
Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python
Software Packages: React, MySQL, Express JS, NodeJS, Prisma ORM, Tailwind, Hadoop HDFS
Soft Skills: Presentation Skills, Teamwork, Time management

========================================

[PROJECTS]
----------------------------------------
Expedition- Backend Python , ( Github ) ( Demo ) December 2024
Tech Stack: Python
Description: Backend in Python for a ticket booking system, Expedition.
Features: Comprehensive CRUD APIs for managing data, secure user authentication, and ecient data handling for
smooth ticket booking and selling system.
iPhone 15 Pro Website , ( Github ) ( Demo ) December 2024
Tech Stack: ReactJS, ThreeJS, GSAP
Description: iPhone 15 Pro website replica with exceptional design and functionality.
Features: Developed a precise iPhone 15 Pro website replica featuring an advanced video carousel and enhanced user
experience.
FashMore-ECommerce-Project , ( Github ) ( Demo ) September 2024
Tech Stack: React, Firebase
Description: Developed a modern e-commerce platform delivering the latest in fashion trends.
Features: Seamless browsing with organized categories, secure authentication, advanced search, ltering, and ecient cart
management.

========================================

[CERTIFICATIONS]
----------------------------------------
NOT_FOUND

========================================

[ACHIEVEMENTS]
----------------------------------------
ACHIEVEMENTS
Grade: 8.57/10.0
Grade: 94.26%
Grade: 93.4%

========================================

[LANGUAGES]
----------------------------------------
Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python

========================================

