"""
Demo script to showcase the prompt logging functionality.

This script demonstrates how the prompt logging system works by making
sample LLM calls and showing the generated log files.
"""

import os
import time
from datetime import datetime
from prompt_logger import log_llm_call, get_log_stats, cleanup_old_logs

def demo_prompt_logging():
    """Demonstrate the prompt logging functionality."""
    print("🚀 Prompt Logging System Demo")
    print("=" * 50)
    
    # Demo 1: Basic logging
    print("\n1. Basic LLM Call Logging")
    print("-" * 30)
    
    sample_prompt = """You are an expert resume parser. Extract the following information from this resume:

Name: <PERSON>
Email: <EMAIL>
Phone: ******-123-4567
Experience: Software Engineer at Tech Corp (2020-2023)
Skills: Python, JavaScript, React, Node.js
Education: B.S. Computer Science, University of Tech (2016-2020)

Please return the information in JSON format."""
    
    sample_response = """{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "experience": [
    {
      "company_name": "Tech Corp",
      "role": "Software Engineer",
      "duration": "2020-2023"
    }
  ],
  "skills": ["Python", "JavaScript", "React", "Node.js"],
  "education": [
    {
      "degree": "B.S. Computer Science",
      "institution": "University of Tech",
      "year": "2016-2020"
    }
  ]
}"""
    
    log_path = log_llm_call(
        prompt=sample_prompt,
        response=sample_response,
        model_name="gemma3:4b",
        endpoint="/resume",
        context="john_doe_resume.pdf",
        call_type="main",
        metadata={
            "timeout_seconds": 120,
            "max_tokens": 1000,
            "processing_time": 15.67,
            "has_image": False,
            "prompt_length": len(sample_prompt),
            "response_length": len(sample_response),
            "eval_count": 456,
            "prompt_eval_count": 123
        }
    )
    
    print(f"✅ Resume parsing log created: {log_path}")
    
    # Demo 2: Error logging
    print("\n2. Error Logging")
    print("-" * 30)
    
    error_log_path = log_llm_call(
        prompt="Parse this corrupted resume data...",
        response="",
        model_name="gemma3:4b",
        endpoint="/resume",
        context="corrupted_resume.pdf",
        call_type="main",
        metadata={
            "timeout_seconds": 120,
            "max_tokens": 1000,
            "processing_time": 0
        },
        error="Timeout after 120s - file appears to be corrupted"
    )
    
    print(f"✅ Error log created: {error_log_path}")
    
    # Demo 3: JSON repair logging
    print("\n3. JSON Repair Logging")
    print("-" * 30)
    
    repair_prompt = """The following JSON is malformed. Please fix it and return valid JSON:

{
  "name": "Jane Smith",
  "email": "<EMAIL>"
  "skills": ["Python", "Machine Learning"]
  "experience": [
    {
      "company": "AI Corp"
      "role": "Data Scientist",
    }
  ]
}"""
    
    repair_response = """{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "skills": ["Python", "Machine Learning"],
  "experience": [
    {
      "company": "AI Corp",
      "role": "Data Scientist"
    }
  ]
}"""
    
    repair_log_path = log_llm_call(
        prompt=repair_prompt,
        response=repair_response,
        model_name="gemma3:4b",
        endpoint="json_repair",
        context="malformed_json",
        call_type="repair",
        metadata={
            "timeout_seconds": 30,
            "max_tokens": 2000,
            "processing_time": 3.45
        }
    )
    
    print(f"✅ JSON repair log created: {repair_log_path}")
    
    # Demo 4: Image processing logging
    print("\n4. Image Processing Logging")
    print("-" * 30)
    
    image_prompt = """This document contains important text. Please extract ALL text content from this image.
Format your response as plain text only, preserving the structure as much as possible."""
    
    image_response = """RESUME
John Smith
Software Developer
Email: <EMAIL>
Phone: (*************

EXPERIENCE
• Software Developer at TechCorp (2021-2023)
• Junior Developer at StartupInc (2020-2021)

SKILLS
• Python, JavaScript, React
• Database: MySQL, PostgreSQL
• Cloud: AWS, Docker"""
    
    image_log_path = log_llm_call(
        prompt=image_prompt,
        response=image_response,
        model_name="gemma3:4b",
        endpoint="image_extraction",
        context="scanned_resume.jpg",
        call_type="image_extract",
        metadata={
            "timeout_seconds": 120,
            "max_tokens": 2000,
            "processing_time": 25.89,
            "has_image": True
        }
    )
    
    print(f"✅ Image processing log created: {image_log_path}")
    
    # Demo 5: Show statistics
    print("\n5. Log Statistics")
    print("-" * 30)
    
    stats = get_log_stats()
    print(f"📊 Total log files: {stats.get('total_files', 0)}")
    print(f"📊 Total log size: {stats.get('total_size', 0)} bytes")
    
    subdirs = stats.get('subdirs', {})
    if subdirs:
        print("📁 Log subdirectories:")
        for subdir, info in subdirs.items():
            print(f"   - {subdir}: {info['files']} files, {info['size']} bytes")
    
    # Demo 6: Show sample log content
    print("\n6. Sample Log Content")
    print("-" * 30)
    
    if log_path and os.path.exists(log_path):
        print(f"📄 Content of {os.path.basename(log_path)}:")
        print("=" * 40)
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Show first 800 characters
            if len(content) > 800:
                print(content[:800] + "\n... (truncated)")
            else:
                print(content)
    
    print("\n" + "=" * 50)
    print("✨ Demo completed! Check the prompt_logs directory for all generated files.")

def show_directory_structure():
    """Show the prompt logs directory structure."""
    print("\n📁 Prompt Logs Directory Structure")
    print("=" * 50)
    
    prompt_logs_dir = "prompt_logs"
    if os.path.exists(prompt_logs_dir):
        for root, dirs, files in os.walk(prompt_logs_dir):
            level = root.replace(prompt_logs_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            
            # Show first few files in each directory
            subindent = ' ' * 2 * (level + 1)
            for file in files[:3]:  # Show max 3 files per directory
                print(f"{subindent}{file}")
            if len(files) > 3:
                print(f"{subindent}... and {len(files) - 3} more files")
    else:
        print("❌ No prompt_logs directory found")

if __name__ == "__main__":
    print("🎯 Prompt Logging System Demonstration")
    print("This demo will create sample log files to show how the system works.")
    print()
    
    # Run the demo
    demo_prompt_logging()
    
    # Show directory structure
    show_directory_structure()
    
    print("\n🎉 Demo completed successfully!")
    print("You can now examine the log files in the prompt_logs directory.")
