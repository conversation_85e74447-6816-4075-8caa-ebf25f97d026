# Extracted Text Debug File
# Source File: Charan.Kosuri.Resume.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-18 13:50:29
# Text Length: 22462 characters
# ================================================

CHARAN	KOSURI																																																																																			Senior	Full	Stack	Developer	Phone:	************	Email:	<EMAIL>	Linked	In	ABOUT	ME			I	am	a	highly	motivated	Java	Full	Stack	Developer	with	10	years	of	experience	in	designing,	developing,	and	implementing	web-based	applications	using	Java	technologies.	Seeking	a	challenging	position	in	a	dynamic	organization	where	I	can	utilize	my	technical	skills	and	knowledge	to	contribute	to	the	success	of	the	team	while	constantly	learning	and	growing	in	my	career.		TECHNICAL	SUMMARY	ª Ability	to	efficiently	translate	algorithms,	ideas	and	concepts	to	software	applications.		ª Expertise	with	Object	Oriented	Design	(OOD),	Object	Oriented	Analysis	(OOA),	Object	Oriented	Modeling	(OOM)	based	on	Unified	Modeling	Language	(UML)	architecture.		ª Good	knowledge	of	reading	and	following	UML	design	documentation	such	as	Database	and	Data	Flow	diagrams.		ª Experience	in	Agile	software	development	process,	Test	Driven	Development	and	Scrum.		ª Experience	in	client	side	designing	and	validations	using	HTML,	CSS,	JavaScript,	AJAX,	Angular11	and	Nodes		ª Utilized	Java	8	features	like	Lambda	expressions	and	Stream	API	for	Bulk	data	operations	on	Collections	which	would	increase	the	performance	of	the	Application.		ª Experience	in	developing	client-side	validations	using	Angular11	and	in	developing	Custom	pipes,	Shadow	DOM.	ª RIA	applications	using	Flex,	HTML5,	CSS,	Ajax,	Dojo	and	Node	JS	ª Have	good	experience	on	NoSQL	database	like	MongoDB.		ª Proficient	in	MVC	architecture	using	spring	framework	/	JSP-Servlet	and	J2EE	Design	Patterns.		ª Expertise	in	Struts	and	Hibernates	Frameworks.		ª Developed	Graphical	User	Interfaces	using	UI	frameworks	Angular	11	and	Webpage's	using	HTML,	CSS	and	JSP's	for	user	interaction.		ª Good	knowledge	on	AWS	cloud	formation	templates	and	configured	SQS	service	through	Java	API	to	send	and	receive	the	information.		ª Expertise	in	client	scripting	language	and	serve	scripting	languages	like	JavaScript,	jQuery,	JSON,	Bootstrap,	Node.js,	AngularJS,	Backbone.js.			ª Experience	in	developing	client-side,	server-side/middle-tier	components	applying	Java/J2EE	design	patterns	and	frameworks:	Servlets,	JSF,	Struts,	Spring,	Web	Services,	EJB,	JMS,	MQ	Services,	Hibernate,	iBatis,	JDBC,	JPA,	Log4j,	Portals,	GWT,	JSP,	Custom	Tag	Library,	POJO's,	JSTL,	AJAX,	JSON,	JavaScript,	XML,	UML,	Log4J	and	Junit.			ª Proficient	in	implementation	of	frameworks	like	Struts,	spring,	JSF,	AJAX	frameworks	(Rich	Faces,	My	Faces)	and	ORM	frameworks	like	Hibernate.			ª Transaction	implementation	(CMP,	BMP,	Message-Driven	Beans),	JMS,	Struts,	Spring,	Swing,	Hibernate,	Java	Beans,	JDBC,	XML,	Web	Services,	JNDI,	Multithreading	etc.	ª Extensive	experience	in	designing	and	monitoring	MongoDB	multi-shard	clusters	with	MMS,	along	with	using	PERL	for	data	processing	with	Excel,	SQL	Server,	and	MySQL.			ª Experience	on	developing	cross	platform	server-side	applications	using	Nod	BackBone.js	and	Angular.js.to	achieve	scalability	in	real-time	web	applications.			ª Automate	report	generation	in	MongoDB	using	JavaScript,	shell	scripting,	java.			ª Experience	in	Web	Service	Technologies:	SOAP,	WSDL,	UDDI,	Apache	Axis,	CXF,	IBM	JAX-WS.			ª Experience	in	architecting	and	implementing	Enterprise	Application	Integration	(EAI)	projects	with	Service	Oriented	Architecture	(SOA),	Web	Services	Integrations,	BPM,	BPEL	and	ESB	using	WebSphere	Process	Server,	ESB,	WebSphere	Integration	Developer,	Data	Power	and	WSRR.			ª Expertise	in	XML	technologies	-XSL,	XSLT,	XML	schemas,	XPath,	XForms,	XSL-FO	and	parsers	like	SAX,	DOM.			ª Expertise	in	developing	SOA	(Service	Oriented	Architecture),	REST	and	J2EE	Web	Services	based	on	SOAP,	WSDL,	UDDI,	JAX-WS,	JAX-RPC	using	tools	like	Apache	Axis.			
ª Strong	experience	with	MongoDB	development	(reports,	schema	design,	map	reduce	functions)	and	migrations	from	SQL	relational	databases.			ª Proven	experience	with	Application	Servers	like	IBMWebSphere8.x/7.x/6.x/5.x/,	Oracle/BEA	WebLogic	8.x,	10.x	JBoss,	Drools	BRMS	and	Tomcat	5.x,6.x,7.		ª Experience	in	writing	Queries,	Stored	Procedures,	Triggers,	Cursors,	Functions	and	Packages	using	TOAD.			ª Experience	with	major	relational	databases	-	Oracle	9i,	10g,	SQL	Server,	DB2,	and	My	SQL.			ª Experienced	in	Implementing	the	application's	using	J2EE	design	patterns	like	Singleton,	DAO	(Data	Access	Object),	RDBMS.			ª Expertise	in	design	with	Use	Case,	Sequence	diagrams,	Activity	diagrams,	Class	diagrams	with	exposure	to	UML,	tools	like	Rational	Rose	and	use	of	Rational	Unified	Process	(RUP).			ª Expertise	in	the	implementation	of	Core	concepts	of	Java,	J2EE	Technologies:	JSP,	Servlets,	JSF,	JSTL,	EJB			ª Experience	with	creating	workflows	and	Mappings,	running	jobs	in	Informatica	9.5/ETL.		ª Experienced	with	UI	components	like	Grid	Controls,	Pie	Charts,	and	Swing	Components,	along	with	expertise	in	logging	tools	(Log4J,	JUnit,	JMeter)	and	software	configuration	management	using	CVS,	SVN,	IBM	Clear	Case,	and	build	tools	like	ANT	and	Maven.	EDUCTION		ª Bachelors	in	Computer	Science	from	Sathyabama	University	in	2014.		TECHNICAL	SKILLS		Languages	Java	(J2SE1.8/1.6/1.5,	J2EE	6/5),	SQL,	PL/SQL,	Typescript.		Technologies	JSP2.1/2.0/1.2,	Servlets2.x,	JavaBeans,	JDBC,	Struts2.x/1.x,	RMI,	EJB3.0/2.1,	Hibernate3.x/2.x,	Spring	3.0,	Spring	4,	SOA,	JMS1.1,	SAX	and	DOM	Parsers,	AJAX,	JAXB2.1/2.0,	JAXP1.x,	LDAP,	Sybase	Power	Designer,	RESTful	and	SOAP	web	services		Web	Technologies	HTML/DHTML,	XHTML1.1,	JavaScript1.x,	XML1.0,	XSL,	XSLT,	CSS,	Bootstrap,	Angular-JS,	React	JS,	Node	JS,	Express	JS.	Web/Application	Servers	Tomcat	7.x/6.x/5.x,	WebLogic10.3/9.2/8.1/7.0,	IBM	WebSphere	8.x/7.x/6.	X, Apache	HTTP	Server,	JBoss.	Design	Patterns	MVC,	Front	Controller,	Session	Facade,	Singleton,	Business	Delegate	and	DAO	patterns	Data	Base	Oracle	11g/10g/9i/8i,	MS	SQL	Server	2005/2000,	MySQL5.1/4.1,	DB2	9.1/8.1/7.2,	Mongo	DB.	Platforms	Windows,	UNIX,	LINUX	Methodologies	Agile	Methodology,	RUP,	Extreme	Programming	Build	Tools	Rational	Rose,	CVS,	PVCS,	Git,	SVN,	VSS,	Clear	Case,	JIRA,	Jenkins,	JUnit,	Testing,	Selenium,	Mockito,	ANT,	Maven,	Gradle,	EC2,	VPC.			WORK	EXPERIENCE		Chase	Bank,	Plano,	Texas																																																																																																																																																				Nov	2023	to	current	Full	Stack	Developer	Responsibilities:		ª Involved	in	all	phases	of	Software	Development	Life	Cycle	including	requirement,	analysis,	design,	development,	testing,	release	of	the	project	and	support.	ª Followed	agile	methodology	and	involved	in	daily	SCRUM	meetings,	sprint	planning,	showcases	and	retrospective	and	used	JIRA	as	a	tracking	tool	for	the	sprints.		ª Integrated	Spring	Boot	Hibernate	and	JPA	framework.		ª Used	Hibernate	Framework	for	object	relational	mapping	and	persistence		ª Converted	a	monolithic	app	to	microservices	architecture	using	Spring	Boot	using	12-factor	app	methodology.	Deployed,	Scaled,	Configured,	wrote	manifest	files	for	various	Microservices	in	PCF.		
ª Developed	REST	controllers	using	Spring	Data	REST	to	serve	the	UI	with	required	JSON	data.		ª Developed	REST	exception	handling	controller	advice	to	respond	to	bad	requests	and	server-side	exceptions.		ª Created	Docker	images	and	ran	the	Docker	containers	in	various	environments.		ª Used	complete	annotation	driven	approach	to	develop	the	application	using	Spring	Boot	and	was	also	involved	in	setting	up	the	spring	bean	profiling.		ª Consumed	SOAP/	JAX-WS	based	web	services	using	Spring	framework	and	used	SOAP	UI	for	testing	these	services.		ª Experienced	in	building	scripts	on	AWS	cloud	for	scheduling	EC2	auto	scaling	load	balancer	with	Python	SDK,	and	in	cloud	automation	using	AWS	Cloud	Formation	Templates,	Chef,	and	Puppet.	ª Hands	on	Java	11	full	stack	with	spring	boot,	Spring	Cloud,	Spring	Security,	Spring	Data	JPA,	Spring	AOP,	and	Hibernate.	ª Implemented	dynamic	JSP	loading	using	AJAX	and	extensively	worked	with	Test	Driven	Development	(TDD)	using	JUnit.	ª Designed	dynamic	and	multi-browser	compatible	pages	using	HTML,	CSS,	JavaScript,	Angular	Material,	Typescript,	Angular	11/10.		ª Designed	and	developed	the	application	using	agile	methodology	and	followed	Test	Driven	Development	(TDD),	Scrum.	ª Developed	restful	Web-services	using	Django	Rest	framework	in	Python.	ª This	role	will	suit	you	if	you	thrive	on	working	in	a	fast-paced	environment	where	your	work	has	a	high	impact.	ª Consumed	SOAP	services	and	parses	XML	to	Java	Objects	ª Experience	in	automating	the	infrastructure	in	AWS	using	web	services.	ª Deployed	spring	boot	application	on	AWS	Elastic	Beanstalk.	ª Used	Spring	Constructor	Dependency	Injection	for	injecting	the	object	dependencies.	ª Designed	and	developed	the	REST	based	Micro	services	using	the	Spring	Boot,	Spring	Data	with	JPA	and	used	Swagger	to	interact	with	API	endpoints	of	the	micro	services.	ª Worked	in	Integration	Systems	Such	as	Apache	Camel	and	Spring	Integration.	ª Automated	the	performance	testing	using	Meter	to	measure	the	performance	with	100+	active	sessions	ª Developed	story/task	by	following	TDD/BDD	and	Pair	programming	concepts.		ª Providing	daily	status	in	scrum	meetings	with	the	client.	Mentored	new	team	members	on	effective	usage	of	the	blend	of	Spring	Boot	/JPA	and	Java.	ª Developed	CI/CD	system	with	Jenkins	on	Kubernetes	environment,	utilized	Kubernetes	and	Docker	for	the	runtime	environment	for	the	CI/CD	system	to	build,	Test	and	Deploy.	ª Performance	tuning	on	JIRA	by	deleting	inactive	workflows,	screens,	and	field	configuration	not	associated	with	any	project.		ª Created	a	fully	Automated	Build	and	Deployment	Platform	and	coordinating	code	builds,	promotions	and	orchestrated	deployments	using	Jenkins	and	GIT.		ª Fixed	the	bugs	in	the	existing	application	by	tracking	in	JIRA.		ª Regularly	meet	with	IAM	teams	to	report	new/pending	issues	and	develop	strategic	roadmaps	for	IAM	systems	and	programs.	ª Having	a	Strong	Experience	in	continuous	integration	to	deploy	changes	using	Jenkins	and	automate	the	tests.	ª Helped	migrating	and	managing	multiple	applications	from	on	premise	to	cloud	using	AWS	services	like	S3,	Glacier,	EC2,	RDS,	SQS,	SNS,	SES,	Cloud	Formation,	VPC	etc.		Environment:	Java8/11,	Spring	Boot	2.x,	Restful,	Spring	Cloud,	Microservices,	RESTful	APIs,	JAX-RS,	JPA,	Hibernate,	Spring	Security,	OAuth	2.0,	JWT,	Apache	Camel,	Kafka,	RabbitMQ,	ActiveMQ,	Kubernetes,	Docker,	Jenkins,		CI/CD,	Terraform,	AWS(Lambda,	EC2,	S3,	RDS,	VPC,	IAM,	Route	53,	EKS,	CloudWatch	),	Python,	Django,	Flask,	Fast	API,		Angular	10/11,	React.js,	TypeScript,	JavaScript,	Node.js,	Express.js,	HTML5,	CSS3,	Bootstrap,	SCSS,	JSP,	Servlets,	SOAP,	WSDL,	GraphQL,	Postman,	Swagger,	OpenAPI,	PostgreSQL,	MySQL,	MongoDB,	Cassandra,	Redis,	ELK	Stack	(Elasticsearch,	Logstash,	Kibana),	Jira,	REST	API,	Kubernetes,	,	Maven,	GIT,	Docker,	,	SOAP,	JPA	and	Micro	services.			
MedStar	Family	Choice,	Columbia,	MD																																																																																																																									Sep	2021	to	Oct	2023	Full	Stack	Java	Developer	Responsibilities:	ª Implemented	Angular7/6	Router	to	enable	navigation	from	one	view	to	the	next	as	the	customer	performs	application	tasks.	ª Used	Spring	Boot	is	radically	faster	in	building	cloud	based	Microservices	with	very	less	configuration.	ª Involved	in	writing	of	multiple	batch	jobs	using	Spring	Batch	framework	where	both	annotation	&	xml-based	configuration	is	done	to	support	various	business	functionalities.	ª Implemented	responsive	UI	rich	web	applications	with	HTML5,	CSS3,	SCSS,	Bootstrap	and	Angular7/6	frameworks.	ª Used	reactive	&	template	driven	forms	to	associate	HTML	elements	to	model	&	provide	dynamic	user	validations	on	client	side.	ª Supported	our	legacy	portal	which	was	implemented	with	Spring	MVC	architecture.		ª Configured	Docker	setup	locally	to	have	a	unique	environment	across	different	layers	of	testing.	ª As	part	of	securing	the	Restful	web	services	we	used	spring	security	filters	along	with	bearer	token	authentication	mechanism	using	JWT	token.	ª Implemented	OAUTH	2	(Rest	service	authentication)	in	our	application	to	communicate	securely	with	other	protected	resources	by	exchanging	access	tokens	for	authentication	instead	of	using	username	and	passwords.	ª Evaluated	merchant	sites	for	PCI	(Payment	Card	Industry)	compliance	and	advised	accordingly,	managed	SSL	(Secure	Sockets	Layer)	certificates.	ª Administered,	monitored	and	maintained	multi	data-center	Cassandra	cluster	using	OpsCenter	and	Nagios	in	production.	ª Used	MongoDB	internal	tools	like	Mongo	Compass,	Mongo	Atlas	Manager	&	Ops	Manager,	Cloud	Manager	etc.	ª Used	AWS	Beanstalk	for	deploying	and	scaling	web	applications	and	services	developed	with	Java.	ª Led	daily	stand-ups	and	scrum	ceremonies	for	two	teams	while	collaborating	with	product	owners	to	groom	the	backlog	and	plan	sprints.	ª Enhance	the	performance	and	capacity	of	the	virtual	system	of	enterprise.	ª Designed	and	developed	Java	APIs	for	consuming	and	executing	AWS	services	like	AWS	Lambda,	EC2,	S3.	ª Work	on	setting	up	CI/CD	pipelines	for	automated	deployments	on	to	multiple	environments	using	AWS,	Jenkins,	and	Shell	Scripts.	ª Monitored	and	fixed	issues	Build’s	for	QA,	DEV,	integration	and	production	are	monitored	by	integration	tool	Jenkins.	ª Used	Active	MQ,	as	message	broker	for	reliable	and	secure	delivery	of	messages	between	applications.	ª Followed	Acceptance	Test	Driven	Development	(TDD)	with	the	help	of	Cucumber	framework	where	we	automated	the	test	execution	of	each	feature	we	developed.	ª Involved	in	writing	test	cases	using	Junit	and	Mockito	framework	and	maintained	a	branch	coverage	above	85%,	which	is	a	mandatory	requirement.	ª Designing	and	developing	Restful	API	and	services	using	best	practices	to	interact	within	the	Micro	services	and	with	the	front	end.	Using	spring	MVC	and	spring	rest.	ª Experienced	in	implementing	Micro	services,	Service	Oriented	Architecture	(SOA)	with	XML	based	Web	Services	(SOAP/WSDL)	using	Top	Down	and	Bottom-Up	approach.	ª Hands-on	experience	in	configuring	Jenkins	build	jobs	for	various	services	to	facilitate	continuous	integration	(CI/CD).	ª Involved	in	using	GitHub,	Jira,	and	Confluence	for	daily	routines,	and	in	configuration	setting	for	Development,	Test,	and	Production	Environment.	ª Generated	swagger	documentation	for	rest	services	and	exposed	it	to	external	teams	by	using	spring	fox	swagger	library.	 Environment:  Java8,	Spring	framework,	Spring	Boot,	Spring	Security,	JWT,	OAuth,	Spring	Batch,	Angular7/6,	Node.js,	Typescript,	JSP,	Servlets,	XML,	XSLT,	Log4j,	AJAX,	Eclipse,	HTML,	CSS,	Web	Services	(SOAP	and	WSDL),	HTML5,	CSS3,	Bootstrap,	Eclipse,	Jenkins,	GitHub,	Tomcat	Oracle,	SOAP	and	Restful,	XML,	XSD,	TDD,	MongoDB,	UNIX,	Windows	XP,	AWS	Services,	Jenkins,	Active	MQ.		
Verizon,	Irving,	TX																																																																																																																																																															Jan	2019	to	Aug	2021	Full	Stack	Developer	Responsibilities:	ª Worked	with	Business	Analysis	teams,	Product	Owner	and	Clients	in	collecting	and	understanding	the	business	requirements	and	worked	with	architects	in	converting	them	to	functional	and	technical	specifications.	ª Involved	in	the	Software	Development	Life	Cycle	(SDLC),	Requirements	gathering,	Design,	Code,	Integration,	and	Deployment	and	production	phases.	ª Built	Restful	web	services	using	Node	JS	and	Express	JS	and	used	a	full	complement	of	Express.	ª Created	Typescript	reusable	components	and	services	to	consume	REST	API's	using	Component-based	architecture	provided	by	Angular	6.	ª Created	and	consumed	RESTful	API	using	Express.js	to	provide	centralized	logic	and	reusable	data	retrieval	build	on	top	of	Node.js.	ª Implemented	Logic	for	pulling	the	data	from	the	REST	API	with	Axios	AJAX	and	issued	HTTP	requests	to	interact	with	REST	endpoints.	ª Involved	in	development	of	Restful	web	services	using	MERN	stack,	MongoDB,	Express	React-Redux	and	Node	JS.	ª Developed	various	single	page	applications	(SPA)	using	React	JS	and	used	various	components	in	the	Redux	library.	ª Used	React-Routers	for	connecting	the	APIs,	which	enforces	communication	with	the	server	while	also	implementing	mock	services	in	Node.js	with	the	help	of	modules	using	Express.js.	ª Developed	dashboard	based	on	Ajax	calls	with	Business	Customers	Count,	Flags,	Real-time	graphs	for	Analytical	Reporting	using	Node	JS.	ª Implemented	JSON	web	tokens	mechanism	for	authentication	and	authorization	security	configurations	using	Node.js.	ª Used	React	JS	virtual	DOM	for	client-side	view	rendering	services,	React-Redux	for	state	management	and	React-Router	for	client-side	routing	for	dynamically	changing	client-side	content.	ª Implemented	asynchronous	logic	for	CRUD	operations	in	MongoDB	using	mongoose	connector,	and	assertions.	ª Created	Angular	HTTP	Client	interceptors	to	add	necessary	JWT	tokens	for	the	outgoing	requests	from	UI.	ª Closely	worked	with	the	Kafka	Admin	team	to	set	up	Kafka	cluster	setup	on	the	QA	and	Production	environments.	ª Developed	and	implemented	unit	test	cases	and	integration	test	cases	using	Chai	and	Mocha,	and	used	Selenium	for	creating	a	web-based	test	automation	suite	to	perform	functional	testing.	ª We	hosted	all	our	microservices	in	AWS	cloud	infrastructure	using	Jenkins	CI/CD	pipelines.	ª Used	Pivotal	Cloud	Foundry	to	quickly	build,	deploy	and	update	the	applications	and	managed	Pivotal	Cloud	Foundry	based	platform	built	on	Kubernetes	programming	with	deployment	manager	and	containerized	using	Docker.	 Environment:	Java,	HTML5,	CSS3,	Typescript,	REACT	JS,	REDUX	Chai,	Angular,	Mocha,	Git,	NPM,	YARGS	Grunt,	Bower,	Spring	Security	(JWT),	MongoDB,	Kafka,	Node.js,	Express,	Jetty	Server,	AEM,	Kubernetes,	Docker,	AWS,	EC2,	Jenkins,	Splunk,	Pivotal	Cloud	Foundry,	Selenium.		Elegant	Microweb,	Hyd,	Ind																																																																																																																																													Mar	2016	to	Nov	2018		Java	Developer											Responsibilities:	ª Implemented	Agile,	and	Scrum	methodologies	actively	involved	in	technical	meetings,	requirements	gathering,	analysis,	planning,	effort	estimations,	development,	and	testing.		ª Followed	Agile	methodology	and	used	Test	Driven	Development.	Design	Docs	Created	from	Functional	Specifications		ª Expertise	in	the	implementation	of	Core	concepts	of	Java,	J2EE	Technologies:	JSP,	Servlets,	Spring,	Hibernate,	Java	Beans,	JDBC,	XML,	Web	Services	ª Experienced	in	using	Agile	Project	Management	tools	like	Azure	Boards	for	agile	planning,	tracking,	and	management.		ª Setup	Alerting	and	monitoring	using	Stack	driver	in	GCP	ª Reviewed	High-level	Design	(HLD),	Functional	requirements	and	discussed	the	new	functionality	in	team	meetings.		ª Extensively	worked	on	Java	8,	Microservices,	Sprint	Boot,	and	Pivotal	Cloud	Foundry	(PCP)	cloud	structure.	ª Responsible	for	maintaining	and	expanding	our	AWS	infrastructure	using	AWS	EC2,	managing	documents	in	S3,	and	providing	IAM	access	to	S3	buckets.	ª Good	knowledge	of	using	Splunk	to	identify	the	failures	and	errors	in	the	applications	ª Involved	in	designing,	developing,	and	testing	the	web	application	by	using	the	HTML5,	CSS3,	Bootstrap,	and	React	JS.	
ª Involved	in	developing	REST	API	which	produces	and	consumes	Web	services	from	other	APIs.	Used	Hibernate	and	Spring	JPA	to	persist	data	into	the	database.		ª Developed	Class	and	Sequence	Diagrams	using	Rational	Rose	Software	and	used	Maven	for	building	and	deploying	applications.	ª Experienced	in	using	DevOps	tools	like	Azure	DevOps	for	continuous	integration,	deployment	and	creating	pipeline	ª Developed	and	implemented	a	Linux	Shell	script	that	retrieves	the	metadata	of	all	the	hive	tables	in	a	database.		ª Used	JERSEY	framework	to	implement	RESTful	web	services.		ª Used	JAX-RS	to	support	the	creation	of	XML	and	JSON	via	the	Java	Architecture	for	XML	Binding	(JAXB).	Prepared	the	Technical	Design	Documents.		ª Implemented	Spring	Validation	API	for	writing	and	integrating	the	validation	rules.		ª Used	Hibernate	ORM	framework	as	persistence	engine,	configured	O/R	mapping	and	wrote	hibernate	queries.		Environment:	Java	1.8,	Spring	2.5,	Node	js	Hibernate,	Spring	JPA,	Java	Web	Services	(REST,	JAX-WS),	EJB	3.0,	jQuery,	Spring	Boot,	JavaScript,	Bootstrap,	Web	services,	AWS,	My	Eclipse	10,	PostgreSQL,	Tomcat	8,	Splunk,	Java	Beans,	Maven,	XML,	XHTML,	JavaScript,	CSS,	XSL,	DB2,	SQL,	Log4j,	UNIX	Shell	Script.			OG	Software	Solutions,	Chennai,	Ind																																																																																																																														Oct	2014	to	Feb	2016	Java	Developer		Responsibilities:	ª Followed	agile	methodology	(Scrum)	to	meet	customer	expectations	and	timelines	with	quality	deliverables.	ª Participate	in	sprint	planning	meetings,	user	stories	grooming	and	scrum	meetings.	ª Translate	customer	requirements	into	formal	requirements	and	design	documents,	establish	specific	solutions	and	leading	the	efforts	including	programming	and	testing	culminate	in	client	acceptance	of	the	results.	ª Experience	working	with	java	application	servers	like	Tomcat,	JBoss	fuse	WebSphere	and	tools	Eclipse,	Maven,	Jenkins	ª Jobs	Application	server	implementation,	support,	workload	management	and	performance	tuning	include	Web	server	setup	configuration	with	jobs	App	Server.		ª Deployed	Application	instance	on	Jobs	and	WebLogic	Server.	ª Developed	the	Unix	Search	Utility	Tool	using	SSH2	implementation	Gschu	Java	Secured	Channel	with	Spring	MVC	and	Apache	ª Developed	a	core	routing	framework	for	Verizon	National	Preorder	using	Apache	Camel	ª Developed	application	using	Spring	Boot	Starter	by	exploring	the	required	services.	ª Involved	in	the	preparation	of	test	cases	for	regression	and	integrated	testing.	ª Developed	Unit	test	cases	using	Junit	Mockito	with	the	coverage	of	90%.	ª Root	cause	analysis	of	any	incident	reported	on	Process	Monitor	with	quick	turnaround	resolution	approach.	ª Establish	a	quality	procedure	for	the	team	continuously	monitoring	and	auditing	to	ensure	the	team	meets	quality	goals.	ª Implemented	logging	and	monitoring	to	proactively	detect	and	resolve	issues.		Environment:	Java,	Jobs,	Spring	Boot	(Moneta	Boot),	Mantis,	Spring3,	Restful	WS	(Jersey),	Angular	JS,	Apache	Tomcat,	Maven,	Eclipse	Indigo,	GIT	SCM,	SQL	DB,	Jenkins,	Crucible.		
