# Extracted Text Debug File
# Source File: Akhil - Java.docx
# Context: resume
# Extraction Method: docx_text
# Timestamp: 2025-06-09 14:22:24
# Text Length: 35665 characters
# ================================================

Name: Akhil P
SR Java Full Stack Developer
Gmail:  
Phone: 613 – 434 – 6131.
LinkedIn: 
PROFESSIONAL SUMMARY:
Having 9+ years of overall experience in the Banking domains and Healthcare domain and all phases of the Software Development Life Cycle including Software Design, Analysis, Coding, Development, Testing, Implementation, Maintenance, and Support. Using technologies related to Java, J2EE, and XML. Reporting, Databases (Oracle, SQL Servers), Web-related technologies, tools, and testing tools and strengths include the following. 
Technical experience in the areas of Object-Oriented Analysis (OOA), Object Oriented Design (OOD) using UML, Software Life Cycle Management, and Development of Distributed applications.
Experience in Server technologies such as Spring Framework, Spring Boot, Spring Batch, Spring MVC, Hibernate, AngularJS, Node JS, RESTFUL, SQL, SOAP, Web Services, and Apache.
Experience in client technologies using JavaScript, HTML5, XML, and CSS3. Experience in XML technologies such as DTD, XSL, XSLT, XML, Schemas, XPath, and DOM. 
Expertise in designing and implementing the Sail Point connectors like Active Directory, LDAP, JDBC, SQL Server, Sybase, DB2 and Linux.
Expertise with Relational databases such Oracle, PostgreSQL, SQL server and MYSQL, PL/SQL.
Extensive experience in programming, deploying, configuring, fine-tuning, and administering middle-tier popular J2EE Application Servers like IBM WebSphere, BEA WebLogic, JBOSS Apache Tomcat
Used Streams and Lambda expressions available as part of Java 8/11 to store and process the data.
Fluent with the core Java concepts like Multi-Threading, Exceptions, RegEx, Data Structures, and Serialization, including Java 8 API-like functional programming.
Experience in integrating the internal security model into new projects with Spring Batch, and Spring Micro Services.
Hands-on experience in debugging and troubleshooting existing code. Proficient in using SOAP, WSDL, and RESTful Web Services. Good experience in writing Spark applications using Scala.
Created REST Microservice APIs using Spring Boot Application.
Experience in analysing and designing applications by Object-Oriented Analysis and designing methodology, object-oriented programming paradigm, and complex data structures.
Good knowledge and experience in web applications using JSF, JSP, JSTL, JNDI, JDBC, Kafka, Mongo DB, Java Beans, and JMS.
In depth knowledge in HTML5, CSS3(Less, Sass) frameworks, JavaScript, TypeScript, Angular, React, jQuery, Ember.js, Backbone.js, Ext.js for designing interactive UI pages.
Experience in Microsoft Azure IaaS, PaaS, Provisioning VM's, Virtual Hard disks, Virtual Networks, Deploying Web Apps and creating Web - Jobs, Azure Cosmos DB, Active Directory, Azure Windows server, Microsoft SQL Server, Microsoft Visual Studio, Windows PowerShell, Cloud infrastructure.
Solid understanding of Object-Oriented Programming concepts and Expertise in the design and development of J2EE components and APIs.
Hands-on experience in using message brokers such as ActiveMQ and RabbitMQ.
WebLogic server 10.3.6 installed and configured WebLogic server along with ECM server.
Assist business stakeholders to increase and improve their usage of Adobe Analytics (Omniture/Site Catalyst) and other analytics platforms.
Migrating the code from Hive to Apache Spark and Scala using Spark SQL, RDD.
Involved in performing unit integration and system testing using JUnit, JMock, Easy Mock, TestNG, and Mockito.
Deployment of applications done using tools Maven, Nexus, Gradle, and logging tools like Log4J.
Proficient in core java concepts like Collection Framework, Multi-threading, Generics, Annotations, Serialization, Java Beans, and Externalization. Developed Java server components using Spring, Spring MVC, and Hibernate web services technologies.
Experience in using Hibernate framework for mapping POJO classes with the database and using Hibernate Query Language.
Good knowledge of Hadoop Architecture and various components such as HDFS, Job Tracker, Task Tracker, Name Node, Data Node, Resource Manager, Node Manager, Application Master and Map Reduce concepts.
Experience in using Accumulator variables, Broadcast variables, RDD caching for Spark Streaming.
Setting up AWS Oracle RDS databases for new project, use data pump to migrate data to Relational Database Services (RDS).
Extensively worked on database applications using Amazon RDS, OracleDB, SQL Server, PostgreSQL, Amazon Aurora, DynamoDB, Sqlite and MySQL.
Created APIs in Azure using Azure API management.
Build highly available content distribution sites by using CI/CD tools like Chef, Ansible, Docker, Maven, Jenkins, Jira, Kubernetes etc.
Experienced in using Jasmine, Karma, Gulp, Grunt and NPM to test and build applications.
Use Golang API/SDK such as viper, gorilla/mux, go-ps, couchbase gocb, grpc, quic-go.
Solid experience developing the rendering view React JS components, forms, events, routers, and Redux asynchronous functions and implemented React - Flux pattern for core dependency.
Good knowledge of Docker and Container orchestration.
Created test suites for each service using Azure end-points in Ready API to test the services created in Azure APIM.
Strong analytical skills with the ability to follow project standards and decision-making capability.
Ability to adapt to evolving technology, a strong sense of responsibility, and accomplishment. 
Excellent Leadership, Interpersonal, Problem-solving, and Time Management skills. 
Expertise with Relational databases such Oracle, PostgreSQL, SQL server and MYSQL, PL/SQL.
Developed Applications using Rule Engines, and Drools to validate the business User Roles.
Experience in configuring and deploying web applications using WebLogic, JBoss, Tomcat, and Apache.
Good understanding of Domain Driven Design (DDD), Microservices, Continuous Integration, and Continuous Deployment (CI/CD) using Jenkins and Docker.
Know about Relational Databases such as SQL/PLSQL, MySQL, MS Access, and NoSQL such as MongoDB.
Performance Targeted Implementer with substantial familiarity in Service Oriented Architecture (SOA) using Apache Axis Web Service Framework using the Web Services protocols like SOAP, REST, JAX-RPC, JAXB, WSDL, and UDDI.
Experience in Amazon Web Services (Amazon EC2, Amazon S3, Amazon Simple DB, Amazon RDS, Amazon Elastic Load Balancing, Amazon SQS, AWS Identity, and access).
Excellent Written (documentation) and Verbal (presentation) communication skills.
Hardworking and result-oriented with customer Focus and the ability to persist and explore new ways of finding solutions to the problem.
Hands on experience with Tools like Maven, Ant, SVN, Jenkins, Nexus, GIT, Bitbucket, Bamboo, JIRA.
Knowledgeable of Spark and Scala win framework exploration for transition from Hadoop/Map Reduce to Spark.
Developed rule-based applications using Drools and Java.
Experience working with Gaming, Banking Networks, or Telecom Domains 
Good understanding and experience working with Waterfall and AGILE software development methodologies.
Created a responsive web offer wall that can be integrated with any iOS/Android app via a web view, the project was created with Google Polymer and PHP.
TECHNICAL SKILLS:
PROFESSIONAL EXPERIENCE:
Client: Vanguard, Charlotte, NC							                 Jul 2023 – Till Date
Role: Sr Java Full-stack Developer
Responsibilities:
Involved in Team Meetings, Iteration Planning’s to discuss about the Features and Development Process.
Used Elastic search as the data indexer and query parser to extract information from Couchbase in JSON format to retain Last Search (Auto fill columns with previously searched Data) when the member return to application again 
Involved in writing Couch base queries for DAO layer and Created POJO, Mapping to perform Upset and retrieval of documents.
Designed and developed a decision tree application using Neo4j graph database to model the nodes and relationships for each decision.
Experience integrating ASP.NET MVC and WebAPI based applications with DocuSign REST APIS
Aggressively used Kibana and Splunk monitor the application performance and used the logs to explore error messages and Troubleshooting.
Used OpenShift Environment to Deploy the Application for all Environments.
Created & Migrated the Service accounts from CyberArk to Conjur and Configured the applications with Conjur Service accounts.
Good knowledge and worked on Spark SQL, Spark Core topics such as Resilient Distributed Dataset (RDD) and Data Frames.
Knowledge of Azure resources and Azure DevOps for managing Azure cloud storage.
Working on creating batch jobs using Autosys as the job scheduler and technologies like SQL Invoker, UNIX shell scripting and core java.
Developed Golang code to Feedback system.
Wrote Python code embedded with JSON and XML to produce HTTP GET request, parsing HTML data from websites.
Developed API for using AWS Lambda to manage the servers and run the code in the DB.
Used Hibernate to store the persistence data into the PostgreSQL database and wrote HQL to access the data from the database.
Experience with configuration management tools like Ansible and Packer and automated the cloud deployments using terraform.
Designed and implemented solutions that leveraged OpenSearch for indexing and searching data.
Writing REST based micro services, JPA, AngularJS code using TDD methodology.
Involved in creating new JSR batch, to extract the HTML files from GZ and Store records to Documentum in PDF format and Metadata to SQL DB.
Developing Spark programs using Scala API's to compare the performance of Spark with Hive and SQL.
Involved in design, implement and testing phases of SDLC as project was agile in nature.
Breakdown the feature into Stories and planned the work as per Capacity.
Involved in Developing the Kafka Producer Rest Service with events and Upset query to store the search details to Kafka based on ID.
Part of team implementing REST API's in Python using micro-framework like Flask with SQLAlchemy.
Developed restful Web-services using Django Rest framework in Python.
Experience in migrating data from Oracle database using Spring JDBC to Apache Cassandra (Datastax) NoSQL Database.
Designed and developed the framework to consume the web services hosted in Amazon EC2 instances.
Implemented unit test for testing Angular components with frameworks using KARMA and JASMINE.
Experienced in running Hadoop streaming jobs to process terabytes of xml format data.
Developed the Docker based micro services deployment modules with the Jenkins, Kubernetes, and Ansible based pipelines/frameworks.
Designed and developed the angular application using Angular CLI, TypeScript, HTML5, CSS3, JQuery, Bootstrap.
Implemented classes, interfaces and methods, constructors and objects definitions using TypeScript.
Worked with NoSQL database to store JSON like dynamic connections data.
Created Azure Application Insights for track user activities and geographical count on users logged in to application. Utilized Open Telemetry to customize user information and dashboard display in Application Insights. Created alerts for specific log messages in APPInsights.
Wrote services to store and retrieve user data from the MongoDB for the application on devices.
Configured the Kafka Producer and Kafka Consumer Rest Services with events, N1QL queries to read the data from Kafka and store in Couchbase.
Developed a custom File System plug in for Hadoop so it can access files on Data Platform. This plugin allows Hadoop MapReduce programs, HBase, Pig and Hive to work unmodified and access files directly.
Developed back-end services and APIs using Node.js for fast and scalable applications.
Experience in Creating desktop client applications using UNIX shell scripts and Java applications.
Involved in developing the DAL (data access layer) Service with retrieval query and provided the input validations for the events to avoid SQL Injections.
Involved in designing and creating the Database Tables using PostgreSQL Database.
Implemented Spark using Scala and SparkSQL for faster testing and processing of data.
Implemented Java 8(lambda expressions, Stream API, Concurrency API and J2EE Design patterns like Business Delegate and Data Transfer Object (DTO), Data Access Object and Service Locator.
Managed client requirements and configured Sail Point connectors.
Used RabbitMQ open-source message broker software that implements the Advanced Message Queuing Protocol (AMQP).
Defined and implemented data models suitable for OpenSearch, ensuring efficient data storage and retrieval.
Resolved customer issues by establishing workarounds and solutions and by debugging the defects
Configured AWS Infrastructure-as-Code (IaC) with Terraform for web applications, databases, AWS Lambda.
Create Control-M jobs to schedule the JSR batch to process records in all Environments.
Work closely with Control-M team to monitor the scheduled jobs and logs.
Experience in using Citbox Servers to Test Legacy applications in DEV Environment.
Collaborated with enterprise architect and other team members to define and develop new integration application.
Coordinated with A testers for end-to-end system testing and postproduction testing.
Involved in deployment of application on Jboss Server in Development.
Coordinate with offshore team and share the technical understanding, assigning modules to team members.
Document all the work in wiki for future reference.
Performed advanced procedures like text analytics and processing, using the in-memory computing capabilities of Spark using Scala.
Implemented server-side rendering (SSR) for web applications, improving SEO and user experience.
AWS Lambda was utilized for serverless implementations, where the functions were triggered when the database tables were updated and AWS Cloud Watch was utilized for alarms.
Used JIRA tool for Issue/bug tracking, monitoring of work assignment in the system Log4J was used to monitor the error logs and used GIT as version control tool.  
Drools Expert and browsing capability using Apache Solr with JBOSS.
Deployed the drools rules as a service in J2EE stack environment and maintained multiple existing projects including rules.
Experience and expertise in GCP environment in particular Google Big Query, Google Pub/sub, Google Spanner, Dataflow, Compute Engine, Google Storage.
Experienced on Managing the local deployments in Kubernetes and creating local cluster and deploying application containers.
Experience in building isomorphic applications using React.js and Redux with GraphQL on the server-side.
Worked closely with Couchbase team, to set up the Couchbase bucket to store the Data.
Involved in the development of Couchbase queries, Index and created replicas.
Designed and managed NoSQL databases using AWS DynamoDB for low-latency applications.
Designed and Developed Restful web services using Java, Spring Boot, and Hibernate.
Experience in Data migration from DB2 to Apache Cassandra DB and involved in major and minor up gradation of Couch base and Cassandra cluster.
Used Bootstrap and Angular, React.js and Node.js in effective web design.
Creation of POJO based domain models integrated with Hibernate (Entity, Mapping) to persist the data.
Create Validators for Client interface Validation and Server-side validation using Spring IOC and Spring Annotations.
Create queries dynamically with Criteria Builder, Criteria Query based on Search Criteria.
Create new SQL DB development creation of new tables based on Business requirements.
Expertise in using source code control systems such as GIT, JIRA, and Jenkins for maintaining versions across various releases.
Write well designed SQL queries, index, Stored Procedures, functions, views to perform CURD operations.
Release/Deploy applications to production environment using Red hat Open Shift Environment.
WebLogic server 10.3.6 installed and configured WebLogic server along with ECM server.
Perform Security, integration testing to applications using Postman/Ready API/Soap UI tools before releasing to Production.
Used Hibernate to store the persistence data into the PostgreSQL database and wrote HQL to access the data from the database.
Deployed and managed applications on web servers such as Apache Tomcat, Nginx, or Jetty.
Worked with Application Servers and Web Servers including IBM WebSphere, Oracle WebLogic, JBOSS and Apache Tomcat for developing and deploying.
Created and maintained RESTful APIs that interacted with OpenSearch for data ingestion and retrieval.
Have knowledge of Kotlin Android Extensions framework.
Developing micro services PCF (Pivotal Cloud Foundry) and AWS ECS Fargate applications in cloud environment.
Conducted unit testing of Vue components using Jest and Vue Test Utils, ensuring high code quality and reliability.
Worked on CI/CD tools for deploying a pipeline for automation of testing and deployment process.
Provided Technical support for variously Applications, fixed bugs and added enhancements for existing applications.
Coordinating and managing all the communication between the team and client as well as the deliverables
Environment: Java 11/17/21, Spring, Angular 16/17, Hibernate, Rest Web Service, Couchbase, Apache Kafka, Documentum, MYSQL, SQL, Documentum, NoSQL, Eclipse, IntelliJ, Gitlab, IBM MDM, Amazon Aurora, Jira, React.js, Terraform, GraphQL, PostgreSQL, Kibana, Kotlin, Vue JS, Splunk, PostgreSQL, RabbitMQ, Sail Point, Open Shift, AWS, Soap, Drools, REST, control-m, Qtest, Wiki, Citbox, SDLC, ECM, DocuSign.
Client: Charter Communications, Stamford, CT					                              Aug 2021 – Jun 2023
Role: Java Full-stack Developer
Responsibilities:
Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.
Design and develop the processes using multithreading to make it asynchronous.
Jersey implementation of JAX-RS is utilized to develop RESTful service.
Developed a RESTful service to provide all the CRUD capabilities.
Created and injected Spring services, Spring controllers, and DAOs to achieve dependency injection and to wire objects of business classes.
Used Go Programming Language (GoLang) and Scala in the development of the application.
Write Micro Service using spring Boot and deploy into open shift to query the data from Google spanner/Big query.
Worked on web application called NBA Arena Management using ASP.NET WebAPI, Angular 7 with DocuSign REST API.
Configured bamboo automating the builds into Gradle archiva repository.
Develop API to index/retrieve data from elastic search instance which is in AWS cloud using Jest client API.
Setup Hadoop cluster on Amazon EC2 using whirr for POC.
Scheduled, deployed and managed container replicas onto a node cluster using Kubernetes.
Get the error report and analyze the defects and also categorize the defects using the SPLUNK tool.
Collaborated with data engineers and analysts to ensure effective data flow between applications and OpenSearch.
Implemented Neo4j to integrate graph databases with relational database and to efficiently store, handle and query highly connected elements in your data model especially for object oriented and relational developers.
Developed code and test artifacts that reuse objects and are well structured, backed by automated tests. 
Developed various generic JavaScript functions used for validations. Used SOAP UI for testing web services. 
Developed Microservices & APIs using Spring Boot and Used Apache Kafka cluster as messaging system between the APIs and Microservices. 
﻿﻿Hands on using Azure API management, Resource Groups, Storage accounts and Application registrations for managing API.
Worked with Jasmine in testing and performed Unit testing and execution testing utilizing JUNIT.
Created GraphQL schema and queries to interact with Mongo DB database.
Integrated Node.js with databases like MongoDB and Redis for real-time applications.
The mentioned project is interrelated with various business flows, a bridge using various technologies like Spring MVC using JSP, AJAX, and HTML.
Created Directives, Services, Filters using Angular JS with Typescript.
Participated in the requirements gathering process. Involved in the analysis and design of the project.
Responsible for designing the User Interface by coding in HTML, CSS3, AngularJS, Bootstrap to build this SPA Single Page Application.
Optimized data models and partitioning strategies for high scalability and cost efficiency.
Extensively used various Spring Framework modules like MVC, DI (IOC), Auto Wiring, JDBC Templates, Spring Security, and AOP.
Developed backup and recovery engine for VM backup/recovery using VMware vSphere APIs, GoLang programming language and RabbitMQ Message bus (communication interface).
Worked with MVVM pattern and developed view - models using typescript.
Monitored and optimized web server performance for high-traffic applications.
Developed Single Page applications using AngularJS using components, directives using TypeScript.
Spring MVC Framework IOC (Inversion of Control) design pattern is used to have relationships between application components.
Generated Java APIs for retrieval and analysis on No-SQL database such as HBase and Cassandra and Worked with NoSQL databases like Cassandra and Mongo DB for POC purpose.
Worked on SMTP Server setup for customers using ansible and terraform.
Skilled in creating Azure DevOps projects and building Cl and CD pipelines in Azure DevOps.
Successfully integrated Vue.js front-end applications with Java-based RESTful APIs, ensuring seamless data flow and efficient communication between client and server.
Worked on the Kotlin for building the backend components and API resources, experience working on Json API for creating multiple API’s.
Used Struts Model-View-Controller approach to help design new features and fix Jira bugs and request.
Developed Spark code using Scala and Spark-SQL/Streaming for faster testing and processing of data.
Developed SOAP and RESTful Web services using Spring and Apache CXF frameworks.
Rational Software Architect and Spring Tool Suite are the IDEs used to develop the services.
Created an XML document using STAX XML API to pass the XML structure to Web Services. 
Apache Ant was used for the entire build process.
Utilized Vue.js best practices to optimize application performance, including lazy loading, code splitting, and efficient state management.
Used JMeter to run the load tests and query optimization to improve the response times.
Migrated from Ant to Maven via Ivy for dependency resolution and from ClearCase to GIT.
Used JavaScript for client-side validation.
Wrote roll out procedures and plans. Environment was mixed Windows and AIX, CSS, XML, HTML, Knockout.js, Hbase, NoSql, XSLT, projects written in C.
To convert the Entire event flow as a SOA based architecture used RabbitMQ as a queues and BUS.
Kafka integration with Spark using Spark Streaming API.
Involved in the development of the front end of the application using Python.
Exposed the Web Services to the client applications by sharing the WSDL.
Used Mockito framework for implementing JUnits.
Jenkins is being used as a continuous integration tool.
Monitoring Resources and Applications using AWS Cloud Watch, including creating alarms to monitor metrics such as EBS, EC2, ELB, RDS, S3 instances.
Developed the terraform script to get the certificates from the bucket and create an SSL cert in the respective project on the fly without downloading the certs on the local machine.
Developed the project in Java, different J2EE technologies like JSP, Servlet, NodeJS, React, JavaScript, HTML, and CSS
Used AWS SDK for connection to Amazon S3 buckets as it is used as the object storage service to store and retrieve the media files related to the application.
Developed mock data generation feature that is integrated with GraphiQL IDE interface to allow users to send queries against their custom generated schema and render mock results in order to familiarize users to the GraphQL syntax.
Integrated DynamoDB with AWS Lambda to support serverless application architecture.
Developed API for using AWS Lambda to manage the servers and run the code in AWS.
Worked closely with the QA team and fixed QA bugs as well as production issues with a quick turnaround time.
Worked on Scala for implementing spark machine learning libraries and spark streaming.
Experience with Spring MVC and Spring MVC with REST integration and JSON.
Designed and developed a multi-threading code to fetch data from different sources in parallel.
Environment: Java 8/11, J2EE, Angular 16, Spring Boot, Restful, SOAP, JHipster, RabbitMQ, XML, JSP, MVC, JUnit, Maven, Kubernetes, Jenkins, Sonar, Log4J, Oracle DB, MongoDB, LDAP, JavaScript libraries such as HTML5, PostgreSQL, Scala, Vue JS, CSS3, jQuery, AngularJS, Unix Shell Scripting, DocuSign, Drools, PostgreSQL, IBM WebSphere/Apache Application Servers, Agile.
Client: U.S. Renal Care, Plano, TX					                                May 2019 – Jul 2021                                                                    
Role: Java Developer
Responsibilities:
The project mentioned is interrelated with various business flows, a bridge using various technologies like Spring MVC, Spring Boot using JSP 2.0, AJAX, and HTML.
﻿﻿Used a variety of version control tools like GitHub, SVN, Azure Repos.
Extensively used JSF, JavaScript, JSON, and Ajax for interactivity in the UI layer. UI interactivity was written using JQuery by manipulating DOM and using Ajax calls.
Extensively used/modified JQUERY to perform AJAX calls for creating interactive web pages on JSON response.	
Smoke Test and Acceptance Testing with Selenium in multiple Java platforms.
Strong working knowledge of HIPAA and HL7 messaging.
Designed the Web application layout and forms using HTML, CSS, and JavaScript.
Coded multiple tiers applications – DAO’s, business logic, and UI (interactivity, asynchronous calls, DOM manipulation).
Created service accounts using Terraform with the respective roles to support the services deployed for managing the GCP TechStack.
Worked on tools for handling runtime HL7 formatted data.
Conceived and developed environment to communicate with SOAP services using REST commands/requests using Azure APIM, O.Auth, DevOps, Application Insights and storage accounts.
Converted PSD mockups into pure hand-written HTML and CSS pages.
Analyzed and resolved production system problems tracked with Rational ClearQuest and JIRA.
Experience in Message Oriented Middleware implementation using JMS and Apache Kafka
Wrote Oracle stored procedures, DAO’s, and Spring MVC Controllers.
Implemented AngularJS and Node.JS with Spring MVC as a model component. Developed custom AngularJS directives, tags and integrated with Spring forms.
Experience of the design patterns and best practices with golang (and more) to start with design and get to deployable production systems including scale monitoring and instrumentation platform.
Leveraged GCP services like Cloud Functions, BigQuery, and Pub/Sub for data processing pipelines.
Designed and implemented HL7 interfaces for seamless communication between healthcare systems.
Involved in converting Hive/SQL queries into Spark transformations using Spark RDDs using Scala.
Developed and tested many features for dashboard using Python, Bootstrap, CSS, JavaScript and JQuery.
Have knowledge on partition of Kafka messages and setting up the replication factors in Kafka Cluster.
Created tables and worked on SQL and PL/SQL to write Stored Procedures functions and packages for complex Inserts and updates in the database. 
Strong knowledge on working with GraphQL schema, queries and mutations to interact with Mongo DB and several other data layers.
Migrated existing applications to GCP for improved performance and scalability.
Ensured compliance with healthcare data standards, including HIPAA, while working with sensitive patient data.
Used Ajax, DOJO to communicate with the server to get the asynchronous response and display it for the Username auto-complete feature in the Feedback edit page.
Implemented RESTFUL Web Services. These Web Services are consumed by multiple intranet Freddie applications. 
Using Kafka for Publish/Subscribe pattern in application dealing with messaging.
Involved in fixing Front-End issues with the layouts.
Managed cloud infrastructure with GCP tools, ensuring cost-efficient and secure operations.
Implemented Spark using Scala and utilizing Data frames and Spark SQL API for faster processing of data.
Actively used XML (documents and transformations) for creating templates with dynamic data from the XML file.
Environment: JAVA/J2EE, JSP, Servlet, EJB, Spring, RSA, JUnit, CSS, Python, Toad, Log4j, JDBC, JavaScript, HTML/DHTML, XML, UML, Web service (SOAP, WSDL, Restful), Kotlin, Terraform, jQuery, Amazon RDS, WebSphere 6/7, DB2, Oracle 11g, PL/SQL, Kafka, MS SQL server, Toad, Windows & Unix.  
Client: Merck, North Wales, PA					                    	Sep 2017 – Apr 2019
Role: Java/J2EE Developer
Responsibilities:
Involved in web component design and development using the spring framework, JSP, Servlets, and taglibs.
Worked closely with cross-functional teams as a part of AGILE environment to gather and analyze the requirements of the Application. 
Coded extensively using JavaScript MVC Framework, AngularJS to make rich internet web application for a Single page app (SPA).
Generated Java APIs for retrieval and analysis on No-SQL database such as HBase and Cassandra.
Developing Docker file for different end points and validate them through the docker-central to run the Jenkins job with given parameter and deploy job to the Kubernetes.
Responsible for creating front end applications, user inctive (UI) web pages using web technologies like HTML, CSS, JavaScript, jQuery, AJAX, JSON, XML, Angular 2, Node Js and Bootstrap.
Worked with React.js workflows such as Flux and Redux.
Using Component-based architecture provided by Angular 4 created Typescript reusable components and services to consume REST API's.
Experience in Designing AZURE Resource Manager (ARM) templates and extensive experience in designing custom build steps using PowerShell.
Use AWS Analytics Elastic search index instance to index participant information’s.
Designed and implemented business logic using Spring and Hibernate frameworks. 
Spring framework and DAO classes using JPA framework for persistence management and involved in integrating the frameworks for the project.
Reviewed Design Document gathered information from Architect and Business Analysts to design Functional Specifications for this UI Interface Project.
Designed and developed presentation layers using Servlets, JSP, Java Beans, CSS, HTML, DHTM, jQuery and Java Script.
Involved in complete development of Agile Development Methodology' and tested the application within each iteration.
Implemented JSF in Presentation layer programming using JSTL, AJAX, GWT Development.
Worked with HTML, DHTML, CSS, JAVASCRIPT, JSON in UI pages.
Implemented MongoDB and Oracle as the Back-End storage system of the application.
Implementing RESTful web services architecture for Client-server interaction and implemented respective POJOs for its implementations.  
Using JIRA tool to track the support tickets and resolve issues in a timely manner.
Used SOAP for Web Services by exchanging XML data between applications over HTTP. 
Develop Docker based Microservices deployment modules with Jenkins based frameworks.
Worked with Build Engineer on Maven Configuration to build the application and deployed on WebSphere Application Server.
Involved in writing test cases for unit testing using JUnit and Selenium for UI automation.
Configured Log4j tool to log the entire application. 
Created documentation for all the components which is included in React JS-Bootstrap page.
Used GitHub for code repository and IntelliJ for IDE. 
Involved in migrating on-premises to Azure and built Azure Disaster Recovery Environment and Azure Backups from scratch using PowerShell Scripts.
Configured RDS instances using Cloud formation templates and terraform.
Used Drools Decision table using excel sheets with a Spring MVC project. 
Environment: Java/J2EE, Springs, HTML, CSS, jQuery, Ajax, XML, JSON, Angular, Bootstrap, Angular, Node.JS, React.JS, MongoDB, AWS, GITHUB, Oracle WebLogic, Kotlin, JBOSS, Apache Tomcat, IntelliJ, SOAP, Jenkins, Junit, Selenium, Docker, Microservices, Maven, WebSphere, Log4j.
Client: Payism, Bangalore, India						                         Jun 2013 – Nov 2015 
Role: JAVA/J2EE Developer
Responsibilities:
Worked in all the modules of the application which involved front-end presentation logic developed using Tiles, JSP, JSTL, and JavaScript, Business objects developed using POJOs and data access layer using hibernate framework.
Developed various generic JavaScript functions used for validations.
Developed screens using JSP, JavaScript, AJAX, and Ext JS.
Used AJAX extensively to implement front end /user interface features in the application.
Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.
Exposed the Web Services to the client applications by sharing the WSDL.
Created logical and physical data models putting to practice, concepts of normalization and RDBMS.
Database development required the creation of new tables, PL/SQL stored procedures, functions, views, indexes and constraints, triggers, and required SQL tuning to reduce the response time in the application.
Involved in writing Thread Safe blocks for multithread access to make valid transactions. 
Developed Android applications and Framework using Kotlin and Java.
Used DAO pattern to fetch data from database using Hibernate to conduct various database.
Conducted weekly code walkthrough using UNIX Shell Scripts.
Created session EJBs that retrieved information from the Oracle database using Hibernate.
Created AWS environment for this application. Generate various AWS resources like S3, EC2, ECS, IAM, Route 53, Cognito, Kinesis and others using terraform.
Focused on Test Driven Development; thereby creating detailed JUnit tests for every single piece of functionality before writing the functionality.
Used Ant scripts to build and deploy the applications in JBoss application Server.
Worked closely with the QA team and fixed QA bugs as well as production issues with a quick turnaround time.
Environment: J2EE, Spring Framework, Spring MVC, Servlets 2.3, JDBC, AJAX, Web services, SOAP, XML, Java Beans, Apache POI, JQuery, JavaScript, Oracle 10g, Kotlin, terraform, Agile Methodology, Design Patterns, CVS, Apache Maven, JUnit, HTML Unit, XSLT, HTML/DHTML.
Education:
Bachelors of Technology in Computer science at TKR College of engineering and technology -2013.
Master’s in CS - University of Dayton, USA- 2017
Java/J2EE Technologies | Servlets, JSP, JSTL, JDBC, JMS, JNDI, EJB, AWT, Applets, Multi-threading
Programming Languages | Java (java SE 6/7/8/11/17/21), python, JavaScript, kotlin, SQL, PL/SQL
Application/Web Servers: | Oracle, IBM WebSphere, JBoss, Tomcat
Frameworks | Spring, Spring Boot, Webflux, Hibernate, RESTFul, SOAP JSF, Express
IDEs | Eclipse, Intelij, Visual studio
Web technologies | JSP, jQuery, AJAX, XML, XSLT, HTML, DHTML, ReactJS, CSS
Cloud | Docker, Kubernetes, AWS
Methodologies | Agile, Scrum, RUP, TDD, OOAD, SDLC
Modeling Tools | UML, Rational Rose, lucid charts
Testing technologies/tools | JUnit, JMeter
Database Servers | Oracle 8i/9i/10g, SQL Server, MySQL, MongoDB 
Version Control | CVS, SVN
Build Tools | ANT, Maven